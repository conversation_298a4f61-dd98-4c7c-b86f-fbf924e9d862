# Pull Request Template for Lambda Repositories

## PR Title
Ensure your PR title follows the format:  
`TICKET-NO: Short descriptive title of the changes`

## Description
<!-- Provide a summary of the changes in this PR.
Describe the purpose of the PR, the problem it solves, and any relevant background context. -->

## Jira Ticket
<!-- Add the Jira ticket link associated with this PR. -->
[Jira Ticket Link](https://your-jira-instance/browse/TICKET-NO)

## Affected Lambda(s)
Select the Lambda(s) affected by this PR:
- [ ] `balance-finder-lambda`
- [ ] `banking-aggregator-lambda`
- [ ] `banking-anomaly-detector-lambda`
- [ ] `banking-claim-file-deletion-lambda`
- [ ] `banking-file-fetch-lambda`
- [ ] `banking-file-validator-lambda`
- [ ] `banking-invalidator-lambda`
- [ ] `banking-orchestration`
- [ ] `banking-persistor-lambda`
- [ ] `banking-status-change-lambda`
- [ ] `banking-status-change-orchestration`
- [ ] `banking-status-change-trigger-lambda`
- [ ] `banking-status-updator-lambda`
- [ ] `banking-trigger-lambda`
- [ ] `caledonian-tbr-dropping-lambda`
- [ ] `claim-aggregator-lambda`
- [ ] `claim-anomaly-detector-lambda`
- [ ] `claim-file-fetch-lambda`
- [ ] `claim-file-generator-lambda`
- [ ] `claim-file-validator-lambda`
- [ ] `claim-invalidator-lambda`
- [ ] `claim-orchestration`
- [ ] `claim-persistor-lambda`
- [ ] `claim-status-change-lambda`
- [ ] `claim-status-change-orchestration`
- [ ] `claim-status-change-trigger-lambda`
- [ ] `claim-status-updator-lambda`
- [ ] `claim-trigger-lambda`
- [ ] `cognito-custom-email-lambda`
- [ ] `dashboard`
- [ ] `db-setup-lambda`
- [ ] `gtl-tbr-dropping-lambda`
- [ ] `helpers`
- [ ] `iglu-escrow-scheduler-lambda`
- [ ] `risk-exposure-insight-lambda`
- [ ] `sftp-auth-lambda`
- [ ] `sftp-lambda`
- [ ] `sftp-to-ptt-lambda`
- [ ] `wlh-net-amount-calculator-lambda`

## Changes Made
<!-- Provide a detailed list of changes made in this PR. Include: 
- New features
- Bug fixes
- Refactoring
- Configuration updates -->

## Testing
<!-- Explain how the changes were tested. Provide details on:
- Unit tests added or modified
- Manual testing conducted
- CI/CD pipeline results -->

### Steps to Test
<!-- Describe the steps to verify the changes. -->
1. Step 1:  
2. Step 2:  
3. Step 3:  

## Checklist
- [ ] My PR title follows the format `TICKET-NO: Title`
- [ ] All affected Lambdas are listed above
- [ ] Relevant unit tests are added or updated
- [ ] I have added the Jira ticket link
- [ ] Code has been reviewed by at least one other team member
