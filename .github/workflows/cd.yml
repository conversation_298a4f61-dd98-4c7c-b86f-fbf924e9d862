name: Continuous Deployment

on:
  push:
    branches: [dev, release, main]

env:
  WORKING_DIRECTORY: ${{ fromJson('{dev:"./provisioning/nonprod/dev",release:"./provisioning/nonprod/pre-prod",main:"./provisioning/prod/prod" }')[github.ref_name] }}

jobs:
  terraform_apply:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up python
        id: setup-python-poetry
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      #----------------------------------------------
      #  -----  install & configure poetry  -----
      #----------------------------------------------
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: latest
          virtualenvs-create: true
          virtualenvs-in-project: true
          installer-parallel: true

      - name: Set up python
        id: setup-python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      #----------------------------------------------
      #       load cached venv if cache exists
      #----------------------------------------------
      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v3
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
      #----------------------------------------------
      # install dependencies if cache does not exist
      #----------------------------------------------
      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install --no-interaction --no-root

      - name: AWS Configuration for prod
        if: ${{ github.ref_name == 'main' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.PROD_AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.PROD_SECRET_NAME }}" >> $GITHUB_ENV
          echo "TF_VAR_auth_token=${{ secrets.AUTH_TOKEN }}" >> $GITHUB_ENV
          echo "TF_VAR_redis_password=${{ secrets.PROD_REDIS_PASSWORD }}" >> $GITHUB_ENV

      - name: AWS Configuration for release
        if: ${{ github.ref_name == 'release' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.PRE_PROD_SECRET_NAME }}" >> $GITHUB_ENV
          echo "TF_VAR_auth_token=${{ secrets.AUTH_TOKEN }}" >> $GITHUB_ENV
          echo "TF_VAR_redis_password=${{ secrets.NON_PROD_REDIS_PASSWORD }}" >> $GITHUB_ENV

      - name: AWS Configuration for dev
        if: ${{ github.ref_name == 'dev' }}
        run: |
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> $GITHUB_ENV
          echo "TF_VAR_secret_name=${{ secrets.DEV_SECRET_NAME }}" >> $GITHUB_ENV
          echo "TF_VAR_auth_token=${{ secrets.AUTH_TOKEN }}" >> $GITHUB_ENV
          echo "TF_VAR_redis_password=${{ secrets.NON_PROD_REDIS_PASSWORD }}" >> $GITHUB_ENV

      - name: Install Terraform
        env:
          TERRAFORM_VERSION: "1.1.3"
        run: |
          tf_version=$TERRAFORM_VERSION
          wget https://releases.hashicorp.com/terraform/"$tf_version"/terraform_"$tf_version"_linux_amd64.zip
          unzip terraform_"$tf_version"_linux_amd64.zip
          sudo mv terraform /usr/local/bin/
      - name: Verify Terraform version
        run: terraform --version

      - name: Terraform init
        working-directory: ${{ env.WORKING_DIRECTORY }}
        run: terraform init -input=false

      - name: Give file permission
        run: chmod u+x ./provisioning/common/build_layer_dependencies.sh

      - name: Terraform apply
        working-directory: ${{ env.WORKING_DIRECTORY }}
        run: terraform apply -auto-approve -input=false

  push_tag:
    runs-on: ubuntu-latest
    needs: [terraform_apply]
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: "0"
      - name: Bump version and push tag
        uses: anothrNick/github-tag-action@1.36.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          WITH_V: true
