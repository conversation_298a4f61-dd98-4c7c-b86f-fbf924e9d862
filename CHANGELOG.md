# Changelog
All notable changes to this project will be documented in this file.
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.0.11] - 2025-06-03
### Fixed
## IMP-25-65
- Added Supplier Reference for Sunshine client

## [0.0.10] - 2025-04-28
### Fixed
## IMP-25-60
- Data transform lambda and Bluestlye minor fix

## [0.0.9] - 2025-04-17
### Added
## IMP-25-60
- Data transform lambda
- 
## [0.0.8] - 2025-01-28
### Added
## IMP-25-48
# https://pttayata-221121.atlassian.net/jira/software/projects/IMP25/boards/5?assignee=712020%3Aa476e1d6-afea-4fc4-977b-b503f91921d6&selectedIssue=IMP25-48
- Redis password via github

## [0.0.7] - 2025-01-28
### Added
## IMP-25-48
# https://pttayata-221121.atlassian.net/jira/software/projects/IMP25/boards/5?assignee=712020%3Aa476e1d6-afea-4fc4-977b-b503f91921d6&selectedIssue=IMP25-48
- Blue Style - included conditions

## [0.0.6] - 2025-01-28
### Added
## IMP-25-29
- Mighty hoopla

## [0.0.5] - 2025-04-01
### Added
## Escrow changes for Anglia & Wst
- Changed escrow calculation logic for Anglia & Wst

## [0.0.4] - 2025-02-25
### Removed
## REDIS
- Using redis via ec2 for all env together

## [0.0.3] - 2025-02-25
### Added
## Bluestyle changes
- Adding Redis via terraform

## [0.0.5] - 2025-04-01
### Added
## Escrow changes for Anglia & Wst
- Changed escrow calculation logic for Anglia & Wst

## [0.0.4] - 2025-02-16
### Changes
## IMPL3-344
- Minor changes for Bluestyle / Added redis via variables

## [0.0.3] - 2025-01-28
### Added
## IMPL3-344
- Bluestyle changes

## [0.0.3] - 2025-01-20
### Added
## IMPL3-341 / IMPL3-343
- Bluestyle changes

## [0.0.2] - 2025-01-20
### Added
## IMPL3-335
- added anomaly for delay payment report


## [0.0.1] - 2025-01-21
### Updated
- updated custom_data_modifier_broadway in claim file validator
