# MongoDB to Power BI Data Pipeline

## Overview

This automated pipeline extracts banking and claims data from MongoDB, transforms it into Excel format, and delivers it to Power BI for daily refreshed reports. The solution enables comprehensive financial reporting and analysis without manual intervention.

## Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   MongoDB   │───▶│ AWS Lambda  │───▶│  S3 Bucket  │───▶│Power Automate│───▶│  Power BI   │
│             │    │(Data Export)│    │             │    │or Python    │    │  Reports    │
│ Banking &   │    │             │    │ Excel Files │    │   Script    │    │             │
│ Claims Data │    │ Pandas      │    │             │    │             │    │ Dashboards  │
└─────────────┘    │ Transform   │    └─────────────┘    └─────────────┘    └─────────────┘
                   └─────────────┘           │                    │
                          │                  │                    │
                          │                  ▼                    ▼
                   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
                   │ EventBridge │    │ SharePoint/ │    │ Automated   │
                   │Daily Schedule│    │  OneDrive   │    │  Refresh    │
                   │  6:00 AM    │    │             │    │  8:00 AM    │
                   └─────────────┘    └─────────────┘    └─────────────┘
```

## Components

### 1. MongoDB Data Export Lambda (`powerbi-data-export-lambda`)

**Purpose**: Extracts, transforms, and exports banking/claims data to Excel format.

**Key Features**:
- Fetches data from multiple MongoDB collections
- Performs data cleaning and standardization
- Creates multiple Excel sheets with different views
- Uploads to S3 with automated naming
- Includes data validation and quality checks

**Data Sources**:
- `banking_file_details` - Banking transaction details
- `claims_file_details` - Claims transaction details  
- `trust_fund_v2` - Trust fund balances
- `client_basic_info` - Client information

**Output Sheets**:
- `Banking_Data` - Cleaned banking transactions
- `Claims_Data` - Cleaned claims transactions
- `Client Summary` - Aggregated data by client
- `Currency Summary` - Aggregated data by currency
- `Monthly Summary` - Time-based trends
- `Export_Metadata` - File generation info and validation results

### 2. EventBridge Scheduling

**Purpose**: Triggers daily data export at 6:00 AM UTC.

**Configuration**:
- Schedule: `cron(0 6 * * ? *)` (Daily at 6:00 AM UTC)
- Target: PowerBI Export Lambda
- Payload: `{"days_back": 30, "bucket": "ptt-powerbi-exports-{env}"}`

### 3. S3 Storage

**Purpose**: Intermediate storage for Excel files.

**Bucket Structure**:
```
ptt-powerbi-exports-{env}/
├── powerbi-exports/
│   ├── ptt_banking_claims_export_20231201_060000.xlsx
│   ├── ptt_banking_claims_export_20231202_060000.xlsx
│   └── ...
```

### 4. Power Automate Flow / Python Sync Script

**Purpose**: Syncs latest Excel file from S3 to SharePoint/OneDrive.

**Options**:
- **Power Automate**: GUI-based flow with SharePoint integration
- **Python Script**: Code-based solution using Office365 APIs

**Schedule**: Daily at 7:00 AM UTC (1 hour after data export)

### 5. Power BI Integration

**Purpose**: Consumes Excel data for reporting and visualization.

**Features**:
- Automatic dataset refresh at 8:00 AM UTC
- Multiple data tables for different analysis needs
- Pre-built relationships and calculated measures
- Real-time dashboard updates

## Installation and Setup

### Prerequisites

1. **AWS Account** with Lambda, S3, and EventBridge access
2. **MongoDB** connection with read access to required collections
3. **Microsoft 365** subscription with Power Automate and Power BI
4. **SharePoint Online** or **OneDrive for Business**

### Step 1: Deploy AWS Infrastructure

```bash
# Install dependencies
poetry install

# Deploy Terraform infrastructure
cd provisioning/prod  # or nonprod
terraform init
terraform plan
terraform apply
```

### Step 2: Configure Power Automate or Python Sync

#### Option A: Power Automate Flow
1. Import `powerbi-integration/power-automate-flow.json`
2. Configure SharePoint and AWS connections
3. Set schedule for 7:00 AM UTC

#### Option B: Python Script
1. Deploy `powerbi-integration/s3-to-onedrive-sync.py` as Lambda or cron job
2. Configure environment variables for SharePoint access
3. Schedule execution for 7:00 AM UTC

### Step 3: Set Up Power BI

1. **Connect to Data Source**:
   - Open Power BI Desktop
   - Connect to SharePoint Online List
   - Select the Excel file: `ptt_banking_claims_latest.xlsx`

2. **Load Data Tables**:
   - Import all sheets from Excel file
   - Set up relationships between tables
   - Create calculated columns and measures

3. **Build Visualizations**:
   - Summary cards for key metrics
   - Time series charts for trends
   - Client and currency breakdowns
   - Balance reconciliation reports

4. **Publish and Schedule**:
   - Publish to Power BI Service
   - Configure scheduled refresh for 8:00 AM UTC
   - Set up alerts and notifications

## Data Schema

### Banking Data Schema
```
transaction_id: string          # Unique transaction identifier
client_id: string              # Client identifier
client_name: string            # Client friendly name
booking_reference: string      # Booking reference number
transaction_amount: decimal    # Transaction amount
currency: string               # Currency code (GBP, EUR, USD)
entry_type: string            # Type of entry (deposit, withdrawal)
payment_type: string          # Payment method
lead_passenger: string        # Lead passenger name
passenger_count: integer      # Number of passengers
booking_date: datetime        # Date of booking
departure_date: datetime      # Departure date
return_date: datetime         # Return date
total_booking_value: decimal  # Total value of booking
bonding_type: string          # Bonding type (ATOL, ABTA)
booking_status: string        # Status of booking
file_date: date              # File processing date
created_timestamp: datetime   # Record creation time
updated_timestamp: datetime   # Last update time
current_balance: decimal      # Current balance
total_in_trust: decimal      # Total amount in trust
total_claimed: decimal       # Total claimed amount
transaction_type: string     # Always "Banking"
net_position: decimal        # Calculated net position
days_to_departure: integer   # Days until departure
booking_month: string        # Booking month (YYYY-MM)
file_month: string          # File month (YYYY-MM)
```

### Claims Data Schema
```
transaction_id: string          # Unique transaction identifier
client_id: string              # Client identifier
client_name: string            # Client friendly name
booking_reference: string      # Booking reference number
claim_amount: decimal          # Claim amount
currency: string               # Currency code
claim_type: string            # Type of claim
lead_passenger: string        # Lead passenger name
passenger_count: integer      # Number of passengers
booking_date: datetime        # Date of booking
departure_date: datetime      # Departure date
return_date: datetime         # Return date
total_booking_value: decimal  # Total value of booking
bonding_type: string          # Bonding type
booking_status: string        # Status of booking
file_date: date              # File processing date
created_timestamp: datetime   # Record creation time
updated_timestamp: datetime   # Last update time
current_balance: decimal      # Current balance
total_in_trust: decimal      # Total amount in trust
total_claimed: decimal       # Total claimed amount
transaction_type: string     # Always "Claims"
claim_percentage: decimal    # Claim as % of booking value
days_since_departure: integer # Days since departure
booking_month: string        # Booking month (YYYY-MM)
file_month: string          # File month (YYYY-MM)
```

## Configuration

### Environment Variables

**Lambda Function**:
```
ENVIRONMENT=prod
POWERBI_EXPORT_BUCKET=ptt-powerbi-exports-prod
DATABASE_URI=mongodb://...
```

**Power Automate Flow**:
```
aws_access_key=AKIA...
aws_secret_key=***
s3_bucket_name=ptt-powerbi-exports-prod
sharepoint_site_url=https://company.sharepoint.com/sites/powerbi
sharepoint_library=PowerBI Data
```

**Python Sync Script**:
```
S3_BUCKET=ptt-powerbi-exports-prod
SHAREPOINT_SITE_URL=https://company.sharepoint.com/sites/powerbi
SHAREPOINT_USERNAME=<EMAIL>
SHAREPOINT_PASSWORD=***
DOCUMENT_LIBRARY=PowerBI Data
```

### Customization Options

**Data Range**: Modify `days_back` parameter to change data extraction period
**Scheduling**: Adjust cron expressions for different timing
**Data Filters**: Add client or date filters in MongoDB aggregation pipelines
**Excel Formatting**: Customize sheet names and column formatting
**Validation Rules**: Add custom data quality checks

## Monitoring and Troubleshooting

### CloudWatch Logs
```bash
# View Lambda execution logs
aws logs filter-log-events \
  --log-group-name "/aws/lambda/powerbi-data-export-lambda-prod" \
  --start-time $(date -d "1 hour ago" +%s)000
```

### Common Issues

1. **Lambda Timeout**: Increase timeout (currently 15 minutes) or memory (currently 2048 MB)
2. **MongoDB Connection**: Check VPC configuration and security groups
3. **S3 Permissions**: Verify IAM roles and bucket policies
4. **SharePoint Authentication**: Check Power Automate connections
5. **Power BI Refresh**: Verify data source credentials and refresh schedule

### Data Quality Monitoring

The pipeline includes built-in data validation:
- Record count verification
- Missing required fields detection
- Data type validation
- Business rule checks (negative amounts, future dates)
- Results stored in `Export_Metadata` sheet

## Performance Optimization

### Lambda Optimization
- Memory: 2048 MB (optimized for pandas operations)
- Timeout: 15 minutes (handles large datasets)
- VPC: Configured for MongoDB access

### Data Processing
- Batch processing for large datasets
- Efficient MongoDB aggregation pipelines
- Pandas optimization for memory usage
- Excel streaming for large files

### Cost Management
- S3 lifecycle policies for old exports
- Lambda memory optimization
- Power BI shared capacity usage
- Power Automate execution limits

## Security Considerations

1. **AWS IAM**: Least privilege access for Lambda execution role
2. **MongoDB**: Network security groups and authentication
3. **SharePoint**: Limited library access permissions
4. **Credentials**: Use AWS Secrets Manager for sensitive data
5. **Data Encryption**: S3 encryption at rest and in transit

## Support and Maintenance

### Regular Tasks
- Monitor Lambda execution success rates
- Review data quality validation results
- Update dependencies and security patches
- Optimize performance based on usage patterns

### Troubleshooting Resources
- CloudWatch logs for Lambda execution details
- Power Automate run history for sync issues
- Power BI refresh history for dataset problems
- S3 access logs for file transfer verification

For additional support, refer to the detailed setup guide in `powerbi-integration/setup-guide.md`.
