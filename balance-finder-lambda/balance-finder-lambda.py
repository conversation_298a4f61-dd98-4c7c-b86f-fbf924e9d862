import os
from pymongo import MongoClient, ReadPreference, ReturnDocument, read_concern
from datetime import datetime, timedelta
from helpers.secret_manager_handler import get_secret

client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def modify_balance(item, date, date_yesterday, session):
    balance = db.opening_closing_balance.find_one(
        {"client_id": item["client_id"], "currency": item["currency"], "date": date}, session=session
    )
    opening_balance = balance["opening_balance"] if balance else 0
    closing_balance = item["amount"]
    while True:
        balance = db.opening_closing_balance.find_one(
            {"client_id": item["client_id"], "currency": item["currency"], "date": date}, session=session
        )
        if balance:
            balance = db.opening_closing_balance.find_one_and_update(
                {"client_id": item["client_id"], "currency": item["currency"], "date": date},
                {
                    "$inc": {"closing_balance": item["amount"]},
                    "$set": {"updated_at": datetime.utcnow(), "opening_balance": opening_balance},
                },
                return_document=ReturnDocument.AFTER,
                session=session,
            )
            closing_balance = balance["closing_balance"]
        else:
            db.opening_closing_balance.insert_one(
                {
                    "client_id": item["client_id"],
                    "currency": item["currency"],
                    "opening_balance": opening_balance,
                    "closing_balance": closing_balance,
                    "updated_at": datetime.utcnow(),
                    "created_at": datetime.utcnow(),
                    "date": date,
                },
                session=session,
            )

        if date >= date_yesterday:
            break
        opening_balance = closing_balance
        date = date + timedelta(days=1)


def lambda_handler(event, context):
    date = (datetime.today()) - timedelta(days=1)
    date_yesterday = datetime.combine(date, datetime.min.time())
    date_yesterday_max = datetime.combine(date, datetime.max.time())
    date_today = datetime.combine(datetime.today(), datetime.min.time())

    banking = db.banking_metadata.aggregate(
        [
            {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
            {"$addFields": {"file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}}},
            {
                "$match": {
                    "created_at": {
                        "$gte": date_yesterday,
                        "$lte": date_yesterday_max,
                    },
                    "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                },
            },
            {"$project": {"client_id": 1, "deposit": "$recent_file.deposit", "file_date": "$file_datetime"}},
        ]
    )
    claims = db.claims_metadata.aggregate(
        [
            {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
            {"$addFields": {"file_datetime": {"$dateFromString": {"dateString": "$recent_file.file_date"}}}},
            {
                "$match": {
                    "created_at": {
                        "$gte": date_yesterday,
                        "$lte": date_yesterday_max,
                    },
                    "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                },
            },
            {"$project": {"client_id": 1, "claim_total": "$recent_file.claim_total", "file_date": "$file_datetime"}},
        ]
    )

    amount_dict = {}
    with client.start_session() as session:
        with session.start_transaction(
            read_preference=ReadPreference.PRIMARY, read_concern=read_concern.ReadConcern(level="snapshot")
        ):
            for item in banking:
                for currency, amount in item["deposit"].items():
                    if f"{item['client_id']}_{currency}_{item['file_date']}" in amount_dict.keys():
                        amount_dict[f"{item['client_id']}_{currency}_{item['file_date']}"]["amount"] += amount
                    else:
                        amount_dict.update(
                            {
                                f"{item['client_id']}_{currency}_{item['file_date']}": {
                                    "amount": amount,
                                    "client_id": item["client_id"],
                                    "currency": currency,
                                    "date": item["file_date"],
                                }
                            }
                        )

            for item in claims:
                for currency, amount in item["claim_total"].items():
                    if f"{item['client_id']}_{currency}_{item['file_date']}" in amount_dict.keys():
                        amount_dict[f"{item['client_id']}_{currency}_{item['file_date']}"]["amount"] -= amount
                    else:
                        amount_dict.update(
                            {
                                f"{item['client_id']}_{currency}_{item['file_date']}": {
                                    "amount": -1 * amount,
                                    "client_id": item["client_id"],
                                    "currency": currency,
                                    "date": item["file_date"],
                                }
                            },
                        )
            for item in amount_dict.values():
                modify_balance(item, item["date"], date_yesterday, session)

            opening_closing_balance_changes = list(db.opening_closing_balance_changes.find({"date": date_yesterday}))
            for item in opening_closing_balance_changes:
                modify_balance(item, datetime.strptime(item["file_date"], "%Y-%m-%d"), date_yesterday, session)

            opening_closing_balance_yesterday = list(
                db.opening_closing_balance.find(
                    {"date": date_yesterday},
                    projection={"created_at": 0, "updated_at": 0, "_id": 0, "date": 0},
                    session=session,
                )
            )
            for item in opening_closing_balance_yesterday:

                opening_closing_balance_today = db.opening_closing_balance.find_one_and_update(
                    {"client_id": item["client_id"], "currency": item["currency"], "date": date_today},
                    {
                        "$inc": {"closing_balance": item["closing_balance"]},
                        "$set": {"updated_at": datetime.utcnow(), "opening_balance": item["closing_balance"]},
                    },
                    session=session,
                )
                if not opening_closing_balance_today:
                    db.opening_closing_balance.insert_one(
                        {
                            **item,
                            "date": date_today,
                            "opening_balance": item["closing_balance"],
                            "closing_balance": item["closing_balance"],
                            "updated_at": datetime.utcnow(),
                            "created_at": datetime.utcnow(),
                        },
                        session=session,
                    )
