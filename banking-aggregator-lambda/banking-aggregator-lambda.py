from datetime import datetime
import json
from math import ceil
import os
from pymongo import MongoClient
from helpers.secret_manager_handler import get_secret
import boto3
from bson import ObjectId
import redis

# Redis Configuration (Use the same EC2 Redis instance)
REDIS_HOST = os.environ.get("REDIS_URL")
REDIS_PORT = 6379
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD")

# Connect to Redis
try:
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        decode_responses=True
    )
    redis_client.ping()
    print("Connected to Redis successfully.")
except redis.ConnectionError as e:
    print(f"Redis connection failed: {str(e)}")
    redis_client = None


STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    client_id = event["clientId"]
    file_id = event["fileId"]
    bucket = event["bucket"]
    data_key = event["dataKey"]

    s3 = boto3.client("s3")
    data_object = s3.get_object(Bucket=bucket, Key=data_key)
    data_items = json.loads(data_object["Body"].read().decode("utf-8"))

    item_count = {}
    deposit = {}
    # Clear Redis cache after processing all transactions
    redis_client.flushdb()
    print("Redis cache cleared successfully.")


    for data in data_items:
        for currency, count in data["itemCount"].items():
            if currency not in item_count:
                item_count[currency] = count
                deposit[currency] = data["deposit"][currency]
            else:
                item_count[currency] += count
                deposit[currency] += data["deposit"][currency]

    db.banking_metadata.update_one(
        {"client_id": ObjectId(client_id), "banking_files": {"$elemMatch": {"file_id": file_id}}},
        {
            "$set": {
                "banking_files.$.item_count": item_count,
                "banking_files.$.deposit": deposit,
                "updated_at": datetime.utcnow(),
            }
        },
    )

    banking_transactions = db.banking_file_details.find({"file_id": file_id}, projection={"_id": 1})
    transaction_ids = [str(transaction["_id"]) for transaction in banking_transactions]

    output = {
        "clientId": client_id,
        "fileId": file_id,
        "sftp": event["sftp"],
        "sftpKey": event["sftpKey"],
        "transactionIds": transaction_ids,
    }
    data_key = f"banking/aggregator/{file_id}.json"
    s3.put_object(Body=json.dumps(output), Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=data_key)
    response = [{"batchNo": i, "dataKey": data_key} for i in range(ceil(len(transaction_ids) / BATCH_SIZE))]
    return response
