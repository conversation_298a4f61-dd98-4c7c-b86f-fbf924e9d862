import json
import os
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId
from helpers.secret_manager_handler import get_secret
import boto3
from helpers import round

STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def add_banking_anomaly(transaction, file_id, anomaly_type, anomaly_id):
    banking_anomaly = {
        "booking_ref": transaction["booking_ref"],
        "transaction_id": transaction["_id"],
        "client_id": transaction["client_id"],
        "banking_id": transaction["banking_id"],
        "file_id": file_id,
        "anomaly_type": anomaly_type,
        "anomaly_id": anomaly_id,
        "status": "Unresolved",
        "deleted": False,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }
    return banking_anomaly


def negative_funds_trust(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    if round(trust["balance"], 2) < 0:
        return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
    else:
        db.anomaly_banking.update_many(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"],
                "anomaly_type": anomaly_type,
                "status": "Unresolved",
                "deleted": False,
            },
            {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )
        db.anomaly_claims.update_many(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"],
                "anomaly_type": anomaly_type,
                "status": "Unresolved",
                "deleted": False,
            },
            {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )


def agent_balance(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    if (
        transaction.get("customer_type", "").casefold() == "Agent".casefold()
        and transaction.get("total_booking_value")
        and transaction.get("departure_date")
    ):
        banking_metadata = db.banking_metadata.find_one({"_id": transaction["banking_id"]})
        file_date = banking_metadata["banking_files"][0]["file_date"]
        departure_date = transaction["departure_date"]
        d1 = datetime.strptime(departure_date, "%Y-%m-%d")
        d2 = datetime.strptime(file_date, "%Y-%m-%d")
        diff = d1 - d2
        if diff.days <= 30 and transaction["total_booking_value"] - trust["balance"] != 0:
            return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def movement_of_funds(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    old_booking_ref = transaction.get("transfer_of_funds")
    collection_name = "trust_fund" if str(transaction["client_id"]) == os.environ.get("NAS") else "trust_fund_v2"
    if old_booking_ref and old_booking_ref != "None":
        old_trust = db[collection_name].find_one(
            {"client_id": transaction["client_id"], "booking_ref": old_booking_ref},
            collation={"locale": "en", "strength": 1},
        )
        if not old_trust or trust["balance"] != old_trust["balance"] + transaction["amount"]:
            return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def banking_supplier_list(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    if transaction.get("supplier_names"):

        insurance_list = list(
            db.client_insurance_info.find(
                {"client_id": transaction["client_id"]}, projection={"supplier_list_file": 1, "_id": 0}
            )
        )
        for insurance in insurance_list:
            if insurance.get("supplier_list_file"):
                list_file_id = []
                for item in insurance_list:
                    for key, value in item.items():
                        list_file_id.append(value["file_id"])
                supplier_list = list(db.client_files.find({"file_id": {"$in": list_file_id}}))
                supplier_names = []
                for file in supplier_list:
                    supplier_names.extend(file["supplier_list"])
                supplier_names = [supplier_name.casefold().replace(" ", "") for supplier_name in supplier_names]
                transaction["supplier_names"] = "".join(transaction["supplier_names"].split())
                transaction_supplier_names = transaction["supplier_names"].split(",")
                transaction_supplier_names = [
                    transaction_supplier_name.casefold() for transaction_supplier_name in transaction_supplier_names
                ]
                check = all(item in supplier_names for item in transaction_supplier_names)
                if check is False and insurance_list:
                    return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def claim_exceed_cost(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    if client_anomaly.get("bank_guarantee_value"):
        cursor = db.banking_file_details.aggregate(
            [
                {
                    "$match": {
                        "client_id": transaction["client_id"],
                        "file_id": file_id,
                        "deleted": False,
                    }
                },
                {"$group": {"_id": None, "total_amount": {"$sum": "$amount"}}},
            ],
            collation={"locale": "en", "strength": 1},
        )
        data = next(cursor, None)
        total_amount = data.get("total_amount") if data else 0
        if total_amount >= client_anomaly.get("bank_guarantee_value") * client_anomaly["custom_field_value"] / 100:
            return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
        else:
            db.anomaly_banking.insert_one({"client_id": transaction["client_id"], "file_id": file_id})


def trust_bookings_check(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    if (
        transaction.get("trust_type") == "Trust"
        and transaction.get("return_date")
        and transaction.get("departure_date")
    ):
        if (
            datetime.strptime(transaction["return_date"], "%Y-%m-%d")
            - datetime.strptime(transaction["departure_date"], "%Y-%m-%d")
        ).days < client_anomaly["custom_field_value"]:
            return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def non_trust_bookings_check(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    if (
        transaction.get("trust_type") == "Non-Trust"
        and transaction.get("return_date")
        and transaction.get("departure_date")
    ):
        if (
            datetime.strptime(transaction["return_date"], "%Y-%m-%d")
            - datetime.strptime(transaction["departure_date"], "%Y-%m-%d")
        ).days > client_anomaly["custom_field_value"]:
            return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
        else:
            data_list = list(
                db.banking_file_details.find(
                    {
                        "client_id": transaction["client_id"],
                        "deleted": False,
                        "trust_type": "Non-Trust",
                        "booking_ref": transaction["booking_ref"],
                    },
                    projection={"_id": 0, "element": 1},
                )
            )
            if data_list and len(data_list) > 1:
                return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def bookings_before_live(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    if transaction.get("booking_date"):
        if transaction["booking_date"] < "2024-03-18":
            return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def bookings_before_live_bluestyle(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    go_live_date = "2024-07-01"
    if transaction.get("payment_date"):
        if transaction["payment_date"] < go_live_date:
            return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def calculate_date_difference_in_days(payment_date, departure_date):
    return (datetime.strptime(departure_date, "%Y-%m-%d") - datetime.strptime(payment_date, "%Y-%m-%d")).days


def bluestyle_bond_proportion_anomaly(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    payment_date = transaction.get("payment_date")
    departure_date = transaction.get("departure_date")

    if not payment_date or not departure_date:
        return False

    date_diff = calculate_date_difference_in_days(payment_date, departure_date)

    if date_diff < 1 and transaction.get("bond_proportion") > 0:
        return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def payment_report_delay_anomaly(transaction, trust, file_id, client_anomaly, anomaly_type, session=None):
    payment_date = transaction.get("payment_date")
    file_id = transaction.get("file_id")
    file = db.banking_metadata.find_one({"banking_files.file_id": file_id})
    submitted_date = None
    if file:
        banking_file = file.get("banking_files", [])[0]
        submitted_date = banking_file.get("submitted_date") if banking_file else None
    if submitted_date:
        submitted_date = str(submitted_date.date())

    if not payment_date or not submitted_date:
        return False
    date_diff = calculate_date_difference_in_days(payment_date, submitted_date)

    if date_diff > 6:
        return add_banking_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def lambda_handler(event, context):
    anomaly_type_dict = {
        "Negative Funds in Trust": negative_funds_trust,
        "Agent Balance": agent_balance,
        "Movement of funds between bookings": movement_of_funds,
        "Banking SupplierList Anomaly": banking_supplier_list,
    }
    data_key, batch_no = (event.get("dataKey"), event.get("batchNo"))
    if not event.get("transactions"):
        s3 = boto3.client("s3")
        data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=data_key)
        data = json.loads(data_object["Body"].read().decode("utf-8"))
        client_id = data["clientId"]
        file_id = data["fileId"]
        sftp = data["sftp"]
        sftp_key = data["sftpKey"]
        transactions = data["transactionIds"][batch_no * BATCH_SIZE : batch_no * BATCH_SIZE + BATCH_SIZE]
    else:
        transactions = event["transactions"]

    transaction_ids = [ObjectId(transaction) for transaction in transactions]
    banking_file_details = list(db.banking_file_details.find({"_id": {"$in": transaction_ids}}))
    if client_id == os.environ.get("LIVESCORE"):
        livescore_anomaly_flag = list(db.anomaly_banking.find({"client_id": ObjectId(client_id), "file_id": file_id}))
        if livescore_anomaly_flag:
            anomaly_flag = True
        else:
            anomaly_flag = None
            anomaly_type_dict.update({"Claim Exceeds Cost %": claim_exceed_cost})

    if client_id == os.environ.get("BLUESTYLE"):
        anomaly_type_dict.update(
            {
                "Blue Style Bond Proportion Anomaly": bluestyle_bond_proportion_anomaly,
                "Bookings Before Live - Bluestyle": bookings_before_live_bluestyle,
                "Payment Report Delay Anomaly": payment_report_delay_anomaly,
            }
        )

    if client_id == os.environ.get("PENNYWOOD"):
        anomaly_type_dict.update(
            {
                "Trust Bookings Check": trust_bookings_check,
                "Non-Trust Bookings Check": non_trust_bookings_check,
                "Bookings Before Live": bookings_before_live,
            }
        )

    client_banking_anomalies = list(
        db.client_anomaly.aggregate(
            [
                {
                    "$lookup": {
                        "from": "lookup_anomaly",
                        "localField": "anomaly_id",
                        "foreignField": "_id",
                        "as": "lookup_anomaly",
                    }
                },
                {"$unwind": "$lookup_anomaly"},
                {
                    "$match": {
                        "client_id": ObjectId(client_id),
                        "lookup_anomaly.name": {"$in": list(anomaly_type_dict.keys())},
                    }
                },
                {
                    "$project": {
                        "_id": "$lookup_anomaly._id",
                        "name": "$lookup_anomaly.name",
                        "custom_field_value": 1,
                        "elements": 1,
                        "bank_guarantee_value": 1,
                    }
                },
            ]
        )
    )

    booking_refs = [transaction["booking_ref"] for transaction in banking_file_details]
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    trust_funds = list(
        db[collection_name].find(
            {"client_id": ObjectId(client_id), "booking_ref": {"$in": booking_refs}},
            collation={"locale": "en", "strength": 1},
        )
    )
    trust_fund_mapper = {trust_fund["booking_ref"].casefold(): trust_fund for trust_fund in trust_funds}

    banking_anomalies = []
    for transaction in banking_file_details:
        booking_ref = transaction["booking_ref"].casefold()
        if booking_ref not in trust_fund_mapper:
            # Log the missing booking reference and continue with the next transaction
            print(f"Warning: Booking reference '{booking_ref}' not found in trust fund collection. Skipping anomaly detection for this transaction.")
            continue
            
        trust = trust_fund_mapper[booking_ref]
        for anomaly in client_banking_anomalies:
            anomaly_function = anomaly_type_dict[anomaly["name"]]
            if anomaly_function == claim_exceed_cost and anomaly_flag is not None:
                continue
            else:
                anomaly_info = anomaly_function(transaction, trust, file_id, anomaly, anomaly["name"])
            if anomaly_function == claim_exceed_cost:
                anomaly_flag = True
            if anomaly_info:
                banking_anomalies.append(anomaly_info)
    if banking_anomalies:
        db.anomaly_banking.insert_many(banking_anomalies)
    return {"clientId": client_id, "fileId": file_id, "sftp": sftp, "sftpKey": sftp_key}
