import os
import boto3

from pymongo import MongoClient
from datetime import datetime

from helpers.secret_manager_handler import get_secret
import logging

logger = logging.getLogger(__name__)

client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    today = datetime.combine(datetime.today(), datetime.min.time())
    latest_records = list(
        db.banking_and_claim_summary.aggregate(
            [
                {
                    "$sort": {
                        "submitted_date": -1,
                    }
                },
                {"$match": {"submitted_date": {"$lt": today}, "delete": False}},
                {
                    "$group": {
                        "_id": {
                            "client_id": "$client_id",
                            "fileName": "$file_name",
                        },
                        "latest_record": {"$first": "$$ROOT"},
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "latest_files": {"$addToSet": "$latest_record._id"},
                    }
                },
            ]
        )
    )

    for item in latest_records:
        with db.client.start_session() as session:
            with session.start_transaction():
                db.banking_and_claim_summary.update_many(
                    {"_id": {"$nin": item["latest_files"]}, "submitted_date": {"$lte": today}},
                    {"$set": {"delete": True}},
                    session=session,
                )

    docs_to_delete = list(
        db.banking_and_claim_summary.find({"delete": True, "_id": {"$nin": latest_records[0]["latest_files"]}})
    )
    objects_to_delete = [doc.get("file_id") for doc in docs_to_delete]

    try:
        BANKING_AND_CLAIM_SUMMARY_BUCKET = os.environ["BANKING_AND_CLAIM_SUMMARY_BUCKET"]
        s3 = boto3.client("s3")
        objects_to_delete_params = [{"Key": obj} for obj in objects_to_delete]
        response = s3.delete_objects(
            Bucket=BANKING_AND_CLAIM_SUMMARY_BUCKET, Delete={"Objects": objects_to_delete_params}
        )
        if "Errors" in response:
            for error in response["Errors"]:
                logger.info(f"Failed to delete {error['Key']} - Error: {error['Message']}")
        logger.info("Deleted objects from S3")
    except Exception as e:
        logger.info(f"Error deleting objects from S3 - Error: {str(e)}")
