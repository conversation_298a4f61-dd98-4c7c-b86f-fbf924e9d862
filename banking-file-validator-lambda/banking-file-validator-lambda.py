import csv
import json
import logging
import os
from typing import Any
from sheet2dict import Worksheet
import boto3
from pymongo import MongoClient
from datetime import datetime
import calendar
from helpers.secret_manager_handler import get_secret
from math import ceil
from bson import ObjectId
import tempfile
from collections import defaultdict
import redis

# import re

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

TEMP_DIR = tempfile.gettempdir()
STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()
BLUESTYLE = os.environ.get("BLUESTYLE")


# Redis Configuration (Use the same EC2 Redis instance)
REDIS_HOST = os.environ.get("REDIS_URL")
REDIS_PORT = 6379
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD")

# Connect to Redis
try:
    redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD, decode_responses=True)
    redis_client.ping()
    logger.info("Connected to Redis successfully.")
except redis.ConnectionError as e:
    logger.error(f"Redis connection failed: {str(e)}")
    redis_client = None


def custom_filter(value: str) -> str:
    data = value.casefold()
    data = "".join(data.split())
    return data


def try_parsing_date(value: str, data_type: str, file_id: str, key: str, row_count, client_id) -> str:
    for date in (
        "%Y-%m-%d",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y/%m/%d",
        "%d-%m-%Y",
        "%d-%m-%Y %H:%M:%S",
        "%d-%m-%Y %H:%M:%S.%f",
        "%Y/%m/%d %H:%M:%S %p",
        "%m/%d/%Y %H:%M:%S %p",
        "%m/%d/%Y" if client_id == os.environ.get("GTL") else "%d/%m/%Y",
    ):
        try:
            return datetime.strptime(value, date).strftime("%Y-%m-%d")
        except ValueError:
            pass
    cancel_banking_file(
        file_id,
        f"The uploaded banking file contains an error in '{key}' column for the given value '{value}' at row {row_count}. Expecting a valid {data_type}.",
    )
    raise ValueError(
        f"The uploaded banking file contains an error in '{key}' column for the given value '{value}' at row {row_count}. Expecting a valid {data_type}."
    )


def validate_type(value: str, data_type: str, file_id: str, key: str, row_count, client_id) -> Any:
    if key == "bookingstatus":
        return value
    elif data_type == "string" and value is not None and isinstance(value, str):
        return value
    elif data_type == "number" and value is not None:
        try:
            return float(value)
        except ValueError:
            logging.error("Invalid value, expected a number.")
            pass
    elif data_type == "alphanumeric" and value is not None and value.replace(" ", "").isalnum():
        return value
    elif data_type == "date" and value is not None:
        return try_parsing_date(value, data_type, file_id, key, row_count, client_id)
    elif (
        data_type == "month"
        and value is not None
        and value.casefold() in map(lambda x: x.casefold(), calendar.month_name[1:])
    ):
        return value
    elif (
        data_type == "boolean"
        and value is not None
        and (value.lower() == "yes" or value.lower() == "true" or value.lower() == "y")
    ):
        return True
    elif (
        data_type == "boolean"
        and value is not None
        and (value.lower() == "no" or value.lower() == "false" or value.lower() == "n")
    ):
        return False

    cancel_banking_file(
        file_id,
        f"The uploaded banking file contains an error in '{key}' column for the given value '{value}' at row {row_count}. expecting a valid {data_type}",
    )
    raise Exception(
        f"The uploaded banking file contains an error in '{key}' column for the given value '{value}' at row {row_count}. expecting a valid {data_type}"
    )


def cancel_banking_file(file_id, error_msg):
    banking_metadata = db.banking_metadata.find_one({"banking_files": {"$elemMatch": {"file_id": file_id}}})
    banking_files = banking_metadata["banking_files"]
    banking_files[-1] = {**banking_files[-1], "status": "Cancelled by System", "notes": error_msg}
    db.banking_metadata.update_one(
        {"banking_files": {"$elemMatch": {"file_id": file_id}}},
        {"$set": {"status": "Cancelled by System", "banking_files": banking_files}},
    )


def currency_validation(currency_list: list, value: str, file_id: str) -> str:
    if value not in currency_list:
        cancel_banking_file(
            file_id,
            "invalid currency",
        )
        raise Exception("Invalid currency format")
    return value


def is_zero(value):
    return value in ("None", "", None) or float(value) == 0


def custom_data_modifier_pennywood(data, initial_row_count, client_id):
    element_dict = {}
    data_list = []
    lookup_banking_elements = list(db.lookup_banking_elements.find())
    banking_elements = [item["name"] for item in lookup_banking_elements]

    basic_info = db.client_basic_info.find_one({"_id": ObjectId(client_id)})
    trust_account = db.lookup_trust_type.find_one({"_id": basic_info["type_of_trust_account"]})
    trust_type = trust_account["name"] if trust_account else None
    new_basic_info = list(
        db.client_basic_info.find({"created_at": {"$gt": datetime(2025, 1, 1)}}, projection={"_id": 1})
    )
    new_basic_info_list = []
    for item in new_basic_info:
        new_basic_info_list.append(item.get("_id"))
    filtered_banking_elements = [elem for elem in banking_elements if elem in ["Deposit", "Balance"]]

    for row in data:
        element_lis = []
        initial_row_count += 1
        if client_id == os.environ["WLH_NEW"]:
            if float(row["Flight"]) == 0 and float(row["FlightCorpCard"]) == 0 and float(row["Insurance"]) == 0:
                element_dict["element"] = "Performance"
                element_dict["Amount"] = row["Funds Collected"]
                element_dict["initial_row_count"] = initial_row_count
                element_lis.append(element_dict.copy())
        if client_id == os.environ["ESKY"] or client_id == os.environ["GVH"]:
            if all(
                is_zero(row[col])
                for col in [
                    "Flight",
                    "Flight BSP",
                    "Flight Corp Card",
                    "Insurance",
                    "Cruise",
                    "Coach",
                    "Commission",
                    "Accommodation",
                    "Performance",
                    "Car hire",
                    "Tours",
                    "Deposit",
                    "Balance",
                    "Non-trust",
                    "Transfers",
                ]
            ):
                element_dict["element"] = ""
                element_dict["Amount"] = row["Funds Collected"]
                element_dict["initial_row_count"] = initial_row_count
                element_lis.append(element_dict.copy())
        if client_id not in (os.environ["WLH_NEW"], os.environ["ESKY"], os.environ["BLUESTYLE"], os.environ["GVH"]):
            if all(
                is_zero(row[col])
                for col in [
                    "Flight",
                    "Flight BSP",
                    "Flight Corp Card",
                    "Insurance",
                    "Cruise",
                    "Coach",
                    "Commission",
                    "Accommodation",
                    "Performance",
                    "Car hire",
                    "Tours",
                    "Deposit",
                    "Balance",
                    "Non-trust",
                    "Transfer",
                ]
            ):
                element_dict["element"] = ""
                element_dict["Amount"] = row["Funds Collected"]
                element_dict["initial_row_count"] = initial_row_count
                element_lis.append(element_dict.copy())
        for key, value in row.items():
            # Winter green check
            if row[key] in ["(No Value)", "(No value)"]:
                row[key] = 0
            logging.info("row[key] = %s" % row[key])
            if trust_type == "ATOL Escrow" and ObjectId(client_id) in new_basic_info_list:
                if key in filtered_banking_elements and row[key] not in ["None", ""] and float(row[key]) != 0:
                    element_dict["element"] = key

                    def safe_float(value):
                        try:
                            return float(value) if value not in [None, "None", ""] else 0.0
                        except ValueError:
                            return 0.0

                    deposit = safe_float(row.get("Deposit", 0))
                    balance = safe_float(row.get("Balance", 0))

                    if deposit == 0 and balance == 0:
                        element_dict["Amount"] = row["Funds Collected"]
                        print("there is no balence or deposit")
                    elif deposit != 0 and balance == 0:
                        element_dict["Amount"] = row["Deposit"]
                        print("there is no balence so deposit will be amount")
                    elif balance != 0 and deposit == 0:
                        element_dict["Amount"] = row["Balance"]
                        print("there is no deposit so balence will be amount")
                    else:
                        sum_of_depobal = deposit + balance
                        element_dict["Amount"] = str(sum_of_depobal)
                        print("both are there and the sum have to be calculated")

                    element_dict["initial_row_count"] = initial_row_count
                    element_lis.append(element_dict.copy())
            else:
                if key in banking_elements and row[key] not in ["None", ""] and float(row[key]) != 0:
                    element_dict["element"] = key
                    element_dict["Amount"] = value
                    element_dict["initial_row_count"] = initial_row_count
                    element_lis.append(element_dict.copy())
        for item in lookup_banking_elements:
            row.pop(item["name"], None)
        row_list = []
        for item in element_lis:
            row.update(item)
            row_list.append(row.copy())
        data_list = data_list + row_list

    return data_list


def manipulate_data_items(data_items, batch_size=1000):
    """
    Groups data_items by BookingRef and batches them ensuring all occurrences of a BookingRef stay in the same batch.
    """
    # Step 1: Group data by BookingRef
    grouped_data = defaultdict(list)
    for item in data_items:
        grouped_data[item["BookingRef"]].append(item)

    # Step 2: Create batches ensuring each BookingRef group stays in the same batch
    batches = []
    current_batch = []
    current_batch_size = 0

    for booking_ref, records in grouped_data.items():
        if current_batch_size + len(records) > batch_size:
            # Start a new batch if adding this BookingRef exceeds batch_size
            batches.append(current_batch)
            current_batch = []
            current_batch_size = 0

        # Add the group to the current batch
        current_batch.extend(records)
        current_batch_size += len(records)

    # Add the last batch if not empty
    if current_batch:
        batches.append(current_batch)

    # Flatten the batches back into a list
    manipulated_data_items = [record for batch in batches for record in batch]

    return manipulated_data_items


def store_in_redis(normalized_data):
    """
    Store transactions in Redis using Hash (HSET) for efficient retrieval.
    Each transaction is stored under a booking_ref with entry_no as a field.
    """
    if not redis_client:
        logger.error("Redis client is not initialized.")
        return

    try:
        pipe = redis_client.pipeline()

        for transaction in normalized_data:
            booking_ref = transaction.get("booking_ref")
            if booking_ref:
                redis_key = f"booking:{booking_ref}"
                entry_no = transaction.get("entry_no", "default_entry")
                pipe.hset(redis_key, entry_no, json.dumps(transaction))

        pipe.execute()
        logger.info("Transactions successfully stored in Redis.")

    except Exception as e:
        logger.error(f"Error storing data in Redis: {e}")


def lambda_handler(event, context):
    logger.info(f"Lambda invoked with event: {event}")
    state = event.get("state")
    client_id = event.get("clientId")
    file_id = event.get("fileId")
    file_type = event.get("fileType")
    logger.info(f"Processing state: {state}, client_id: {client_id}, file_id: {file_id}, file_type: {file_type}")
    clients_using_old_template = [
        os.environ["BLUESTYLE"],
        os.environ["SHEARINGS_ABTOT"],
        os.environ["WINTER_GREEN"],
        os.environ["CAMPINGS"],
        os.environ["MIGHTY_HOOPLA"],
    ]

    s3 = boto3.client("s3")

    if state == "extract":
        logger.info("Starting file extraction process.")
        ws = Worksheet()

        if file_type == "text/csv":
            s3.download_file(os.environ["BANKING_FILE_BUCKET"], file_id, f"{TEMP_DIR}/banking_file.csv")
            logger.info("Processing a CSV file.")
            try:
                s3.download_file(os.environ["BANKING_FILE_BUCKET"], file_id, f"{TEMP_DIR}/banking_file.csv")
                logger.info(f"Downloaded CSV file to {TEMP_DIR}/banking_file.csv.")
                csv_file = open(f"{TEMP_DIR}/banking_file.csv", "r", encoding="utf-8-sig")  # Handle BOM
            except (IOError, OSError) as err:
                logger.error(f"Error opening CSV file: {err}")
                cancel_banking_file(file_id, str(err))
                raise err

            first_line = csv_file.readline()
            if not first_line or not first_line.strip():
                logger.error("The uploaded CSV file is empty.")
                cancel_banking_file(file_id, "The uploaded csv file is empty")
                raise Exception("The uploaded csv file is empty")

            second_line = csv_file.readline()
            if not second_line or not second_line.strip():
                logger.error("The uploaded CSV file has empty rows.")
                cancel_banking_file(file_id, "The uploaded csv file has empty rows")
                raise Exception("The uploaded csv file has empty rows")

            csv_file.seek(0)
            temp_lines = csv_file.readline() + "\n" + csv_file.readline()
            dialect = csv.Sniffer().sniff(temp_lines, delimiters=",~")
            logger.info(f"Detected CSV dialect: {dialect.delimiter}")
            csv_file.seek(0)
            dict_reader = csv.DictReader(csv_file, dialect=dialect)
            data_items = list(dict_reader)
            headers = data_items[0].keys()
            logger.info(f"CSV headers extracted: {list(headers)}")

        else:
            s3.download_file(os.environ["BANKING_FILE_BUCKET"], file_id, f"{TEMP_DIR}/banking_file.xlsx")
            logger.info("Processing an Excel file.")
            try:
                s3.download_file(os.environ["BANKING_FILE_BUCKET"], file_id, f"{TEMP_DIR}/banking_file.xlsx")
                logger.info(f"Downloaded Excel file to {TEMP_DIR}/banking_file.xlsx.")
                ws.xlsx_to_dict(path=f"{TEMP_DIR}/banking_file.xlsx")
            except (IOError, OSError) as err:
                logger.error(f"Error opening Excel file: {err}")
                cancel_banking_file(file_id, str(err))
                raise err

            try:
                headers = list(ws.header.keys())
                logger.info(f"Excel headers extracted: {headers}")
            except IndexError:
                logger.error("The uploaded banking file contains empty rows.")
                cancel_banking_file(file_id, "The uploaded banking file contains empty rows")
                raise Exception("The uploaded banking file contains empty rows")
            data_items = ws.sanitize_sheet_items

        # Filter headers and normalize
        filtered_headers = filter(lambda x: x not in (None, "None"), headers)
        normalized_headers = [
            custom_filter(header.lstrip("\ufeff").strip().replace("*", "")) for header in filtered_headers
        ]
        logger.info(f"Filtered and normalized headers: {normalized_headers}")

        # Check for transformation
        if "BookingRef" in headers or "\ufeffBookingRef" in headers:
            logger.info(f"'BookingRef' found in original headers. After normalization: {normalized_headers}")

        # Normalize headers
        filtered_headers = filter(lambda x: x not in (None, "None"), headers)
        normalized_headers = [
            custom_filter(header.lstrip("\ufeff").strip().replace("*", "")) for header in filtered_headers
        ]
        logger.info(f"Filtered and normalized headers: {normalized_headers}")

        # Validate lookup_banking
        lookup_banking = list(db.lookup_banking.find())
        if not lookup_banking:
            logger.error("Lookup banking collection is empty.")
            raise Exception("Lookup banking collection is empty.")
        logger.info(f"Lookup banking entries: {len(lookup_banking)}")

        client_mandatory_banking_columns = db.client_banking_column.find({"client_id": ObjectId(client_id)})
        mandatory_banking_column_ids = [item["column"] for item in client_mandatory_banking_columns]
        mandatory_columns = list(filter(lambda x: x["_id"] in mandatory_banking_column_ids, lookup_banking))
        mandatory_column_names = [item["column_name"] for item in mandatory_columns]

        basic_info = list(
            db.client_basic_info.find({"created_at": {"$lt": datetime(2024, 2, 28)}}, projection={"_id": 1})
        )
        basic_info_list = []
        for item in basic_info:
            basic_info_list.append(item.get("_id"))
        basic_info_list.append(ObjectId("67c1900d5e16bf013ea21adf"))
        logger.info(f"Mandatory column names expected: {mandatory_column_names}")

        # Map headers to lookup banking columns
        mapped_headers = []
        multiple_elements = False
        for name in normalized_headers:
            mapped_name = next(filter(lambda x: custom_filter(x["name"]) == name, lookup_banking), None)
            if mapped_name:
                mapped_headers.append(mapped_name["column_name"])
        if (
            ObjectId(client_id) not in basic_info_list
            and "element" not in mapped_headers
            and client_id not in clients_using_old_template
        ):
            multiple_elements = True
            lookup_banking_elements = list(db.lookup_banking_elements.find())
            for name in headers:
                element_mapped_name = next(
                    filter(lambda x: custom_filter(x["name"]) == name, lookup_banking_elements), None
                )
                if element_mapped_name:
                    element_mapped_name["column_name"] = "element"
                    mapped_headers.append(element_mapped_name["column_name"])
        logger.info(f"Mapped headers from file: {mapped_headers}")

        if not set(mandatory_column_names).issubset(set(mapped_headers)):
            missing_columns = set(mandatory_column_names) - set(mapped_headers)
            logger.error(f"Mandatory fields missing: {missing_columns}")
            logger.info(f"Headers received: {headers}")
            cancel_banking_file(file_id, "Mandatory fields missing")
            raise Exception(f"Mandatory fields missing: {missing_columns}")

        # Initialize banking_columns
        banking_columns = {}
        for item in lookup_banking:
            banking_columns[custom_filter(item["name"])] = {
                "column_name": item["column_name"],
                "data_type": item["data_type"],
            }
        lookup_currency = db.lookup_currency.find()
        currency_list = [currency["code"] for currency in lookup_currency]
        lookup_banking_elements = list(db.lookup_banking_elements.find())
        lookup_banking_elements = [item["name"].casefold() for item in lookup_banking_elements]

        for item in lookup_banking:
            banking_columns[custom_filter(item["name"])] = {
                "column_name": item["column_name"],
                "data_type": item["data_type"],
            }
        lookup_currency = db.lookup_currency.find()
        currency_list = [currency["code"] for currency in lookup_currency]
        lookup_banking_elements = list(db.lookup_banking_elements.find())
        lookup_banking_elements = [item["name"].casefold() for item in lookup_banking_elements]

        logger.info("File validation passed. Preparing batch processing.")
        if client_id == BLUESTYLE:
            data_items = manipulate_data_items(data_items)
            # hardocded for now need to changed in future versions
            normalized_headers_map = {
                "EntryNo": "entry_no",
                "EntryDate": "entry_date",
                "BookingRef": "booking_ref",
                "CustomerNo": "customer_no",
                "LeadPax": "lead_pax",
                "PaxCount": "pax_count",
                "DepartureDate": "departure_date",
                "ReturnDate": "return_date",
                "Type": "type",
                "Bonding": "bonding",
                "DaysToProcess": "days_to_process",
                "SupplierRef": "supplier_ref",
                "SupplierNames": "supplier_names",
                "BookingDate": "booking_date",
                "BookingStatus": "booking_status",
                "CurrentReservationStatus": "current_reservation",
                "LastStatusUpdate": "last_status_update",
                "TotalBookingValue": "total_booking_value",
                "NonBondedAmount": "non_bonded_amount",
                "TotalBondedValue": "total_bonded_value",
                "Entry Type": "entry_type",
                "CurrencyCode": "currency_code",
                "PaymentType": "payment_type",
                "PaymentDate": "payment_date",
                "JournalID": "journal_id",
                "AllocRecID": "alloc_rec_id",
                "AllocDate": "alloc_date",
                "Amount": "amount",
                "AllocatedAmount": "allocated_amount",
                "RemainingAmount": "remaining_amount",
                "BondProportion": "bond_proportion",
                "DateBankingFile": "date_banking_file",
                "RefBankingFile": "ref_banking_file",
                "BankFileAmount": "bank_file_amount",
            }
            normalized_data = [
                {normalized_headers_map.get(k, k.lower()): v for k, v in item.items()} for item in data_items
            ]
            # Store data in Redis
            store_in_redis(normalized_data)
            global BATCH_SIZE
            BATCH_SIZE = 1000

        for i in range(ceil(len(data_items) / BATCH_SIZE)):
            data = {
                **event,
                "state": "validate",
                "batchNo": i,
                "batchSize": BATCH_SIZE,
                "headers": normalized_headers,
                "dataItems": data_items[i * BATCH_SIZE : i * BATCH_SIZE + BATCH_SIZE],
                "bankingColumns": banking_columns,
                "mandatoryColumnNames": mandatory_column_names,
                "multipleElements": multiple_elements,
                "currencyList": currency_list,
                "lookupBankingElements": lookup_banking_elements,
            }
            data_key = f"banking/file-validator/extract/{file_id}/{i}.json"
            s3.put_object(
                Body=json.dumps(data),
                Bucket=STATE_MACHINE_PAYLOAD_BUCKET,
                Key=data_key,
            )
            logger.info(f"Saved batch {i} to S3: {data_key}")

        logger.info("File extraction process completed successfully.")
        return {**event, "bucket": STATE_MACHINE_PAYLOAD_BUCKET, "prefix": f"banking/file-validator/extract/{file_id}/"}

    else:
        data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=event["Key"])
        data = json.loads(data_object["Body"].read().decode("utf-8"))
        client_id = data["clientId"]
        file_id = data["fileId"]
        batch_no = data["batchNo"]
        batch_size = data["batchSize"]
        headers = data["headers"]
        data_items = data["dataItems"]
        banking_columns = data["bankingColumns"]
        mandatory_column_names = data["mandatoryColumnNames"]
        multiple_elements = data["multipleElements"]
        lookup_banking_elements = data["lookupBankingElements"]
        currency_list = data["currencyList"]
        sftp = data["sftp"]
        sftp_key = data["sftpKey"]
        file_type = data["fileType"]

        modified_data_list = []
        row_count = batch_no * batch_size + 1
        if multiple_elements:
            data_items = custom_data_modifier_pennywood(data_items, row_count, client_id)
        for row in data_items:
            if multiple_elements:
                row_count = row["initial_row_count"]
            else:
                row_count += 1

            if set(row.values()).union({"None", "", None}) == {"None", "", None}:
                continue
            row = {
                custom_filter(k.replace("*", "")): v.strip() if v is not None else None
                for k, v in row.items()
                if k not in (None, "None", "initial_row_count", "(No value)")
            }
            data = {}
            if row.get("leadguestfirstname") and row.get("leadguestlastname"):
                row["leadpax"] = f"{row.get('leadguestfirstname')} {row.get('leadguestlastname')}"
                row.pop("leadguestfirstname")
                row.pop("leadguestlastname")

            for key, value in row.items():
                if not value or value == "None":
                    value = None
                # Special handling for WINTER_GREEN client
                if client_id == os.environ["WINTER_GREEN"] and value == "(No value)":
                    value = None
                if (
                    not key
                    or not banking_columns.get(key)
                    or (banking_columns[key]["column_name"] not in mandatory_column_names and not value)
                ):
                    continue
                if banking_columns[key]["column_name"] in mandatory_column_names and value is None:
                    cancel_banking_file(
                        file_id,
                        f"Value of mandatory field {banking_columns[key]['column_name']} is missing at row {row_count}",
                    )
                    raise Exception(
                        f"Value of mandatory field {banking_columns[key]['column_name']} is missing at row {row_count}"
                    )
                if (banking_columns[key]["column_name"] == "element") and (
                    value.casefold().strip() not in lookup_banking_elements
                ):
                    cancel_banking_file(
                        file_id,
                        f"{value} is not a valid element type at row {row_count}",
                    )
                    raise Exception(f"{value} is not a valid element type at row {row_count}")
                if banking_columns[key]["column_name"] == "currency_code":
                    value = value.upper()
                    currency_validation(currency_list, value, file_id)
                if banking_columns[key]["column_name"] == "booking_ref" and value[-2:] == ".0":
                    value = value[:-2]
                data[banking_columns[key]["column_name"]] = validate_type(
                    value, banking_columns[key]["data_type"], file_id, key, row_count, client_id
                )
            modified_data_list.append(data)

        if not modified_data_list:
            cancel_banking_file(
                file_id,
                "The uploaded banking file contains empty rows",
            )
            raise Exception("The uploaded banking file contains empty rows")

        data_key = f"banking/file-validator/validate/{file_id}/{batch_no}.json"

        for data_item in modified_data_list:
            data_item.update(
                {
                    "fileId": file_id,
                    "clientId": client_id,
                    "sftp": sftp,
                    "sftpKey": sftp_key,
                    "fileType": file_type,
                }
            )
        s3.put_object(Body=json.dumps(modified_data_list), Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=data_key)

        response = {
            "dataKey": data_key,
        }
        return response
