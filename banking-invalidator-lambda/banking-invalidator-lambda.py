from datetime import datetime
import json
import os
from mongomock import ObjectId
from pymongo import MongoClient, ReadPreference
from helpers.secret_manager_handler import get_secret
import boto3


STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    s3 = boto3.client("s3")
    data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=event["Key"])
    data = json.loads(data_object["Body"].read().decode("utf-8"))
    client_id, transactions = (data["clientId"], data["transactions"])
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    gtl = os.environ.get("GTL")

    transaction_ids = []
    with client.start_session() as session:
        with session.start_transaction(read_preference=ReadPreference.PRIMARY):
            for transaction in transactions:
                transaction_ids.append(ObjectId(transaction["_id"]))
                db[collection_name].update_one(
                    {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                    [
                        {
                            "$set": {
                                "lead_pax": transaction.get("lead_pax"),
                                "pax_count": transaction.get("pax_count"),
                                "booking_date": transaction.get("booking_date"),
                                "departure_date": transaction.get("departure_date"),
                                "return_date": transaction.get("return_date"),
                                "total_booking_value": transaction.get("total_booking_value"),
                                "bonding": transaction.get("bonding"),
                                "type": transaction.get("type"),
                                "payment_type": transaction.get("payment_type"),
                                "updated_at": datetime.utcnow(),
                            }
                        },
                        {
                            "$set": {
                                "balance": {
                                    "$cond": {
                                        "if": {
                                            "$and": [
                                                {
                                                    "$not": {
                                                        "$in": [
                                                            transaction.get("payment_type"),
                                                            ["Protected Deposit - Applied", "Gift Voucher"],
                                                        ]
                                                    }
                                                },
                                                {"$ne": [ObjectId(client_id), ObjectId(gtl)]},
                                            ]
                                        },
                                        "then": {"$round": [{"$add": ["$balance", -1 * transaction["amount"]]}, 2]},
                                        "else": {"$round": ["$balance", 2]},
                                    }
                                },
                                "total_in_trust": {
                                    "$cond": {
                                        "if": {
                                            "$and": [
                                                {
                                                    "$not": {
                                                        "$in": [
                                                            transaction.get("payment_type"),
                                                            ["Protected Deposit - Applied", "Gift Voucher"],
                                                        ]
                                                    }
                                                },
                                                {"$ne": [ObjectId(client_id), ObjectId(gtl)]},
                                            ]
                                        },
                                        "then": {
                                            "$round": [{"$add": ["$total_in_trust", -1 * transaction["amount"]]}, 2]
                                        },
                                        "else": {"$round": ["$total_in_trust", 2]},
                                    }
                                },
                            }
                        },
                        {
                            "$set": {
                                "currency_code": {
                                    "$cond": {
                                        "if": {"$gt": [{"$size": "$currency_code"}, 0]},
                                        "then": {
                                            "$slice": [
                                                "$currency_code",
                                                {"$subtract": [{"$size": "$currency_code"}, 1]},
                                            ]
                                        },
                                        "else": "$currency_code",
                                    }
                                }
                            }
                        },
                    ],
                    collation={"locale": "en", "strength": 1},
                    session=session,
                )
            db.banking_file_details.update_many(
                {"_id": {"$in": transaction_ids}},
                {"$set": {"deleted": True, "status": "Cancelled", "updated_at": datetime.utcnow()}},
                session=session,
            )
