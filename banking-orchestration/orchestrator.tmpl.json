{"Comment": "Banking Workflow Orchestration", "StartAt": "Replace Previous Banking File?", "States": {"Replace Previous Banking File?": {"Type": "Choice", "Choices": [{"Variable": "$.replace", "BooleanEquals": true, "Next": "Fetch Previous Banking File"}], "Default": "File Validator (Extract)"}, "Fetch Previous Banking File": {"Type": "Task", "Resource": "${banking_file_fetch_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Invalidate", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}]}, "Fail": {"Type": "Fail"}, "Invalidate": {"Type": "Map", "Iterator": {"StartAt": "Invalidate Previous Banking Transactions", "States": {"Invalidate Previous Banking Transactions": {"Type": "Task", "Resource": "${banking_invalidator_lambda}", "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 20, "BackoffRate": 1.1}], "End": true}}, "ProcessorConfig": {"Mode": "DISTRIBUTED", "ExecutionType": "STANDARD"}}, "Next": "File Validator (Extract)", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "ResultPath": null, "MaxConcurrency": 1, "Label": "Invalidate", "ItemReader": {"Resource": "arn:aws:states:::s3:listObjectsV2", "Parameters": {"Bucket.$": "$.bucket", "Prefix.$": "$.prefix"}}}, "File Validator (Extract)": {"Type": "Task", "Resource": "${banking_file_validator_lambda}", "Parameters": {"state": "extract", "clientId.$": "$.clientId", "fileId.$": "$.fileId", "fileType.$": "$.fileType", "sftp.$": "$.sftp", "sftpKey.$": "$.sftpKey"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Validate", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}]}, "Validate": {"Type": "Map", "Next": "Persist", "Iterator": {"StartAt": "File Validator", "States": {"File Validator": {"Type": "Task", "Resource": "${banking_file_validator_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}, "ProcessorConfig": {"Mode": "DISTRIBUTED", "ExecutionType": "STANDARD"}}, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "Label": "Validate", "MaxConcurrency": 500, "ItemReader": {"Resource": "arn:aws:states:::s3:listObjectsV2", "Parameters": {"Bucket.$": "$.bucket", "Prefix.$": "$.prefix"}}}, "Persist": {"Type": "Map", "Iterator": {"StartAt": "Banking Persistor", "States": {"Banking Persistor": {"Type": "Task", "Resource": "${banking_persistor_lambda}", "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 20, "BackoffRate": 1.1}], "End": true}}}, "Next": "Banking Aggregator", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "OutputPath": "$[0]", "MaxConcurrency": 1}, "Banking Aggregator": {"Type": "Task", "Resource": "${banking_aggregator_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Calculate Anomalies"}, "Calculate Anomalies": {"Type": "Map", "Iterator": {"StartAt": "Anomaly Detector", "States": {"Anomaly Detector": {"Type": "Task", "Resource": "${banking_anomaly_detector_lambda}", "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 20, "BackoffRate": 1.1}], "End": true}}}, "Next": "Banking Status Updator", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "OutputPath": "$[0]", "MaxConcurrency": 1}, "Banking Status Updator": {"Type": "Task", "Resource": "${banking_status_updator_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Success", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}]}, "Success": {"Type": "Succeed"}}}