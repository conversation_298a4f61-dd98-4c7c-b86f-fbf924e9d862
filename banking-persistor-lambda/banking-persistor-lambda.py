import json
import os
import re
from pymongo import MongoClient, ReadPreference, read_concern, WriteConcern
from datetime import datetime
import boto3
from helpers.amount_modifier import escrow_handler, escrow_handler_bs
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
import tempfile
from sheet2dict import Worksheet
import csv
from helpers import round

# import time
import redis

STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
CLIENT_IDS = [ObjectId(item.strip()) for item in os.environ.get("CLIENT_IDS").split(",")]
TEMP_DIR = tempfile.gettempdir()
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()
BANKING_FILE_BUCKET = os.environ["BANKING_FILE_BUCKET"]
BLUESTYLE = os.environ.get("BLUESTYLE")


# Redis Configuration (Use the same EC2 Redis instance)
REDIS_HOST = os.environ.get("REDIS_URL")
REDIS_PORT = 6379
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD")

# Connect to Redis
try:
    redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD, decode_responses=True)
    redis_client.ping()
    print("Connected to Redis successfully.")
except redis.ConnectionError as e:
    print(f"Redis connection failed: {str(e)}")
    redis_client = None


def get_data_from_s3(data_key):
    s3 = boto3.client("s3")
    data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=data_key)
    return json.loads(data_object["Body"].read().decode("utf-8"))


def process_livescore_data(client_id, file_id, file_type):
    data_items, headers = get_file_data(file_id, file_type)
    booking_ref_values = get_booking_ref_values(headers, data_items)
    update_database(client_id, booking_ref_values)


def get_file_data(file_id, file_type):
    s3 = boto3.client("s3")
    local_file_path = f"{TEMP_DIR}/banking_file.{'csv' if file_type == 'text/csv' else 'xlsx'}"
    s3.download_file(BANKING_FILE_BUCKET, file_id, local_file_path)

    if file_type == "text/csv":
        return process_csv_file(local_file_path)
    else:
        return process_xlsx_file(local_file_path)


def process_csv_file(file_path):
    with open(file_path, "r", encoding="utf-8") as csv_file:
        temp_lines = csv_file.readline() + "\n" + csv_file.readline()
        dialect = csv.Sniffer().sniff(temp_lines, delimiters=",~")
        csv_file.seek(0)
        dict_reader = csv.DictReader(csv_file, dialect=dialect)
        data_items = list(dict_reader)
        headers = data_items[0].keys()
    return data_items, headers


def process_xlsx_file(file_path):
    ws = Worksheet()
    ws.xlsx_to_dict(path=file_path)
    return ws.sanitize_sheet_items, list(ws.header.keys())


def get_booking_ref_values(headers, data_items):
    lookup_banking = list(db.lookup_banking.find())
    booking_ref_names = [item["name"] for item in lookup_banking if item["column_name"] == "booking_ref"]
    booking_ref_values = set()
    for head in headers:
        if head in booking_ref_names:
            booking_ref_values.update(data_value[head] for data_value in data_items)
    return list(booking_ref_values)


def update_database(client_id, booking_ref_values):
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    db[collection_name].delete_many(
        {"client_id": ObjectId(client_id), "booking_ref": {"$nin": booking_ref_values}},
    )
    db.banking_file_details.update_many(
        {"client_id": ObjectId(client_id), "booking_ref": {"$nin": booking_ref_values}},
        {"$set": {"deleted": True}},
        collation={"locale": "en", "strength": 1},
    )


def get_banking_metadata(client_id, file_id):
    return db.banking_metadata.find_one(
        {"client_id": ObjectId(client_id), "banking_files": {"$elemMatch": {"file_id": file_id}}}
    )


def get_trust_and_escrow_and_basic_info(client_id, file_date, file_name):
    basic_info = db.client_basic_info.find_one({"_id": ObjectId(client_id)})
    trust_account = db.lookup_trust_type.find_one({"_id": basic_info["type_of_trust_account"]})
    trust_type = trust_account["name"] if trust_account else None

    escrow_multiplier = basic_info.get("escrow_multiplier")
    escrow_multiplier_date = f'{datetime.strptime(file_date, "%Y-%m-%d").strftime("%Y-%m")}-01'
    client_escrow_multiplier = db.client_escrow_multiplier.find_one(
        {"client_id": ObjectId(client_id), "date": escrow_multiplier_date}
    )

    if client_escrow_multiplier and client_escrow_multiplier.get("multiplier"):
        escrow_multiplier = client_escrow_multiplier.get("multiplier")
        if client_id == os.environ.get("IGLU_ESCROW") and file_name.endswith("-generated.xlsx"):
            escrow_multiplier = client_escrow_multiplier.get("multiplier_difference")

    go_live_date = basic_info.get("go_live_date")
    go_live_date = go_live_date.strftime("%Y-%m-%d")
    return trust_type, escrow_multiplier, basic_info, go_live_date


def get_trust_type_name(client_id, file_name):
    if client_id == os.environ.get("PENNYWOOD"):
        pattern = r"^\d{8}-PTL-Banking-Non-Trust.*$"
        return "Non-Trust" if re.match(pattern, file_name, re.IGNORECASE) else "Trust"
    return None


def calculate_balance(transaction, client_id):
    """
    Calculates the balance based on the transaction's payment type.
    """
    # client = os.environ.get("GTL")
    # if client==client_id:
    #     if transaction.get("payment_type") not in  ["Protected Deposit - Applied","Gift Voucher"]:
    #         return round(transaction["amount"], 2)
    #     else:
    #         return 0.0
    if transaction.get("payment_type") not in ["Protected Deposit - Applied", "Gift Voucher"]:
        return round(transaction["amount"], 2)

    else:
        return 0.0


def insert_trust_fund(transaction, client_id):
    return {
        "client_id": ObjectId(client_id),
        "booking_ref": transaction["booking_ref"],
        "balance": calculate_balance(transaction, client_id),
        "total_in_trust": calculate_balance(transaction, client_id),
        "total_claimed": 0.0,
        "currency_code": [transaction["currency_code"]],
        "lead_pax": transaction.get("lead_pax"),
        "pax_count": transaction.get("pax_count"),
        "booking_date": transaction["booking_date"],
        "departure_date": transaction.get("departure_date"),
        "return_date": transaction.get("return_date"),
        "total_booking_value": transaction.get("total_booking_value"),
        "bonding": transaction.get("bonding"),
        "booking_status": transaction.get("booking_status"),
        "type": transaction.get("type"),
        "payment_type": transaction.get("payment_type"),
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }


def update_trust_fund(transaction, client_id, currency_code, session):
    return {
        "$set": {
            "balance": round(transaction["amount"], 2),
            "total_in_trust": transaction["amount"],
            "currency_code": currency_code,
            "lead_pax": transaction.get("lead_pax"),
            "pax_count": transaction.get("pax_count"),
            "booking_date": transaction["booking_date"],
            "departure_date": transaction.get("departure_date"),
            "return_date": transaction.get("return_date"),
            "total_booking_value": transaction.get("total_booking_value"),
            "bonding": transaction.get("bonding"),
            "type": transaction.get("type"),
            "payment_type": transaction.get("payment_type"),
            "updated_at": datetime.utcnow(),
        }
    }


def update_trust_fund_with_balance(transaction, currency_code, client_id):
    gtl_client_id = os.environ.get("GTL")
    return [
        {
            "$set": {
                "currency_code": currency_code,
                "lead_pax": transaction.get("lead_pax"),
                "pax_count": transaction.get("pax_count"),
                "booking_date": transaction["booking_date"],
                "departure_date": transaction.get("departure_date"),
                "return_date": transaction.get("return_date"),
                "total_booking_value": transaction.get("total_booking_value"),
                "bonding": transaction.get("bonding"),
                "type": transaction.get("type"),
                "payment_type": transaction.get("payment_type"),
                "updated_at": datetime.utcnow(),
            }
        },
        {
            "$set": {
                "balance": {
                    "$cond": {
                        "if": {
                            "$and": [
                                {
                                    "$not": {
                                        "$in": [
                                            transaction.get("payment_type"),
                                            ["Protected Deposit - Applied", "Gift Voucher"],
                                        ]
                                    }
                                },
                                {"$ne": ["$client_id", ObjectId(gtl_client_id)]},
                            ]
                        },
                        "then": {"$round": [{"$add": ["$balance", transaction["amount"]]}, 2]},
                        "else": {"$round": ["$balance", 2]},
                    }
                },
                "total_in_trust": {
                    "$cond": {
                        "if": {
                            "$and": [
                                {
                                    "$not": {
                                        "$in": [
                                            transaction.get("payment_type"),
                                            ["Protected Deposit - Applied", "Gift Voucher"],
                                        ]
                                    }
                                },
                                {"$ne": ["$client_id", ObjectId(gtl_client_id)]},
                            ]
                        },
                        "then": {"$round": [{"$add": ["$total_in_trust", transaction["amount"]]}, 2]},
                        "else": {"$round": ["$total_in_trust", 2]},
                    }
                },
            }
        },
    ]


def fetch_from_redis(booking_ref):
    """
    Fetch transactions for a given booking_ref from Redis Hash (HGETALL).
    Returns an array of JSON objects.
    """
    if not booking_ref:
        print("Missing booking_ref.")
        return []

    try:
        redis_key = f"booking:{booking_ref}"
        transactions_dict = redis_client.hgetall(redis_key)

        if transactions_dict:
            transactions = [json.loads(v) for v in transactions_dict.values()]
            # print(f"Found {len(transactions)} records for booking_ref {booking_ref}.")
            return transactions
        else:
            print(f"No records found for booking_ref {booking_ref} in Redis.")
            return []

    except Exception as e:
        print(f"Error retrieving data from Redis: {e}")
        return []


def calculate_banking_amount_bluestyle(transactions, old_transactions, transaction, escrow_multiplier):
    if not transactions:
        return 0

    # Fast date parser using slicing for "YYYY-MM-DD" format.
    # def parse_date_fast(date_str):
    #     try:
    #         return datetime(
    #             int(date_str[0:4]),
    #             int(date_str[5:7]),
    #             int(date_str[8:10])
    #         )
    #     except Exception:
    #         return None

    # Only consider transactions as Trust entries if:
    # 1. They are not an "update" or "draft" entry.
    # 2. Both departure_date and payment_date exist and can be parsed.
    # 3. The difference between departure_date and payment_date is at least 1 day.
    def is_trust(txn):
        # First check entry_type
        entry_type = txn.get("entry_type", "").lower()
        if entry_type in ("update", "draft"):
            print(f"Transaction excluded: entry_type is {entry_type}")
            return False
        
        # Check departure_date and payment_date exist
        dep_str = txn.get("departure_date")
        pay_str = txn.get("payment_date")
        if not dep_str or not pay_str:
            print(f"Transaction excluded: missing dates - dep_date={dep_str}, pay_date={pay_str}")
            return False
        
        # Parse dates consistently using strptime
        try:
            dep_date = datetime.strptime(dep_str, "%Y-%m-%d")
            pay_date = datetime.strptime(pay_str, "%Y-%m-%d")
        except Exception as e:
            print(f"Transaction excluded: date parsing error - {str(e)}")
            return False
        
        # Check day difference
        day_diff = (dep_date - pay_date).days
        result = day_diff >= 1
        if not result:
            print(f"Transaction excluded: day difference {day_diff} is less than 1")
        
        return result

    # Filter current transactions to only include Trust entries.
    trust_transactions = [txn for txn in transactions if is_trust(txn)]
    if not trust_transactions:
        return 0

    # Sum the allocated amounts from trust transactions.
    total_allocated_payments = sum(float(txn.get("allocated_amount", 0)) for txn in trust_transactions)

    # Select the transaction with the highest journal_id (converted to float)
    # and retrieve its non-bonded amount.
    latest_transaction = max(trust_transactions, key=lambda txn: float(txn.get("journal_id", 0)))
    latest_non_bonded_amount = float(latest_transaction.get("non_bonded_amount", 0))

    total_payments_sent_to_trust = 0
    if old_transactions:
        # Filter old transactions to only include Trust entries.
        trust_old_transactions = [txn for txn in old_transactions if is_trust(txn)]
        # Sum their allocated amounts.
        old_allocated_sum = sum(float(txn.get("allocated_amount", 0)) for txn in trust_old_transactions)
        total_allocated_payments += old_allocated_sum
        # Sum the payment amount from trust old transactions.
        total_payments_sent_to_trust = sum(float(txn.get("amount", 0)) for txn in trust_old_transactions)

    bond_proportion = escrow_multiplier if escrow_multiplier else 0.4
    # Calculate the banking amount using the provided formula.
    banking_amount = ((total_allocated_payments - latest_non_bonded_amount) * bond_proportion) - total_payments_sent_to_trust

    # Ensure the final amount is at least -total_payments_sent_to_trust.
    final_banking_amount = max(-total_payments_sent_to_trust, banking_amount)
    return final_banking_amount


def lambda_handler(event, context):
    data_key = event.get("dataKey")
    data = get_data_from_s3(data_key)
    data.sort(key=lambda x: x["booking_ref"])
    first_booking = data[0]
    client_id = first_booking.get("clientId")
    file_id = first_booking.get("fileId")
    file_type = first_booking.get("fileType")
    sftp = first_booking.get("sftp")
    gtl = os.environ.get("GTL")
    sftp_key = first_booking.get("sftpKey")

    if client_id == os.environ.get("LIVESCORE"):
        process_livescore_data(client_id, file_id, file_type)
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"

    banking_metadata = get_banking_metadata(client_id, file_id)
    file_date = banking_metadata["banking_files"][-1]["file_date"]
    file_name = banking_metadata["banking_files"][-1]["file_name"]

    trust_type, escrow_multiplier, basic_info, go_live_date = get_trust_and_escrow_and_basic_info(
        client_id, file_date, file_name
    )

    trust_type_name = get_trust_type_name(client_id, file_name)

    with client.start_session() as session:
        with session.start_transaction(
            read_preference=ReadPreference.PRIMARY,
            read_concern=read_concern.ReadConcern(level="snapshot"),
            write_concern=WriteConcern(w=1),
        ):
            item_count = {}
            deposit = {}
            banking_payments = []
            booking_ref_set = set()

            for transaction in data:
                existing_trust_fund = db[collection_name].find_one(
                    {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                    collation={"locale": "en", "strength": 1},
                    session=session,
                )
                if client_id == os.environ.get("WLH"):
                    transaction["original_balance"] = transaction["amount"]

                escrow_clients = trust_type == "ATOL Escrow" or trust_type == "Escrow Trigger"
                if escrow_clients:
                    if escrow_multiplier is not None:
                        if client_id == BLUESTYLE:
                            escrow_handler_response = escrow_handler_bs(
                                amount=transaction.get("allocated_amount"),
                                payment_date=transaction.get("payment_date"),
                                departure_date=transaction.get("departure_date"),
                                escrow_multiplier=escrow_multiplier,
                                go_live_date=go_live_date,
                            )
                            booking_ref = transaction["booking_ref"]
                            non_trust_flag = escrow_handler_response[2]
                            transaction["non_trust_flag"] = non_trust_flag
                            db.banking_file_details.update_one(
                                {"client_id": ObjectId(client_id), "booking_ref": booking_ref},
                                {"$set": {"non_trust_flag": non_trust_flag}},
                            )

                        else:
                            escrow_handler_response = escrow_handler(
                                transaction["amount"],
                                client_id,
                                transaction["booking_date"],
                                escrow_multiplier=escrow_multiplier,
                            )

                    else:
                        escrow_handler_response = escrow_handler(
                            transaction["amount"],
                            client_id=client_id,
                            booking_date=transaction.get("booking_date"),
                        )
                    transaction["amount"] = round(transaction["amount"], 2)
                    transaction["original_amount"] = transaction["amount"]
                    transaction["escrow_multiplier"] = escrow_handler_response[1]
                    transaction["amount"] = round(escrow_handler_response[0], 2)

                    if client_id == os.environ.get("WLH"):
                        transaction["balance"] = transaction["amount"]
                        transaction["amount"] = (
                            (transaction["amount"] - existing_trust_fund["balance"])
                            if existing_trust_fund and existing_trust_fund.get("balance")
                            else transaction["amount"]
                        )
                        transaction["original_amount"] = escrow_handler(
                            transaction["amount"],
                            trust_type,
                            escrow_multiplier=(1 / escrow_multiplier),
                        )[0]

                transaction["currency_code"] = (
                    transaction["currency_code"] if transaction.get("currency_code") else "GBP"
                )

                old_booking_ref = transaction.get("transfer_of_funds")
                if old_booking_ref:
                    db[collection_name].update_one(
                        {"client_id": ObjectId(client_id), "booking_ref": old_booking_ref},
                        [
                            {
                                "$set": {
                                    "updated_at": datetime.utcnow(),
                                }
                            },
                            {
                                "$set": {
                                    "balance": {
                                        "$cond": {
                                            "if": {
                                                "$and": [
                                                    {
                                                        "$not": {
                                                            "$in": [
                                                                transaction.get("payment_type"),
                                                                ["Protected Deposit - Applied", "Gift Voucher"],
                                                            ]
                                                        }
                                                    },
                                                    {"$ne": ["$client_id", ObjectId(gtl)]},
                                                ]
                                            },
                                            "then": {"$round": [{"$add": ["$balance", -1 * transaction["amount"]]}, 2]},
                                            "else": {"$round": ["$balance", 2]},
                                        }
                                    },
                                    "total_in_trust": {
                                        "$cond": {
                                            "if": {
                                                "$and": [
                                                    {
                                                        "$not": {
                                                            "$in": [
                                                                transaction.get("payment_type"),
                                                                ["Protected Deposit - Applied", "Gift Voucher"],
                                                            ]
                                                        }
                                                    },
                                                    {"$ne": ["$client_id", ObjectId(gtl)]},
                                                ]
                                            },
                                            "then": {
                                                "$round": [{"$add": ["$total_in_trust", -1 * transaction["amount"]]}, 2]
                                            },
                                            "else": {"$round": ["$total_in_trust", 2]},
                                        }
                                    },
                                }
                            },
                        ],
                        collation={"locale": "en", "strength": 1},
                        session=session,
                    )

                if (
                    existing_trust_fund
                    and ObjectId(client_id) in CLIENT_IDS
                    and transaction["booking_ref"] not in booking_ref_set
                    and basic_info.get("new_workflow")
                ):
                    reused = handle_booking_ref_reuse_banking_persistor(
                        existing_trust_fund, transaction, client_id, session
                    )
                    if reused:
                        existing_trust_fund = db[collection_name].find_one(
                            {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                            collation={"locale": "en", "strength": 1},
                            session=session,
                        )

                # Bluestyle computations
                if client_id == BLUESTYLE:
                    departure_date = datetime.strptime(transaction["departure_date"], "%Y-%m-%d")
                    payment_date = datetime.strptime(transaction["payment_date"], "%Y-%m-%d")

                    if transaction["entry_type"].lower() in ("update", "draft"):
                        transaction["amount"] = 0
                    elif (departure_date - payment_date).days < 1:
                        transaction["amount"] = 0
                    else:
                        old_transactions = list(db.banking_file_details.find(
                            {
                                "client_id": ObjectId(client_id),
                                "booking_ref": transaction["booking_ref"],
                                "entry_type": {"$ne": "Update"}
                            },
                            sort=[("date_banking_file", -1)]
                        ))
                    
                        try:
                            target_booking_ref = transaction.get("booking_ref")
                            if not target_booking_ref:
                                print("Skipping transaction: No booking_ref found.")
                            processed_key = f"processed:{target_booking_ref}"
                            # Atomically set the key. Only the first caller will succeed.
                            if redis_client.set(processed_key, transaction["amount"], nx=True, ex=3600):
                                # Use the optimized fetch function
                                transactions = fetch_from_redis(target_booking_ref)
                                if transactions:
                                    banking_amount = calculate_banking_amount_bluestyle(
                                        transactions, old_transactions, transaction, escrow_multiplier
                                    )
                                    transaction["amount"] = banking_amount
                                    print(f"Final Banking Amount for {target_booking_ref}: {banking_amount}")
                                else:
                                    print(f"No records found for booking_ref {target_booking_ref} in Redis.")
                            else:
                                print(f"Duplicate booking found for booking {target_booking_ref}. Skipping processing.")
                                transaction["amount"] = 0
                        except Exception as e:
                            print(f"Error retrieving data from Redis: {str(e)}")
                
                if not existing_trust_fund:
                    if client_id != BLUESTYLE:
                        db[collection_name].insert_one(insert_trust_fund(transaction, client_id), session=session)
                    elif client_id == BLUESTYLE and transaction["entry_type"].lower() == "new":
                        db[collection_name].insert_one(insert_trust_fund(transaction, client_id), session=session)
                else:
                    currency_code = existing_trust_fund["currency_code"]
                    if transaction["currency_code"] not in currency_code:
                        currency_code.append(transaction["currency_code"])
                    if client_id == os.environ.get("LIVESCORE"):
                        db[collection_name].update_one(
                            {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                            update_trust_fund(transaction, client_id, currency_code, session),
                            collation={"locale": "en", "strength": 1},
                            session=session,
                        )
                    
                    elif client_id == BLUESTYLE:
                        if transaction["entry_type"].lower() == "update":
                            db[collection_name].update_one(
                                {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                                {
                                    "$inc": {
                                        "balance": 0,
                                        "total_in_trust": 0,
                                    },
                                    "$set": {
                                        "currency_code": [transaction["currency_code"]],
                                        "lead_pax": transaction.get("lead_pax"),
                                        "pax_count": transaction.get("pax_count"),
                                        "booking_date": transaction["booking_date"],
                                        "departure_date": transaction.get("departure_date"),
                                        "return_date": transaction.get("return_date"),
                                        "total_booking_value": transaction.get("total_booking_value"),
                                        "bonding": transaction.get("bonding"),
                                        "type": transaction.get("type"),
                                        "payment_type": transaction.get("payment_type"),
                                        "updated_at": datetime.utcnow(),
                                    },
                                },
                                collation={"locale": "en", "strength": 1},
                                session=session,
                            )
                        elif transaction["entry_type"].lower() == "new":
                            db[collection_name].update_one(
                                {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                                update_trust_fund_with_balance(transaction, currency_code, client_id),
                                collation={"locale": "en", "strength": 1},
                                session=session,
                            )

                    else:
                        db[collection_name].update_one(
                            {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                            update_trust_fund_with_balance(transaction, currency_code, client_id),
                            collation={"locale": "en", "strength": 1},
                            session=session,
                        )

                transaction.update(
                    {
                        "client_id": ObjectId(client_id),
                        "banking_id": banking_metadata["_id"],
                        "file_id": file_id,
                        "deleted": False,
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                        "trust_type": trust_type_name,
                    }
                )
                banking_payments.append(transaction)

                if not item_count.get(transaction["currency_code"]):
                    item_count[transaction["currency_code"]] = 1
                    deposit[transaction["currency_code"]] = transaction["amount"]
                else:
                    item_count[transaction["currency_code"]] += 1
                    deposit[transaction["currency_code"]] += transaction["amount"]
                booking_ref_set.add(transaction["booking_ref"])
            if banking_payments:
                if client_id == os.environ.get("LIVESCORE"):
                    for item in banking_payments:
                        db.banking_file_details.find_one_and_update(
                            {"client_id": ObjectId(client_id), "booking_ref": item["booking_ref"]},
                            {
                                "$set": {**item},
                            },
                            session=session,
                            upsert=True,
                        )
                else:
                    db.banking_file_details.insert_many(banking_payments, session=session)
                    session.commit_transaction()

            response = []
            output_data_key = f"banking/persistor/output/{file_id}.json"
            s3 = boto3.client("s3")
            try:
                s3.download_file(STATE_MACHINE_PAYLOAD_BUCKET, output_data_key, f"{TEMP_DIR}/persistor_output")
                with open(f"{TEMP_DIR}/persistor_output", "rb") as file_obj:
                    data = json.load(file_obj)
                if data:
                    response = data
                else:
                    response = []
            except Exception as e:
                print(f"An error occurred: {str(e)}")

            response.append(
                {
                    "fileId": file_id,
                    "clientId": client_id,
                    "itemCount": item_count,
                    "deposit": deposit,
                    "sftp": sftp,
                    "sftpKey": sftp_key,
                }
            )

    s3.put_object(
        Body=json.dumps(response),
        Bucket=STATE_MACHINE_PAYLOAD_BUCKET,
        Key=output_data_key,
    )
    return {
        "fileId": file_id,
        "clientId": client_id,
        "bucket": STATE_MACHINE_PAYLOAD_BUCKET,
        "dataKey": output_data_key,
        "sftp": sftp,
        "sftpKey": sftp_key,
    }


def handle_booking_ref_reuse_banking_persistor(existing_trust_fund, transaction, client_id, session):
    if (existing_trust_fund.get("lead_pax") != transaction.get("lead_pax")) or (
        existing_trust_fund.get("booking_date") != transaction["booking_date"]
    ):
        old_booking_ref = existing_trust_fund["booking_ref"]
        new_booking_ref = ""
        older_trust_funds = list(
            db.trust_fund_v2.find(
                {
                    "client_id": ObjectId(client_id),
                    "original_booking_ref": old_booking_ref,
                },
                projection={"booking_ref": 1},
                collation={"locale": "en", "strength": 1},
            )
        )
        if not older_trust_funds:
            new_suffix = 1
        else:
            old_suffixes = [int(item["booking_ref"].split("_")[-1]) for item in older_trust_funds]
            new_suffix = max(old_suffixes) + 1
        new_booking_ref = f"{old_booking_ref}_old_{str(new_suffix)}"

        db.trust_fund_v2.update_many(
            {"client_id": ObjectId(client_id), "booking_ref": old_booking_ref},
            {"$set": {"booking_ref": new_booking_ref, "original_booking_ref": old_booking_ref}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )
        db.banking_file_details.update_many(
            {"client_id": ObjectId(client_id), "booking_ref": old_booking_ref},
            {"$set": {"booking_ref": new_booking_ref}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )
        db.claims_file_details.update_many(
            {"client_id": ObjectId(client_id), "booking_ref": old_booking_ref},
            {"$set": {"booking_ref": new_booking_ref}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )
        return True
    return False
