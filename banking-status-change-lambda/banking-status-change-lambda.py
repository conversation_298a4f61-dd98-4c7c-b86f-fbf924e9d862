from datetime import datetime
import json
import os
from mongomock import ObjectId
from pymongo import MongoClient, ReadPreference
from helpers import track_opening_closing_balance_changes
from helpers.secret_manager_handler import get_secret
import boto3
from helpers import round


STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    if not event["state"] == "final":
        s3 = boto3.client("s3")
        data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=event["input"]["Key"])
        data = json.loads(data_object["Body"].read().decode("utf-8"))
        (client_id, created_at, file_date, is_deleted, transaction_ids) = (
            data["clientId"],
            datetime.fromisoformat(data["createdAt"]),
            data["fileDate"],
            data["isDeleted"],
            data["transaction_ids"],
        )

        gtl = os.environ.get("GTL")
        transaction_ids = [ObjectId(transaction_id) for transaction_id in transaction_ids]
        with client.start_session() as session:
            with session.start_transaction(read_preference=ReadPreference.PRIMARY):
                multiplier = 1
                if not is_deleted:  # Delete valid transactions
                    multiplier = -1
                    banking_file_details = list(
                        db.banking_file_details.find({"_id": {"$in": transaction_ids}, "deleted": False})
                    )
                    db.banking_file_details.update_many(
                        {"_id": {"$in": transaction_ids}, "deleted": False},
                        {"$set": {"deleted": True, "status": "Cancelled"}},
                        session=session,
                    )
                    db.anomaly_banking.update_many(
                        {"transaction_id": {"$in": transaction_ids}},
                        {"$set": {"deleted": True, "updated_at": datetime.utcnow()}},
                        session=session,
                    )

                else:  # Reinstate deleted transactions
                    banking_file_details = list(
                        db.banking_file_details.find({"_id": {"$in": transaction_ids}, "deleted": True})
                    )
                    db.banking_file_details.update_many(
                        {"_id": {"$in": transaction_ids}, "deleted": True},
                        {"$set": {"deleted": False, "status": "Reinstated"}},
                        session=session,
                    )
                    db.anomaly_banking.update_many(
                        {"transaction_id": {"$in": transaction_ids}},
                        {"$set": {"deleted": False, "updated_at": datetime.utcnow()}},
                        session=session,
                    )
                item_count_changes = {}
                deposit_changes = {}
                for item in banking_file_details:
                    collection_name = (
                        "trust_fund" if str(item["client_id"]) == os.environ.get("NAS") else "trust_fund_v2"
                    )
                    inc_balance = multiplier * round(item["amount"], 2)
                    inc_total_in_trust = multiplier * round(item["amount"], 2)
                    db[collection_name].update_one(
                        {"client_id": item["client_id"], "booking_ref": item["booking_ref"]},
                        [
                            {
                                "$set": {
                                    "balance": {
                                        "$cond": {
                                            "if": {
                                                "$and": [
                                                    {
                                                        "$not": {
                                                            "$in": [
                                                                item.get("payment_type"),
                                                                ["Protected Deposit - Applied", "Gift Voucher"],
                                                            ]
                                                        }
                                                    },
                                                    {"$ne": [item["client_id"], gtl]},
                                                ]
                                            },
                                            "then": {"$round": [{"$add": ["$balance", inc_balance]}, 2]},
                                            "else": {"$round": ["$balance", 2]},
                                        }
                                    },
                                    "total_in_trust": {
                                        "$cond": {
                                            "if": {
                                                "$and": [
                                                    {
                                                        "$not": {
                                                            "$in": [
                                                                item.get("payment_type"),
                                                                ["Protected Deposit - Applied", "Gift Voucher"],
                                                            ]
                                                        }
                                                    },
                                                    {"$ne": [item["client_id"], gtl]},
                                                ]
                                            },
                                            "then": {"$round": [{"$add": ["$total_in_trust", inc_total_in_trust]}, 2]},
                                            "else": {"$round": ["$total_in_trust", 2]},
                                        }
                                    },
                                }
                            }
                        ],
                        collation={"locale": "en", "strength": 1},
                        session=session,
                    )

                    if item["currency_code"] in item_count_changes.keys():
                        item_count_changes[item["currency_code"]] += multiplier
                        deposit_changes[item["currency_code"]] += inc_balance
                    else:
                        item_count_changes[item["currency_code"]] = multiplier
                        deposit_changes[item["currency_code"]] = inc_balance
        return {
            "itemCountChanges": item_count_changes,
            "depositChanges": deposit_changes,
        }
    else:
        client_id, banking_id, file_id, file_date, new_status, created_at = (
            event["data"]["clientId"],
            event["data"]["bankingId"],
            event["data"]["fileId"],
            event["data"]["fileDate"],
            event["data"]["newStatus"],
            datetime.fromisoformat(event["data"]["createdAt"]),
        )

        item_count = {}
        deposit = {}
        for data in event["data"]["input"]:
            for currency, count in data["itemCountChanges"].items():
                if currency not in item_count:
                    item_count[currency] = count
                    deposit[currency] = data["depositChanges"][currency]
                else:
                    item_count[currency] += count
                    deposit[currency] += data["depositChanges"][currency]

        inc_items = {}
        for currency in item_count.keys():
            inc_items.update(
                {
                    f"banking_files.$.item_count.{currency}": item_count[currency],
                    f"banking_files.$.deposit.{currency}": deposit[currency],
                }
            )
        inc_query = {"$inc": inc_items} if inc_items else {}

        with client.start_session() as session:
            with session.start_transaction(read_preference=ReadPreference.PRIMARY):
                db.banking_metadata.update_one(
                    {"_id": ObjectId(banking_id), "banking_files.file_id": file_id},
                    {
                        "$set": {
                            "status": new_status,
                            "banking_files.$.status": new_status,
                            "updated_at": datetime.utcnow(),
                        },
                        **inc_query,
                    },
                    session=session,
                )
                for currency, amount_difference in deposit.items():
                    track_opening_closing_balance_changes(
                        db,
                        {"client_id": ObjectId(client_id), "created_at": created_at, "currency_code": currency},
                        amount_difference,
                        file_date,
                        session,
                    )
