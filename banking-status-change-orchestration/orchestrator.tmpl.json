{"Comment": "Banking Status Change Orchestration", "StartAt": "Banking Status Change", "States": {"Banking Status Change": {"Type": "Map", "Iterator": {"StartAt": "Handle Banking Status Change", "States": {"Handle Banking Status Change": {"Type": "Task", "Resource": "${banking_status_change_lambda}", "OutputPath": "$", "Parameters": {"state": "status-change", "input.$": "$"}, "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 20, "BackoffRate": 1.1}], "End": true}}, "ProcessorConfig": {"Mode": "DISTRIBUTED", "ExecutionType": "STANDARD"}}, "Next": "Finalize Banking Status Change", "MaxConcurrency": 1, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "Label": "BankingStatusChange", "ItemReader": {"Resource": "arn:aws:states:::s3:listObjectsV2", "Parameters": {"Bucket.$": "$.bucket", "Prefix.$": "$.prefix"}}, "ResultPath": "$.input"}, "Fail": {"Type": "Fail"}, "Finalize Banking Status Change": {"Type": "Task", "Resource": "${banking_status_change_lambda}", "Parameters": {"state": "final", "data.$": "$"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Success", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}]}, "Success": {"Type": "Succeed"}}}