from datetime import datetime
import os
from pymongo import MongoClient, ReadPreference
from helpers.secret_manager_handler import get_secret
from pymongo.errors import NotPrimaryError
import logging
from bson import ObjectId
import boto3
import tempfile

logger = logging.getLogger()
logger.setLevel(logging.INFO)

client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()
TEMP_DIR = tempfile.gettempdir()


def lambda_handler(event, context):
    client_id = event["clientId"]
    file_id = event["fileId"]
    # sftp = event["sftp"]
    # sftp_key = event["sftpKey"]

    logger.info(f"Processing file: {file_id} for client: {client_id}")

    try:
        with client.start_session() as session:
            with session.start_transaction(read_preference=ReadPreference.PRIMARY):
                logger.info(f"Starting transaction for client: {client_id} and file: {file_id}")

                db.banking_metadata.update_one(
                    {"client_id": ObjectId(client_id), "banking_files": {"$elemMatch": {"file_id": file_id}}},
                    {
                        "$set": {
                            "banking_files.$.status": "Submitted",
                            "banking_files.$.notes": "Successfully processed file.",
                            "status": "Submitted",
                            "updated_at": datetime.utcnow(),
                        }
                    },
                    session=session,
                )
                logger.info(f"Updated banking_metadata for client: {client_id}")

                db.client_basic_info.update_one(
                    {"_id": ObjectId(client_id)}, {"$set": {"is_editable": False}}, session=session
                )
                logger.info(f"Updated client_basic_info for client: {client_id}")

                # if sftp:
                #     sftp_file_to_processed_folder(sftp_key)

    except NotPrimaryError as e:
        logger.error(f"NotPrimaryError encountered for client: {client_id}, file: {file_id}. Error details: {e}")
        return {
            "error": "NotPrimaryError",
            "message": str(e),
            "details": e.details if hasattr(e, "details") else "No additional details.",
        }
    except Exception as e:
        logger.error(f"An error occurred for client: {client_id}, file: {file_id}. Error details: {e}")
        return {"error": "ProcessingError", "message": str(e)}

    logger.info(f"Successfully processed banking file for client: {client_id}, file: {file_id}")
    return {**event, "msg": "Successfully processed banking file."}


def sftp_file_to_processed_folder(key):
    s3 = boto3.client("s3")

    if "/" not in key:
        file_name = key
        folder_location = "/"
    else:
        file_name_split_list = key.rsplit("/", 1)
        file_name = file_name_split_list[-1]
        folder_location = f"/{file_name_split_list[0]}/"

    # s3.download_file(os.environ["SFTP_BUCKET"], key, f"{TEMP_DIR}/{file_name}")
    # file_id = f"{folder_location[1:]}Processed/{file_name}"
    # s3.upload_file(f"{TEMP_DIR}/{file_name}", os.environ["SFTP_BUCKET"], file_id)
    # s3.delete_object(Bucket=os.environ["SFTP_BUCKET"], Key=f"{folder_location[1:]}{file_name}")

    logger.info(f"Downloading file {file_name} from S3 to {TEMP_DIR}")

    s3.download_file(os.environ["PTT_BUCKET"], key, f"{TEMP_DIR}/{file_name}")
    file_id = f"{folder_location[1:]}Processed/{file_name}"

    logger.info(f"Uploading processed file {file_name} to {file_id} in S3")
    s3.upload_file(f"{TEMP_DIR}/{file_name}", os.environ["PTT_BUCKET"], file_id)

    logger.info(f"Deleting original file {file_name} from S3")
    s3.delete_object(Bucket=os.environ["PTT_BUCKET"], Key=f"{folder_location[1:]}{file_name}")
