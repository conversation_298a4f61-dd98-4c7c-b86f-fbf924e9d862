import os
import json
import boto3
from botocore.exceptions import ClientError
import logging

logger = logging.getLogger(__name__)


def find(stepfunctions_client, state_machine_name):
    """
    Finds a state machine by name. This function iterates the state machines for
    the current account until it finds a match and returns the first matching
    state machine.

    :param state_machine_name: The name of the state machine to find.
    :return: The ARN of the named state machine when found; otherwise, None.
    """
    try:
        paginator = stepfunctions_client.get_paginator("list_state_machines")
        for page in paginator.paginate():
            for machine in page["stateMachines"]:
                if machine["name"] == state_machine_name:
                    state_machine_name = state_machine_name
                    state_machine_arn = machine["stateMachineArn"]
                    break
            if state_machine_arn is not None:
                break
        if state_machine_arn is not None:
            logger.info("Found state machine %s with ARN %s.", state_machine_name, state_machine_arn)
        else:
            logger.info("Couldn't find state machine %s.", state_machine_name)
    except ClientError:
        logger.exception("Couldn't find state machine %s.", state_machine_name)
        raise
    else:
        return state_machine_arn


def lambda_handler(event, context):
    sfn_client = boto3.client("stepfunctions")
    state_machine_arn = find(sfn_client, f'banking-orchestration-{os.environ["ENVIRONMENT"]}')
    sfn_client.start_execution(stateMachineArn=state_machine_arn, input=json.dumps(event))
    return {"statusCode": 200, "body": json.dumps("Step Function Triggered!")}
