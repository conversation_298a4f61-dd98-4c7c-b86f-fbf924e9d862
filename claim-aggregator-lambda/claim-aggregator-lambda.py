from datetime import datetime
import json
from math import ceil
import os
from pymongo import MongoClient
from helpers.secret_manager_handler import get_secret
import boto3
from bson import ObjectId

STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    client_id = event["clientId"]
    file_id = event["fileId"]
    bucket = event["bucket"]
    data_key = event["dataKey"]
    s3 = boto3.client("s3")

    data_object = s3.get_object(Bucket=bucket, Key=data_key)
    data_items = json.loads(data_object["Body"].read().decode("utf-8"))

    item_count = {}
    claim_total = {}
    checks = {}
    checked_amount = {}

    for data in data_items:
        for currency, count in data["itemCount"].items():
            if currency not in item_count:
                item_count[currency] = count
                claim_total[currency] = data["claimTotal"][currency]
                checks[currency] = 0
                checked_amount[currency] = 0
            else:
                item_count[currency] += count
                claim_total[currency] += data["claimTotal"][currency]

    metadata = db.claims_metadata.find_one(
        {"client_id": ObjectId(client_id), "claim_files": {"$elemMatch": {"file_id": file_id}}}
    )
    db.claims_metadata.update_one(
        {"_id": metadata["_id"], "claim_files": {"$elemMatch": {"file_id": file_id}}},
        {
            "$set": {
                "claim_files.$.item_count": item_count,
                "claim_files.$.claim_total": claim_total,
                "claim_files.$.checks": checks,
                "claim_files.$.checked_amount": checked_amount,
                "updated_at": datetime.utcnow(),
            }
        },
    )
    db.claim_testing.update_one(
        {"claims_id": metadata["_id"]},
        {
            "$set": {
                "original_claim": claim_total,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }
        },
        upsert=True,
    )

    claim_transactions = db.claims_file_details.find({"file_id": file_id}, projection={"_id": 1})
    transaction_ids = [str(transaction["_id"]) for transaction in claim_transactions]

    output = {
        "clientId": client_id,
        "fileId": file_id,
        "sftp": event["sftp"],
        "sftpKey": event["sftpKey"],
        "transactionIds": transaction_ids,
    }
    data_key = f"claim/aggregator/{file_id}.json"
    s3.put_object(Body=json.dumps(output), Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=data_key)
    response = [{"batchNo": i, "dataKey": data_key} for i in range(ceil(len(transaction_ids) / BATCH_SIZE))]
    return response
