from datetime import datetime, timedelta
import json
import os
from pymongo import MongoClient
from bson import ObjectId
from helpers.date_modifier import get_next_date
from helpers.secret_manager_handler import get_secret
import boto3
from helpers import round
import logging

STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def add_claim_anomaly(transaction, file_id, anomaly_type, anomaly_id, **kwargs):
    claim_anomaly = {
        "booking_ref": transaction["booking_ref"],
        "transaction_id": transaction["_id"],
        "client_id": transaction["client_id"],
        "claims_id": transaction["claims_id"],
        "file_id": file_id,
        "anomaly_type": anomaly_type,
        "anomaly_id": anomaly_id,
        "status": "Unresolved",
        "deleted": False,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        **kwargs,
    }
    return claim_anomaly


def duplicate_trans(transactions, session=None):
    for transaction in transactions:

        db.anomaly_claims.update_many(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"],
                "anomaly_type": "cancelled booking being reclaimed",
                "status": "Unresolved",
                "deleted": False,
            },
            {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )


def negative_funds_trust(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    if round(trust["balance"], 2) < 0:
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
    else:
        db.anomaly_claims.update_many(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"],
                "anomaly_type": anomaly_type,
                "status": "Unresolved",
                "deleted": False,
            },
            {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )
        db.anomaly_banking.update_many(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"],
                "anomaly_type": anomaly_type,
                "status": "Unresolved",
                "deleted": False,
            },
            {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
            collation={"locale": "en", "strength": 1},
            session=session,
        )


def funds_exceed_tbv(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    if (
        trust.get("total_booking_value")
        and transaction.get("element")
        and transaction["element"].casefold() in ["performance", "claim"]
    ):
        difference = trust["total_in_trust"] - trust["total_booking_value"]
        if (round(difference, 2)) != 0:
            return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
        else:
            db.anomaly_claims.update_many(
                {
                    "client_id": transaction["client_id"],
                    "booking_ref": transaction["booking_ref"],
                    "anomaly_type": anomaly_type,
                    "status": "Unresolved",
                    "deleted": False,
                },
                {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
                session=session,
            )


def claim_before_return(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    date_today = datetime.today()
    date_min = datetime.combine(date_today, datetime.min.time())
    if (
        trust_type
        and transaction.get("return_date")
        and transaction.get("element")
        and transaction["element"].casefold() in ["performance", "commission", "returning customers"]
    ):
        if (
            trust_type["name"].casefold() != "Tripartite Tour Op".casefold()
            and (datetime.strptime(transaction["return_date"], "%Y-%m-%d") - date_min).days
            >= client_anomaly["custom_field_value"]
        ):
            return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def duplicate_amount(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    claim_amount = list(
        db.claims_file_details.find(
            {
                "client_id": transaction["client_id"],
                "booking_ref": transaction["booking_ref"],
                "amount": transaction["amount"],
                "element": transaction["element"],
                "deleted": False,
            },
            collation={"locale": "en", "strength": 1},
        )
    )
    if len(claim_amount) > 1:
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def claim_exceed_per_pax(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    if transaction.get("pax_count") and transaction["element"].casefold() in [
        element.casefold() for element in client_anomaly["elements"]
    ]:
        claim_amount = db.claims_file_details.aggregate(
            [
                {
                    "$match": {
                        "client_id": transaction["client_id"],
                        "element": {"$in": client_anomaly["elements"]},
                        "deleted": False,
                        "booking_ref": transaction["booking_ref"],
                    }
                },
                {"$group": {"_id": None, "total_claimed": {"$sum": "$amount"}}},
            ],
            collation={"locale": "en", "strength": 1},
        )
        data = next(claim_amount, None)
        cap_amount = client_anomaly["custom_field_value"]
        total_claimed = data["total_claimed"] if data else 0
        if (total_claimed / transaction["pax_count"]) > cap_amount:
            return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def claim_early_departure_date(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    date_today = datetime.today()
    date_min = datetime.combine(date_today, datetime.min.time())
    if (
        trust_type
        and transaction.get("departure_date")
        and transaction.get("element")
        and transaction["element"].casefold() in ["performance", "balance", "claim"]
        and trust_type["name"].casefold() in ["tripartite tour op", "tripartite ma", "tripartite insurer"]
        and (datetime.strptime(transaction["departure_date"], "%Y-%m-%d") - date_min).days
        > client_anomaly["custom_field_value"]
    ):
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def multi_currency_booking(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    if len(trust["currency_code"]) > 1:
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def insurance_cover_in_date(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    if transaction.get("element"):
        insurance_list = list(db.client_insurance_info.find({"client_id": transaction["client_id"]}))

        if transaction["element"].casefold() in [element.casefold() for element in client_anomaly["elements"]]:
            current_date = datetime.utcnow()

            if not insurance_list:
                return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])

            for insurance in insurance_list:
                if not insurance.get("expiry_date") or insurance["expiry_date"] < current_date:
                    return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def claim_exceeds_apb(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    if (
        trust_type
        and transaction.get("total_booking_value")
        and transaction.get("pax_count")
        and transaction.get("element")
        and (transaction["element"].casefold() == "Deposit".casefold())
    ):
        if (
            trust_type["name"].casefold() == "Tripartite Tour Op".casefold()
            or trust_type["name"].casefold() == "Tripartite MA".casefold()
        ) and (
            transaction["amount"]
            > (transaction["total_booking_value"] / transaction["pax_count"])
            * client_anomaly["custom_field_value"]
            / 100
        ):
            return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def max_cap_anomaly(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    if transaction.get("return_date") and transaction.get("element"):
        date = datetime.today().strftime("%Y-%m-%d")
        if transaction["element"].casefold() in [element.casefold() for element in client_anomaly["elements"]] and (
            transaction["return_date"] >= date
        ):
            from_date = client_anomaly.get("from_date")
            to_date = client_anomaly.get("to_date")

            from_date_str = get_next_date(from_date)
            to_date_str = get_next_date(to_date)
            cursor = db.claims_file_details.aggregate(
                [
                    {
                        "$match": {
                            "client_id": transaction["client_id"],
                            "booking_date": {
                                "$gte": from_date_str,
                                "$lte": to_date_str,
                            },
                            "return_date": {
                                "$gte": date,
                            },
                            "element": {"$in": client_anomaly["elements"]},
                            "deleted": False,
                        }
                    },
                    {"$group": {"_id": None, "total_claimed": {"$sum": "$amount"}}},
                ],
                collation={"locale": "en", "strength": 1},
            )
            data = next(cursor, None)
            cap_amount = client_anomaly["custom_field_value"]
            total_claimed = data["total_claimed"] if data else 0

            db.claims_metadata.update_one(
                {"_id": transaction["claims_id"], "claim_files.file_id": file_id},
                {"$set": {"claim_files.$.max_cap_claim_anomaly_calculated": True}},
            )

            if total_claimed > cap_amount:
                return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
            else:
                db.anomaly_claims.update_many(
                    {
                        "client_id": transaction["client_id"],
                        "anomaly_type": anomaly_type,
                        "status": "Unresolved",
                        "deleted": False,
                    },
                    {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
                    session=session,
                )
                return {"max_cap_claim_anomaly_calculated": True}


def claim_supplier_list(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    if (
        transaction.get("element")
        and transaction.get("supplier_names")
        and transaction["element"].casefold() in ["flight", "flights", "cruise", "bsp"]
    ):
        insurance_list = list(
            db.client_insurance_info.find(
                {"client_id": transaction["client_id"], "supplier_list_file": {"$exists": True}},
                projection={"supplier_list_file": 1, "_id": 0},
            )
        )

        from_date = client_anomaly["from_date"]
        to_date = client_anomaly["to_date"]

        claims_metadata = list(
            db.claims_metadata.aggregate(
                [
                    {
                        "$match": {
                            "$and": [
                                {
                                    "status": {"$nin": ["Cancelled", "Cancelled by System"]},
                                    "client_id": transaction["client_id"],
                                },
                                {"$expr": {"$gte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, from_date]}},
                                {"$expr": {"$lte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, to_date]}},
                            ]
                        },
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "file_id": {"$slice": ["$claim_files.file_id", -1]},
                        },
                    },
                ]
            )
        )

        claims_file_id_list = []
        for claim_file_id in claims_metadata:
            claims_file_id_list.append(claim_file_id["file_id"][0])

        list_file_id = [item["supplier_list_file"]["file_id"] for item in insurance_list]
        supplier_list = list(db.client_files.find({"file_id": {"$in": list_file_id}}))
        transaction_supplier_names = [item.strip().casefold() for item in transaction["supplier_names"].split(",")]
        cursor = db.claims_file_details.aggregate(
            [
                {"$match": {"file_id": {"$in": claims_file_id_list}}},
                {
                    "$addFields": {
                        "supplier_names": {"$split": ["$supplier_names", ","]},
                    }
                },
                {
                    "$addFields": {
                        "supplier_names": {
                            "$map": {
                                "input": "$supplier_names",
                                "in": {"$toLower": {"$trim": {"input": "$$this"}}},
                            }
                        }
                    }
                },
                {"$unwind": "$supplier_names"},
                {
                    "$match": {
                        "client_id": transaction["client_id"],
                        "supplier_names": {"$in": transaction_supplier_names},
                        "element": {"$in": client_anomaly["elements"]},
                        "deleted": False,
                    }
                },
                {"$group": {"_id": "$supplier_names", "total_claimed": {"$sum": "$amount"}}},
            ],
            collation={"locale": "en", "strength": 1},
        )

        for cursor_result in cursor:
            if supplier_list and supplier_list[0].get("supplier_list"):
                supplier_names_in_db = [
                    supplier["supplier_name"].strip().casefold() for supplier in supplier_list[0]["supplier_list"]
                ]
                check = any(supplier_name in supplier_names_in_db for supplier_name in transaction_supplier_names)

                if str(transaction["client_id"]) == os.environ.get("IGLU"):
                    for supplier_name in supplier_list[0]["supplier_list"]:
                        supplier_name_lower = supplier_name["supplier_name"].lower()
                        if cursor_result["_id"].casefold() in ["royal caribbean", "celebrity cruises"]:
                            if supplier_name_lower.casefold() == "rccl (royal caribbean, celebrity cruises)":
                                cursor_result["_id"] = "rccl (royal caribbean, celebrity cruises)"
                                check = True
                        if cursor_result["_id"].casefold() in ["p&o", "princess cruises", "cunard"]:
                            if supplier_name_lower.casefold() == "carnival uk (p&o, princess cruises, cunard)":
                                cursor_result["_id"] = "carnival uk (p&o, princess cruises, cunard)"
                                check = True

                if check is False and insurance_list:
                    return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
                elif check is True:
                    for supplier_name in supplier_list[0]["supplier_list"]:
                        if supplier_name["supplier_name"].casefold() == cursor_result["_id"]:
                            if cursor_result["total_claimed"] >= supplier_name["cap_amount"]:
                                return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def safi_insurance_tracker(
    transaction,
    trust,
    file_id,
    client_anomaly,
    anomaly_type,
    trust_type,
    file_date,
    session=None,
):
    date = datetime.today()
    date_today = datetime.combine(date, datetime.min.time())
    if transaction.get("supplier_names"):
        insurance_list = list(
            db.client_insurance_info.find(
                {"client_id": transaction["client_id"], "supplier_list_file": {"$exists": True}},
                projection={"supplier_list_file": 1, "_id": 0},
            )
        )
        list_file_id = [item["supplier_list_file"]["file_id"] for item in insurance_list]
        supplier_list = list(db.client_files.find({"file_id": {"$in": list_file_id}}))
        filtered_supplier_list = [
            {"supplier_name": item["supplier_name"].casefold(), "cap_amount": item.get("cap_amount")}
            for sublist in supplier_list
            for item in sublist["supplier_list"]
        ]
        transaction_supplier_names = [item.strip().casefold() for item in transaction["supplier_names"].split(",")]
        supplier_names = list(
            filter(lambda x: x["supplier_name"] in transaction_supplier_names, filtered_supplier_list)
        )
        cursor = db.claims_file_details.aggregate(
            [
                {
                    "$addFields": {
                        "return_datetime": {"$dateFromString": {"dateString": "$return_date"}},
                        "supplier_names": {"$split": ["$supplier_names", ","]},
                    }
                },
                {
                    "$addFields": {
                        "supplier_names": {
                            "$map": {"input": "$supplier_names", "in": {"$toLower": {"$trim": {"input": "$$this"}}}}
                        }
                    }
                },
                {"$unwind": "$supplier_names"},
                {
                    "$match": {
                        "client_id": transaction["client_id"],
                        "return_datetime": {
                            "$gte": date_today,
                        },
                        "supplier_names": {"$in": transaction_supplier_names},
                        "deleted": False,
                    }
                },
                {"$group": {"_id": "$supplier_names", "total_claimed": {"$sum": "$amount"}}},
            ],
            collation={"locale": "en", "strength": 1},
        )
        for data in cursor:
            for item in supplier_names:
                if (
                    data["_id"] == item["supplier_name"]
                    and item.get("cap_amount")
                    and (data["total_claimed"] > item["cap_amount"])
                ):

                    return add_claim_anomaly(
                        transaction, file_id, anomaly_type, client_anomaly["_id"], supplier_name=item["supplier_name"]
                    )
                else:
                    db.anomaly_claims.update_many(
                        {
                            "client_id": transaction["client_id"],
                            "anomaly_type": anomaly_type,
                            "status": "Unresolved",
                            "deleted": False,
                            "supplier_name": item["supplier_name"],
                        },
                        {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
                        session=session,
                    )


def commision_claim_anomaly(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    date_today = datetime.today()
    date_min = datetime.combine(date_today, datetime.min.time())
    if (
        transaction.get("departure_date")
        and transaction.get("element")
        and transaction["element"].casefold() == "Commission".casefold()
        and (datetime.strptime(transaction["departure_date"], "%Y-%m-%d") - date_min).days
        >= client_anomaly["custom_field_value"]
        and not transaction["amount"] < 0
    ):
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def cruise_claim_anomaly(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    if trust_type and transaction.get("departure_date") and transaction.get("element"):
        days = client_anomaly["custom_field_value"]
        if transaction["element"].casefold() == "Cruise".casefold() and (
            datetime.strptime(transaction["departure_date"], "%Y-%m-%d") - timedelta(days) > datetime.utcnow()
        ):
            return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def max_claim_exceed(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    if (
        transaction.get("element")
        and transaction["element"].casefold() in [element.casefold() for element in client_anomaly["elements"]]
        and (
            transaction["amount"]
            > ((trust["total_in_trust"]) * client_anomaly["custom_field_value"] / 100)
            - (trust["total_claimed"] - transaction["amount"])
        )
    ):
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def get_claim_exceed_cost_percent_anomaly(transaction):
    logging.info("Getting claim exceed cost percent anomaly")
    claim_exceed_cost_anomaly = os.environ["CLAIM_EXCEEDS_COST_ANOMALY_ID"]
    return db.client_anomaly.find_one(
        {
            "client_id": ObjectId(transaction["client_id"]),
            "anomaly_id": ObjectId(str(claim_exceed_cost_anomaly)),
            "consider_booking_type": True,
        }
    )


def get_total_claimed(transaction, file_id, client_anomaly):
    logging.info("Getting total claimed")
    cursor = db.claims_file_details.aggregate(
        [
            {
                "$match": {
                    "client_id": transaction["client_id"],
                    "booking_ref": transaction["booking_ref"],
                    "element": {"$in": client_anomaly["elements"]},
                    "deleted": False,
                }
            },
            {"$group": {"_id": None, "total_claimed": {"$sum": "$amount"}}},
        ],
        collation={"locale": "en", "strength": 1},
    )
    return next(cursor, None)


def get_total_claimed_for_type(transaction, file_id, client_anomaly, transaction_type):
    logging.info("Getting total claimed for type")
    cursor = db.claims_file_details.aggregate(
        [
            {
                "$match": {
                    "client_id": transaction["client_id"],
                    "type": transaction_type,
                    "booking_ref": transaction["booking_ref"],
                    "element": {"$in": client_anomaly["elements"]},
                    "deleted": False,
                }
            },
            {"$group": {"_id": None, "total_claimed": {"$sum": "$amount"}}},
        ],
        collation={"locale": "en", "strength": 1},
    )
    return next(cursor, None)


def claim_exceed_cost(transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None):
    logging.info("Checking claim exceed cost")
    if not (transaction.get("element") and trust.get("total_booking_value") and trust.get("total_claimed")):
        return

    if transaction["element"].casefold() not in [element.casefold() for element in client_anomaly["elements"]]:
        return

    claim_exceed_cost_percent_anomaly = get_claim_exceed_cost_percent_anomaly(transaction)

    if not claim_exceed_cost_percent_anomaly:
        data = get_total_claimed(transaction, file_id, client_anomaly)
        if data["total_claimed"] > (trust["total_booking_value"] * (client_anomaly["custom_field_value"] / 100)):
            logging.info("Adding claim anomaly")
            return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
    else:
        if transaction["type"] == "Ticketmaster":
            data = get_total_claimed_for_type(transaction, file_id, client_anomaly, "Ticketmaster")
            if data["total_claimed"] >= (
                trust["total_booking_value"] * (claim_exceed_cost_percent_anomaly["ticket_master_limit_value"] / 100)
            ):
                logging.info("Adding claim anomaly")
                return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
        elif transaction["type"] == "Direct":
            data = get_total_claimed_for_type(transaction, file_id, client_anomaly, "Direct")
            if data["total_claimed"] >= (
                trust["total_booking_value"] * (claim_exceed_cost_percent_anomaly["direct_limit_value"] / 100)
            ):
                logging.info("Adding claim anomaly")
                return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def claim_before_t_plus_x_days(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    claim_date = (
        datetime.strptime(file_date, "%Y-%m-%d") - timedelta(days=client_anomaly["custom_field_value"])
    ).strftime("%Y-%m-%d")

    data = db.banking_file_details.find_one(
        {
            "currency_code": transaction["currency_code"],
            "client_id": transaction["client_id"],
            "booking_ref": transaction["booking_ref"],
            "deleted": False,
            "payment_date": {"$gte": claim_date},
        },
    )
    if data is not None:
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def claim_before_x_days_from_booking(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    claim_date = datetime.strptime(file_date, "%Y-%m-%d")
    booking_date = datetime.strptime(trust["booking_date"], "%Y-%m-%d")

    if (
        transaction.get("element")
        and transaction["element"].casefold() in [element.casefold() for element in client_anomaly["elements"]]
        and (claim_date < (booking_date + timedelta(days=client_anomaly["custom_field_value"])))
    ):
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def max_cap_limit_anomaly(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    if transaction.get("element") and transaction["element"].casefold() in [
        element.casefold() for element in client_anomaly["elements"]
    ]:
        if (
            client_anomaly.get("custom_field_value") != 0
            and client_anomaly.get("to_date")
            and "".join(client_anomaly.get("insurance_name").split()).lower() != "lcfbycorpcard"
        ):

            from_date = client_anomaly.get("from_date")
            to_date = client_anomaly.get("to_date")

            claims_metadata = list(
                db.claims_metadata.aggregate(
                    [
                        {
                            "$match": {
                                "$and": [
                                    {
                                        "client_id": transaction["client_id"],
                                    },
                                    {"$expr": {"$gte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, from_date]}},
                                    {"$expr": {"$lte": [{"$arrayElemAt": ["$claim_files.file_date", -1]}, to_date]}},
                                ]
                            },
                        },
                        {
                            "$project": {
                                "_id": 0,
                                "file_id": {"$slice": ["$claim_files.file_id", -1]},
                            },
                        },
                    ]
                )
            )

            claims_file_id_list = []
            for claim_file_id in claims_metadata:
                claims_file_id_list.append(claim_file_id["file_id"][0])
            cursor = db.claims_file_details.aggregate(
                [
                    {"$match": {"file_id": {"$in": claims_file_id_list}}},
                    {
                        "$match": {
                            "client_id": transaction["client_id"],
                            "element": {"$in": client_anomaly["elements"]},
                            "deleted": False,
                        }
                    },
                    {"$group": {"_id": None, "total_claimed": {"$sum": "$amount"}}},
                ],
                collation={"locale": "en", "strength": 1},
            )
            data = next(cursor, None)
            cap_amount = client_anomaly["custom_field_value"]
            total_claimed = data["total_claimed"] if data else 0
            db.claims_metadata.update_one(
                {"_id": transaction["claims_id"], "claim_files.file_id": file_id},
                {"$set": {"claim_files.$.max_cap_limit_anomaly_calculated": True}},
            )
            if total_claimed > cap_amount:
                return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])
            else:
                db.anomaly_claims.update_many(
                    {
                        "client_id": transaction["client_id"],
                        "anomaly_type": anomaly_type,
                        "status": "Unresolved",
                        "deleted": False,
                    },
                    {"$set": {"status": "Resolved", "updated_at": datetime.utcnow()}},
                    session=session,
                )
                return {"max_cap_limit_anomaly_calculated": True}


def departure_within_x_days_from_booking(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, file_date, session=None
):
    departure_date = datetime.strptime(trust["departure_date"], "%Y-%m-%d")
    booking_date = datetime.strptime(trust["booking_date"], "%Y-%m-%d")

    if (
        transaction.get("element")
        and transaction["element"].casefold() in [element.casefold() for element in client_anomaly["elements"]]
        and (departure_date > (booking_date + timedelta(days=client_anomaly["custom_field_value"])))
    ):
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def ommitted_claim_transaction(
    transaction, trust, file_id, client_anomaly, anomaly_type, client_id, trust_type, file_date, ommitted_ids
):

    if transaction["booking_ref"] in ommitted_ids:
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def cancelled_claim_transaction(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, client_id, file_date, cancelled_ids
):

    if transaction["booking_ref"] in cancelled_ids:
        return add_claim_anomaly(transaction, file_id, anomaly_type, client_anomaly["_id"])


def reclaimed_removed_booking(
    transaction, trust, file_id, client_anomaly, anomaly_type, trust_type, client_id, file_date, session=None
):
    """
    Check if the booking reference was previously removed/cancelled and is now being claimed again.
    Excludes Performance, Refund, and Cancellation elements.
    """
    excluded_elements = ["performance", "refund", "cancellation"]
    element = transaction.get("element", "").lower().strip()
    
    # Skip check for excluded elements
    if element in excluded_elements:
        return None
    
    # Check if this booking reference was previously removed and not yet expired
    removed_booking = db.removed_booking_refs.find_one(
        {
            "client_id": ObjectId(client_id),
            "booking_ref": transaction["booking_ref"],
            "expired": False,
            "$or": [
                {"return_date": {"$gte": datetime.now().strftime("%Y-%m-%d")}},
                {"return_date": {"$exists": False}},
                {"return_date": None}
            ]
        }
    )
    
    if removed_booking:
        # Log the detection
        print(f"Detected re-claim of removed booking: {transaction['booking_ref']} "
              f"for client {client_id}, originally removed from file {removed_booking['file_id']}")
        
        return add_claim_anomaly(
            transaction, 
            file_id, 
            anomaly_type, 
            client_anomaly["_id"],
            original_file_id=removed_booking.get("file_id"),
            removal_date=removed_booking.get("removal_date"),
            removal_reason=removed_booking.get("reason")
        )
    
    return None


def lambda_handler(event, context):
    anomaly_type_dict = {
        "Negative Funds in Trust": negative_funds_trust,
        "Funds in Trust Exceed/less than Total Booking Value": funds_exceed_tbv,
        "Claim Performance Before Return": claim_before_return,
        "Duplicate": duplicate_amount,
        "Claim Too Early For Departure Date": claim_early_departure_date,
        "Claim Exceeds Cost Per Pax": claim_exceed_per_pax,
        "Multi-currency Booking": multi_currency_booking,
        "Insurance Cover In Date": insurance_cover_in_date,
        "Claim Exceeds agreed % per booking": claim_exceeds_apb,
        "Max Cap Claim Anomaly (Rollover)": max_cap_anomaly,
        "Claim SupplierList Anomaly": claim_supplier_list,
        "Commission Claim Anomaly": commision_claim_anomaly,
        "Cruise Claim Anomaly": cruise_claim_anomaly,
        "Max Claim % Exceeded": max_claim_exceed,
        "SAFI Insurance Tracker": safi_insurance_tracker,
        "Claim Exceeds Cost %": claim_exceed_cost,
        "Claim before t+x days": claim_before_t_plus_x_days,
        "Claim before x days from booking": claim_before_x_days_from_booking,
        "Max Cap Limit Anomaly": max_cap_limit_anomaly,
        "Departure Within x Days From Booking Date": departure_within_x_days_from_booking,
        "ommitted transactions being reclaimed": ommitted_claim_transaction,
        "cancelled transactions being reused": cancelled_claim_transaction,
        "Re-claiming Removed Booking": reclaimed_removed_booking,
    }
    data_key, batch_no = (event.get("dataKey"), event.get("batchNo"))

    if not event.get("transactions"):
        s3 = boto3.client("s3")
        data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=data_key)
        data = json.loads(data_object["Body"].read().decode("utf-8"))
        client_id = data["clientId"]
        file_id = data["fileId"]
        sftp = data["sftp"]
        sftp_key = data["sftpKey"]
        transactions = data["transactionIds"][batch_no * BATCH_SIZE : batch_no * BATCH_SIZE + BATCH_SIZE]
    else:
        transactions = event["transactions"]

    transaction_ids = [ObjectId(transaction) for transaction in transactions]
    claims_file_details = list(db.claims_file_details.find({"_id": {"$in": transaction_ids}}))
    booking_refs = [transaction["booking_ref"] for transaction in claims_file_details]

    claims_metadata = db.claims_metadata.find_one(
        {"client_id": ObjectId(client_id), "claim_files": {"$elemMatch": {"file_id": file_id}}}
    )

    previous_claims_transactions = list(
        db.claims_file_details.find(
            {"claims_id": claims_metadata["_id"], "deleted": False},
            projection={"_id": {"$toString": "$_id"}, "booking_ref": 1, "amount": 1, "payment_type": 1},
        )
    )
    previous_booking_refs = [transaction["booking_ref"] for transaction in previous_claims_transactions]
    ommitted_ids = list(filter(lambda x:x in booking_refs , previous_booking_refs))
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    trust_funds = list(
        db[collection_name].find(
            {"client_id": ObjectId(client_id), "booking_ref": {"$in": booking_refs}},
            collation={"locale": "en", "strength": 1},
        )
    )
    trust_fund_mapper = {trust_fund["booking_ref"].casefold(): trust_fund for trust_fund in trust_funds}

    claims_metadata_cancelled = db.claims_metadata.find_one({"client_id": ObjectId(client_id)})

    cancelled_claims_transactions = list(
        db.claims_file_details.find(
            {"claims_id": claims_metadata_cancelled["_id"], "deleted": True, "status": "Cancelled"},
            projection={"_id": {"$toString": "$_id"}, "booking_ref": 1, "amount": 1, "payment_type": 1},
        )
    )
    print("Cancelled Claims Transactions:", cancelled_claims_transactions)
    cancelled_booking_refs = [transaction["booking_ref"] for transaction in cancelled_claims_transactions]
    print("Cancelled Booking Refs:", cancelled_booking_refs)
    cancelled_ids = list(filter(lambda x: x in booking_refs, cancelled_booking_refs))
    print("Cancelled IDs:", cancelled_ids)
    print("Claims Metadata Cancelled:", claims_metadata_cancelled)

    client_claim_anomalies = list(
        db.client_anomaly.aggregate(
            [
                {
                    "$lookup": {
                        "from": "lookup_anomaly",
                        "localField": "anomaly_id",
                        "foreignField": "_id",
                        "as": "lookup_anomaly",
                    }
                },
                {"$unwind": "$lookup_anomaly"},
                {
                    "$match": {
                        "client_id": ObjectId(client_id),
                        "lookup_anomaly.name": {"$in": list(anomaly_type_dict.keys())},
                    }
                },
                {
                    "$project": {
                        "name": "$lookup_anomaly.name",
                        "custom_field_value": 1,
                        "elements": 1,
                        "from_date": 1,
                        "to_date": 1,
                        "insurance_name": {"$ifNull": ["$name", ""]},
                    }
                },
            ]
        )
    )
    print("Client Claim Anomalies:", client_claim_anomalies)
    client_basic_info = db.client_basic_info.find_one({"_id": ObjectId(client_id)})
    trust_type = db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    claims_metadata = db.claims_metadata.find_one(
        {"client_id": ObjectId(client_id), "claim_files": {"$elemMatch": {"file_id": file_id}}}
    )
    file_date = claims_metadata["claim_files"][-1]["file_date"]

    claim_anomalies = []
    for transaction in claims_file_details:
        trust = trust_fund_mapper[transaction["booking_ref"].casefold()]
        for anomaly in client_claim_anomalies:
            anomaly_function = anomaly_type_dict[anomaly["name"]]
            """
            when we use maxcap anomaly 'addmore' feature its not raise multiple elements anomaly due to this condition
            """
            if (
                #     anomaly_function == max_cap_anomaly
                #     and claims_metadata["claim_files"][-1].get("max_cap_claim_anomaly_calculated") is True
                # ) or (
                anomaly_function == max_cap_limit_anomaly
                and claims_metadata["claim_files"][-1].get("max_cap_limit_anomaly_calculated") is True
            ):
                continue

            if anomaly_function == ommitted_claim_transaction:
                anomaly_info = anomaly_function(
                    transaction,
                    trust,
                    file_id,
                    anomaly,
                    anomaly["name"],
                    trust_type,
                    file_date,
                    client_id,
                    ommitted_ids,
                )
            elif anomaly_function == cancelled_claim_transaction:
                anomaly_info = anomaly_function(
                    transaction,
                    trust,
                    file_id,
                    anomaly,
                    anomaly["name"],
                    trust_type,
                    file_date,
                    client_id,
                    cancelled_ids,
                )
            elif anomaly_function == reclaimed_removed_booking:
                anomaly_info = anomaly_function(
                    transaction,
                    trust,
                    file_id,
                    anomaly,
                    anomaly["name"],
                    trust_type,
                    client_id,
                    file_date,
                )
            else:
                anomaly_info = anomaly_function(
                    transaction, trust, file_id, anomaly, anomaly["name"], trust_type, file_date
                )

            if anomaly_function == max_cap_anomaly and anomaly_info:
                claims_metadata["claim_files"][-1]["max_cap_claim_anomaly_calculated"] = True
            if anomaly_function == max_cap_limit_anomaly and anomaly_info:
                claims_metadata["claim_files"][-1]["max_cap_limit_anomaly_calculated"] = True
            if anomaly_info and "anomaly_type" in anomaly_info:
                claim_anomalies.append(anomaly_info)

    if claim_anomalies:
        db.anomaly_claims.insert_many(claim_anomalies)
    return {"clientId": client_id, "fileId": file_id, "sftp": sftp, "sftpKey": sftp_key}
