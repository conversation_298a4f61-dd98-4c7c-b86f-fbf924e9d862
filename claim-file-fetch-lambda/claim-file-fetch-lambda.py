import json
from math import ceil
import os
from pymongo import MongoClient
from helpers.secret_manager_handler import get_secret
import boto3
from bson import ObjectId

STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    client_id = event["clientId"]
    file_id = event["fileId"]
    s3 = boto3.client("s3")

    claims_metadata = db.claims_metadata.find_one(
        {"client_id": ObjectId(client_id), "claim_files": {"$elemMatch": {"file_id": file_id}}}
    )
    previous_claims_transactions = list(
        db.claims_file_details.find(
            {"claims_id": claims_metadata["_id"], "deleted": False},
            projection={"_id": {"$toString": "$_id"}, "booking_ref": 1, "amount": 1, "payment_type": 1},
        )
    )
    for i in range(ceil(len(previous_claims_transactions) / BATCH_SIZE)):
        data = {**event, "transactions": previous_claims_transactions[i * BATCH_SIZE : i * BATCH_SIZE + BATCH_SIZE]}
        data_key = f"claim/file-fetch/{file_id}/{i}.json"
        s3.put_object(
            Body=json.dumps(data),
            Bucket=STATE_MACHINE_PAYLOAD_BUCKET,
            Key=data_key,
        )

    response = {**event, "bucket": STATE_MACHINE_PAYLOAD_BUCKET, "prefix": f"claim/file-fetch/{file_id}/"}
    return response
