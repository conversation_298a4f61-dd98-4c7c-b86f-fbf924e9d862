from collections import OrderedDict
import csv
import os
from pymongo import MongoClient
from datetime import datetime, timedelta
from sheet2dict import Worksheet
import boto3
from helpers import date_format, generate_csv_report, generate_excel_report
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
import tempfile
from helpers import round

TEMP_DIR = tempfile.gettempdir()
NAS = os.environ.get("NAS")
FLYPOP = os.environ.get("FLYPOP")
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    client_id = event["clientId"]
    file_id = event["fileId"]
    file_type = event["fileType"]
    s3 = boto3.client("s3")
    ws = Worksheet()
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"

    file_name = None
    if file_type == "text/csv":
        file_name = f"{file_id}.csv"
        s3.download_file(os.environ["CLAIM_FILE_BUCKET"], event["fileId"], f"{TEMP_DIR}/{file_name}")
        csv_file = open(f"{TEMP_DIR}/{file_name}", "r", encoding="utf-8")
        temp_lines = csv_file.readline() + "\n" + csv_file.readline()
        dialect = csv.Sniffer().sniff(temp_lines, delimiters=",~")
        csv_file.seek(0)

        dict_reader = csv.DictReader(csv_file, dialect=dialect)
        data_items = list(dict_reader)
    else:
        file_name = f"{file_id}.xlsx"
        s3.download_file(os.environ["CLAIM_FILE_BUCKET"], event["fileId"], f"{TEMP_DIR}/{file_name}")
        ws.xlsx_to_dict(path=f"{TEMP_DIR}/{file_name}")
        data_items = ws.sanitize_sheet_items

    for item in data_items:
        booking_dates = {}
        if item.get("Booking Date") and item.get("Booking Date") != "None":
            booking_dates["booking_date"] = date_format(item["Booking Date"])
        if item.get("Date of Travel") and item.get("Date of Travel") != "None":
            booking_dates["departure_date"] = date_format(item["Date of Travel"])
        if item.get("Date of Return") and item.get("Date of Return") != "None":
            booking_dates["return_date"] = date_format(item["Date of Return"])
        db[collection_name].update_one(
            {"client_id": ObjectId(client_id), "booking_ref": item["Booking Ref"]},
            {"$set": {"booking_status": "Rebooked", **booking_dates}},
            collation={"locale": "en", "strength": 1},
        )

    anomaly_name = "Claim Too Early For Departure Date"
    lookup_anomaly = db.lookup_anomaly.find_one({"name": anomaly_name})
    client_anomaly = db.client_anomaly.find_one({"client_id": ObjectId(client_id), "anomaly_id": lookup_anomaly["_id"]})
    days = client_anomaly["custom_field_value"]

    client_basic_info = db.client_basic_info.find_one({"_id": ObjectId(client_id)})
    if client_basic_info.get("new_workflow"):
        header_dict, claim_items = new_workflow(client_id, days)
    else:
        header_dict, claim_items = older_workflow(client_id, days)

    if file_type == "text/csv":
        generate_csv_report(header_dict, claim_items, file_name)
    else:
        generate_excel_report(header_dict, claim_items, file_name)
    s3.upload_file(f"{TEMP_DIR}/{file_name}", os.environ["CLAIM_FILE_BUCKET"], file_id)
    return event


def new_workflow(client_id, days):
    date_today = datetime.today()
    date_min = datetime.combine(date_today, datetime.min.time())
    claim_items = []
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    trust_funds = db[collection_name].aggregate(
        [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}],
                }
            },
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "localField": "booking_ref",
                    "foreignField": "booking_ref",
                    "pipeline": [
                        {"$match": {"client_id": ObjectId(client_id), "deleted": False}},
                        {
                            "$project": {
                                "amount": 1,
                                "type": {"$ifNull": ["$type", None]},
                                "supplierNames": {"$ifNull": ["$supplier_names", None]},
                            }
                        },
                    ],
                    "as": "bank",
                }
            },
            {
                "$lookup": {
                    "from": "claims_file_details",
                    "localField": "booking_ref",
                    "foreignField": "booking_ref",
                    "pipeline": [
                        {"$match": {"client_id": ObjectId(client_id), "deleted": False}},
                    ],
                    "as": "claim",
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "booking_ref": 1,
                    "pax_count": {"$toInt": {"$ifNull": ["$pax_count", 0]}},
                    "lead_pax": 1,
                    "total_booking_value": {"$ifNull": ["$total_booking_value", 0.0]},
                    "bonding": 1,
                    "type": {"$ifNull": ["$bank.type", None]},
                    "booking_date": 1,
                    "departure_date": 1,
                    "return_date": 1,
                    "booking_status": {"$ifNull": ["$booking_status", "Active"]},
                    "currency": {"$last": "$currency_code"},
                    "element": "Performance",
                    "payment_date": (datetime.now()).strftime("%Y-%m-%d"),
                    "dept_bal_lead_time": {"$toInt": {"$ifNull": ["$dept_bal_lead_time", 0]}},
                    "banking_amount": "$bank.amount",
                    "supplierNames": {"$ifNull": ["$bank.supplierNames", None]},
                    "claim_amount": "$claim.amount",
                }
            },
            {
                "$unionWith": {
                    "coll": "banking_metadata",
                    "pipeline": [
                        {
                            "$match": {
                                "client_id": ObjectId(client_id),
                                "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                            },
                        },
                        {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                        {"$sort": {"recent_file.submitted_date": -1}},
                        {"$limit": 1},
                        {
                            "$project": {
                                "_id": 0,
                                "file_id": "$recent_file.file_id",
                                "submitted_date": "$recent_file.submitted_date",
                            }
                        },
                        {
                            "$lookup": {
                                "from": "claims_metadata",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "client_id": ObjectId(client_id),
                                            "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                        },
                                    },
                                    {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                                    {"$sort": {"recent_file.submitted_date": -1}},
                                    {"$limit": 1},
                                    {
                                        "$project": {
                                            "_id": 0,
                                            "file_id": "$recent_file.file_id",
                                            "submitted_date": "$recent_file.submitted_date",
                                        }
                                    },
                                ],
                                "as": "claim",
                            }
                        },
                        {"$unwind": "$claim"},
                        {"$match": {"$expr": {"$lt": ["$claim.submitted_date", "$submitted_date"]}}},
                        {
                            "$lookup": {
                                "from": "banking_file_details",
                                "localField": "file_id",
                                "foreignField": "file_id",
                                "pipeline": [
                                    {"$match": {"deleted": False}},
                                    {
                                        "$group": {
                                            "_id": "$booking_ref",
                                            "client_id": {"$first": "$client_id"},
                                        }
                                    },
                                    {"$project": {"client_id": 1, "booking_ref": "$_id"}},
                                ],
                                "as": "bank",
                            }
                        },
                        {"$unwind": "$bank"},
                        {"$project": {"booking_ref": "$bank.booking_ref"}},
                        {
                            "$lookup": {
                                "from": "trust_fund_v2",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "client_id": ObjectId(client_id),
                                            "$and": [{"balance": {"$lt": 0.005}}, {"balance": {"$gt": -0.005}}],
                                        }
                                    },
                                ],
                                "as": "trust_fund",
                            }
                        },
                        {"$unwind": "$trust_fund"},
                        {
                            "$lookup": {
                                "from": "banking_file_details",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "client_id": ObjectId(client_id),
                                            "deleted": False,
                                        }
                                    },
                                    {"$sort": {"created_at": 1}},
                                    {
                                        "$project": {
                                            "amount": 1,
                                            "type": {"$ifNull": ["$type", None]},
                                            "supplierNames": {"$ifNull": ["$supplier_names", None]},
                                        }
                                    },
                                ],
                                "as": "bank",
                            }
                        },
                        {
                            "$lookup": {
                                "from": "claims_file_details",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "client_id": ObjectId(client_id),
                                            "deleted": False,
                                        }
                                    },
                                    {"$sort": {"created_at": 1}},
                                ],
                                "as": "claim",
                            }
                        },
                        {
                            "$project": {
                                "_id": 0,
                                "booking_ref": "$trust_fund.booking_ref",
                                "pax_count": {"$toInt": {"$ifNull": ["$trust_fund.pax_count", 0]}},
                                "lead_pax": "$trust_fund.lead_pax",
                                "total_booking_value": {"$ifNull": ["$trust_fund.total_booking_value", 0.0]},
                                "bonding": "$trust_fund.bonding",
                                "type": "$bank.type",
                                "booking_date": "$trust_fund.booking_date",
                                "departure_date": "$trust_fund.departure_date",
                                "return_date": "$trust_fund.return_date",
                                "payment_date": (datetime.now()).strftime("%Y-%m-%d"),
                                "booking_status": {"$ifNull": ["$trust_fund.booking_status", "Active"]},
                                "banking_amount": "$bank.amount",
                                "supplierNames": "$bank.supplierNames",
                                "claim_amount": "$claim.amount",
                                "currency": {"$last": "$trust_fund.currency_code"},
                                "element": "Performance",
                                "dept_bal_lead_time": {"$toInt": {"$ifNull": ["$trust_fund.dept_bal_lead_time", 0]}},
                            }
                        },
                    ],
                }
            },
        ],
        collation={"locale": "en", "strength": 1},
        allowDiskUse=True,
    )

    for trust_fund in trust_funds:
        supplier_list = trust_fund["supplierNames"]
        type_list = trust_fund["type"]
        payment_list = trust_fund["banking_amount"]
        claim_list = trust_fund["claim_amount"]
        positive_claim_total = 0
        negative_claim_total = 0
        for amount in claim_list:
            if amount > 0:
                positive_claim_total += amount
            else:
                negative_claim_total += amount
        for i, payment in enumerate(payment_list):
            if payment_list[i] > 0 and positive_claim_total > 0:
                payment_list[i] = max(payment - positive_claim_total, 0)
                positive_claim_total = max(positive_claim_total - payment, 0)
            elif payment_list[i] < 0 and negative_claim_total < 0:
                payment_list[i] = min(payment - negative_claim_total, 0)
                negative_claim_total = min(negative_claim_total - payment, 0)

        indexes = [index for index, item in enumerate(payment_list) if round(item) != 0.0]
        payment_list = [payment_list[i] for i in indexes]
        supplier_list = [supplier_list[i] for i in indexes]
        type_list = [type_list[i] for i in indexes]

        positive_payment_list = []
        negative_payment_list = []
        positive_supplier_list = []
        negative_supplier_list = []
        positive_type_list = []
        negative_type_list = []
        for i, amount in enumerate(payment_list):
            if amount > 0:
                positive_payment_list.append(amount)
                positive_supplier_list.append(supplier_list[i])
                positive_type_list.append(type_list[i])
            else:
                negative_payment_list.append(amount)
                negative_supplier_list.append(supplier_list[i])
                negative_type_list.append(type_list[i])
        amount_to_be_claimed = []
        supplier_to_be_added = []
        type_to_be_added = []
        if (
            trust_fund["booking_status"].casefold() in ("active", "live", "rebooked")
            and trust_fund["departure_date"]
            and (datetime.strptime(trust_fund["departure_date"], "%Y-%m-%d") - date_min) <= timedelta(days)
        ):
            amount_to_be_claimed = payment_list
            supplier_to_be_added = supplier_list
            type_to_be_added = type_list
        else:
            remaining_positive_payment_list = positive_payment_list.copy()
            for i, amount in enumerate(negative_payment_list):
                for j, payment in enumerate(remaining_positive_payment_list):
                    remaining_positive_payment_list[j] = max(payment + amount, 0)
                    amount = min(amount + payment, 0)
                    if amount == 0:
                        break
            for i in range(len(positive_payment_list)):
                if positive_payment_list[i] != remaining_positive_payment_list[i]:
                    amount_to_be_claimed.append(positive_payment_list[i] - remaining_positive_payment_list[i])
                    supplier_to_be_added.append(positive_supplier_list[i])
                    type_to_be_added.append(positive_type_list[i])
            amount_to_be_claimed.extend(negative_payment_list)
            supplier_to_be_added.extend(negative_supplier_list)
            type_to_be_added.extend(negative_type_list)

        for i, amount in enumerate(amount_to_be_claimed):
            supplierName = supplier_to_be_added[i] if supplier_to_be_added[i] is not None else None
            type = type_to_be_added[i] if type_to_be_added[i] is not None else None
            claim_items.append({**trust_fund, "amount": amount, "supplierName": supplierName, "type": type})

    header_dict = OrderedDict(
        [
            ("booking_ref", "BookingRef"),
            ("lead_pax", "LeadPax"),
            ("pax_count", "Pax"),
            ("booking_date", "BookingDate"),
            ("departure_date", "DepartureDate"),
            ("return_date", "ReturnDate"),
            ("supplier_ref", "SupplierRef"),
            ("supplierName", "SupplierNames"),
            ("element", "Element"),
            ("currency", "Currency"),
            ("amount", "Amount"),
            ("booking_type", "BookingType"),
            ("bonding", "Bonding"),
            ("payment_type", "PaymentType"),
            ("total_booking_value", "TotalBookingValue"),
            ("days_to_process", "DaysToProcess"),
            ("dept_bal_lead_time", "DeptBalLeadTime"),
            ("payment_date", "PaymentDate"),
            ("booking_status", "BookingStatus"),
            ("type", "Type"),
        ]
    )

    return header_dict, claim_items


def older_workflow(client_id, days):
    date_today = datetime.today()
    date_min = datetime.combine(date_today, datetime.min.time())
    claim_items = []

    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    trust_funds = db[collection_name].aggregate(

        [
            {
                "$match": {
                    "client_id": ObjectId(client_id),
                    "$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}],
                }
            },
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "localField": "booking_ref",
                    "foreignField": "booking_ref",
                    "pipeline": [
                        {"$match": {"client_id": ObjectId(client_id), "deleted": False}},
                        {"$sort": {"created_at": 1}},
                        {"$project": {"currency_code": 1}},
                    ],
                    "as": "bank",
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "booking_ref": 1,
                    "pax_count": {"$toInt": {"$ifNull": ["$pax_count", 0]}},
                    "lead_pax": 1,
                    "total_booking_value": {"$ifNull": ["$total_booking_value", 0.0]},
                    "bonding": 1,
                    "booking_date": 1,
                    "departure_date": 1,
                    "return_date": 1,
                    "booking_status": {"$ifNull": ["$booking_status", "Active"]},
                    "balance": 1,
                    "currency": {"$arrayElemAt": ["$bank.currency_code", -1]},
                    "element": "Performance",
                    "payment_date": (datetime.now()).strftime("%Y-%m-%d"),
                    "type": 1,
                    "dept_bal_lead_time": {"$toInt": {"$ifNull": ["$dept_bal_lead_time", 0]}},
                }
            },
        ],
        collation={"locale": "en", "strength": 1},
        allowDiskUse=True,
    )

    for trust_fund in trust_funds:
        if (
            trust_fund["balance"] > 0
            and trust_fund.get("departure_date")
            and (
                trust_fund["booking_status"].casefold() not in ("active", "live", "rebooked")
                or (datetime.strptime(trust_fund["departure_date"], "%Y-%m-%d") - date_min) > timedelta(days)
            )
        ):
            continue
        claim_items.append(trust_fund)

    header_dict = OrderedDict(
        [
            ("booking_ref", "BookingRef"),
            ("lead_pax", "LeadPax"),
            ("pax_count", "Pax"),
            ("booking_date", "BookingDate"),
            ("departure_date", "DepartureDate"),
            ("return_date", "ReturnDate"),
            ("supplier_ref", "SupplierRef"),
            ("supplier_name", "SupplierNames"),
            ("element", "Element"),
            ("currency", "Currency"),
            ("balance", "Amount"),
            ("booking_type", "BookingType"),
            ("bonding", "Bonding"),
            ("payment_type", "PaymentType"),
            ("type", "Type"),
            ("total_booking_value", "TotalBookingValue"),
            ("days_to_process", "DaysToProcess"),
            ("dept_bal_lead_time", "DeptBalLeadTime"),
            ("payment_date", "PaymentDate"),
            ("booking_status", "BookingStatus"),
        ]
    )

    return header_dict, claim_items
