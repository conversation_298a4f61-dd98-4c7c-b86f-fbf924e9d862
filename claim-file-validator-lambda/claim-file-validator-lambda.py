import csv
import json
import logging
import os
from typing import Any
from sheet2dict import Worksheet
import boto3
from pymongo import MongoClient
from datetime import datetime
import calendar
from math import ceil
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
import tempfile

TEMP_DIR = tempfile.gettempdir()
STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def custom_filter(value: str) -> str:
    data = value.casefold()
    data = "".join(data.split())
    return data


def try_parsing_date(value: str, data_type: str, file_id: str, key: str, row_count, client_id) -> str:
    for date in (
        "%Y-%m-%d",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y/%m/%d",
        "%d-%m-%Y",
        "%d-%m-%Y %H:%M:%S",
        "%d-%m-%Y %H:%M:%S.%f",
        "%Y/%m/%d %H:%M:%S %p",
        "%m/%d/%Y %H:%M:%S %p",
        "%m/%d/%Y" if client_id == os.environ.get("GTL") else "%d/%m/%Y",
    ):
        try:
            return datetime.strptime(value, date).strftime("%Y-%m-%d")
        except ValueError:
            pass
    cancel_claims_file(
        file_id,
        f"The uploaded claim file contains an error in '{key}' column for the given value '{value}' at row {row_count}. Expecting a valid {data_type}.",
    )
    raise ValueError(
        f"The uploaded claim file contains an error in '{key}' column for the given value '{value}' at row {row_count}. Expecting a valid {data_type}."
    )


def validate_type(value: str, data_type: str, file_id: str, key: str, row_count, client_id) -> Any:
    if data_type == "string" and isinstance(value, str):
        return value
    elif data_type == "number" and value is not None:
        try:
            return float(value)
        except ValueError:
            logging.error("Invalid value, expected a number.")
            pass
    elif data_type == "alphanumeric" and value is not None and value.replace(" ", "").isalnum():
        return value
    elif data_type == "date" and value is not None:
        return try_parsing_date(value, data_type, file_id, key, row_count, client_id)
    elif (
        data_type == "month"
        and value is not None
        and value.casefold() in map(lambda x: x.casefold(), calendar.month_name[1:])
    ):
        return value
    elif (
        data_type == "boolean"
        and value is not None
        and (value.lower() == "yes" or value.lower() == "true" or value.lower() == "y")
    ):
        return True
    elif (
        data_type == "boolean"
        and value is not None
        and (value.lower() == "no" or value.lower() == "false" or value.lower() == "n")
    ):
        return False

    cancel_claims_file(
        file_id,
        f"The uploaded claim file contains an error in '{key}' column for the given value '{value}' at row {row_count}. expecting a valid {data_type}",
    )
    raise Exception(
        f"The uploaded claim file contains an error in '{key}' column for the given value '{value}' at row {row_count}. expecting a valid {data_type}"
    )


def cancel_claims_file(file_id, error_msg):
    claims_metadata = db.claims_metadata.find_one({"claim_files": {"$elemMatch": {"file_id": file_id}}})
    claim_files = claims_metadata["claim_files"]
    claim_files[-1] = {**claim_files[-1], "status": "Cancelled by System", "notes": error_msg}
    db.claims_metadata.update_one(
        {"claim_files": {"$elemMatch": {"file_id": file_id}}},
        {"$set": {"status": "Cancelled by System", "claim_files": claim_files}},
    )


def currency_validation(currency_list: list, value: str, file_id: str) -> str:
    if value not in currency_list:
        cancel_claims_file(
            file_id,
            "invalid currency",
        )
        raise Exception("Invalid currency format")
    return value


def custom_data_modifier_broadway(data, initial_row_count):
    element_dict = {}
    data_list = []
    lookup_claim_elements = list(db.lookup_claim_elements.find())
    claim_elements = [item["name"] for item in lookup_claim_elements]
    
    for row in data:
        element_lis = []
        initial_row_count += 1
        for key, original_value in row.items():
            # Strip whitespace if the value is not None
            value = original_value.strip() if original_value is not None else ""
            
            try:
                numeric_value = float(value) if value not in ["None", ""] else 0
            except ValueError:
                numeric_value = 0

            # Use the cleaned-up value instead of the original row[key]
            if key in claim_elements and value not in ["None", ""] and numeric_value != 0:
                element_dict["element"] = key
                element_dict["Amount"] = value
                element_dict["initial_row_count"] = initial_row_count
                element_lis.append(element_dict.copy())
        
        # Remove the claim element keys from the row
        for item in lookup_claim_elements:
            row.pop(item["name"], None)
        
        row_list = []
        for item in element_lis:
            row.update(item)
            row_list.append(row.copy())
        
        data_list.extend(row_list)
    
    return data_list


def lambda_handler(event, context):
    state = event.get("state")
    client_id = event.get("clientId")
    file_id = event.get("fileId")
    file_type = event.get("fileType")
    s3 = boto3.client("s3")
    ws = Worksheet()

    if state == "extract":
        if file_type == "text/csv":
            file_name = f"{file_id}.csv"
            s3.download_file(os.environ["CLAIM_FILE_BUCKET"], event["fileId"], f"{TEMP_DIR}/{file_name}")
            try:
                csv_file = open(f"{TEMP_DIR}/{file_name}", "r", encoding="utf-8")
            except (IOError, OSError) as err:
                cancel_claims_file(
                    file_id,
                    str(err),
                )
                raise err
            first_line = csv_file.readline()
            if not first_line or not first_line.strip():
                cancel_claims_file(
                    file_id,
                    "The uploaded csv file is empty",
                )
                raise Exception("The uploaded csv file is empty")
            second_line = csv_file.readline()
            if not second_line or not second_line.strip():
                cancel_claims_file(
                    file_id,
                    "The uploaded csv file has empty rows",
                )
                raise Exception("The uploaded csv file has empty rows")
            csv_file.seek(0)
            temp_lines = csv_file.readline() + "\n" + csv_file.readline()
            dialect = csv.Sniffer().sniff(temp_lines, delimiters=",~")
            csv_file.seek(0)
            dict_reader = csv.DictReader(csv_file, dialect=dialect)
            data_items = list(dict_reader)
            headers = data_items[0].keys()
        else:
            file_name = f"{file_id}.xlsx"
            s3.download_file(os.environ["CLAIM_FILE_BUCKET"], event["fileId"], f"{TEMP_DIR}/{file_name}")
            try:
                ws.xlsx_to_dict(path=f"{TEMP_DIR}/{file_name}")
            except (IOError, OSError) as err:
                cancel_claims_file(
                    file_id,
                    str(err),
                )
                raise err
            try:
                headers = list(ws.header.keys())
            except IndexError:
                cancel_claims_file(
                    file_id,
                    "The uploaded claim file contains empty rows",
                )
                raise Exception("The uploaded claim file contains empty rows")
            data_items = ws.sanitize_sheet_items
        # Special handling for WINTER_GREEN client
        if client_id == os.environ["WINTER_GREEN"]:
            filtered_headers = filter(lambda x: x not in (None, "None", "(No value)"), headers)
        else:
            filtered_headers = filter(lambda x: x not in (None, "None"), headers)

        headers = [custom_filter(str.replace("*", "")) for str in filtered_headers]
        claim_columns = {}
        lookup_claim = list(db.lookup_claim.find())
        
        # Special handling for Sunshine client - map "Supplier Reference" to supplier_ref instead of "Payment Reference"
        if client_id == os.environ.get("SUNSHINE"):
            lookup_claim_modified = []
            for claim_column in lookup_claim:
                modified_entry = claim_column.copy()
                # For Sunshine: change supplier_ref mapping from "Payment reference" to "Supplier Reference"
                if claim_column["column_name"] == "supplier_ref":
                    modified_entry["name"] = "Supplier Reference"
                lookup_claim_modified.append(modified_entry)
            lookup_claim = lookup_claim_modified
        client_mandatory_claim_columns = db.client_claim_column.find({"client_id": ObjectId(client_id)})
        mandatory_claim_column_ids = [item["column"] for item in client_mandatory_claim_columns]
        mandatory_columns = list(filter(lambda x: x["_id"] in mandatory_claim_column_ids, lookup_claim))
        mandatory_column_names = [custom_filter(item["column_name"]) for item in mandatory_columns]

        mapped_headers = []
        multiple_elements = False
        for name in headers:
            mapped_name = next(filter(lambda x: custom_filter(x["name"]) == name, lookup_claim), None)
            if mapped_name:
                mapped_headers.append(mapped_name["column_name"])
        if (
            client_id == os.environ["BROADWAY"] or client_id == os.environ["BROADWAY_EJ"]
        ) and "element" not in mapped_headers:
            multiple_elements = True
            lookup_claim_elements = list(db.lookup_claim_elements.find())
            for name in headers:
                element_mapped_name = next(
                    filter(lambda x: custom_filter(x["name"]) == name, lookup_claim_elements), None
                )
                if element_mapped_name:
                    element_mapped_name["column_name"] = "element"
                    mapped_headers.append(element_mapped_name["column_name"])

        if not set(mandatory_column_names).issubset(set(mapped_headers)):
            cancel_claims_file(file_id, "Mandatory fields missing")
            raise Exception("Mandatory fields missing")

        claim_columns = {}
        for item in lookup_claim:
            claim_columns[custom_filter(item["name"])] = {
                "column_name": item["column_name"],
                "data_type": item["data_type"],
            }
        lookup_currency = db.lookup_currency.find()
        currency_list = [currency["code"] for currency in lookup_currency]
        lookup_claim_elements = list(db.lookup_claim_elements.find())
        lookup_claim_elements = [item["name"].casefold() for item in lookup_claim_elements]
        for i in range(ceil(len(data_items) / BATCH_SIZE)):
            data = {
                **event,
                "state": "validate",
                "batchNo": i,
                "batchSize": BATCH_SIZE,
                "headers": headers,
                "dataItems": data_items[i * BATCH_SIZE : i * BATCH_SIZE + BATCH_SIZE],
                "claimColumns": claim_columns,
                "mandatoryColumnNames": mandatory_column_names,
                "multipleElements": multiple_elements,
                "currencyList": currency_list,
                "lookupClaimElements": lookup_claim_elements,
            }
            data_key = f"claim/file-validator/extract/{file_id}/{i}.json"
            s3.put_object(
                Body=json.dumps(data),
                Bucket=STATE_MACHINE_PAYLOAD_BUCKET,
                Key=data_key,
            )
        return {**event, "bucket": STATE_MACHINE_PAYLOAD_BUCKET, "prefix": f"claim/file-validator/extract/{file_id}/"}

    else:
        data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=event["Key"])
        data = json.loads(data_object["Body"].read().decode("utf-8"))
        client_id = data["clientId"]
        file_id = data["fileId"]
        batch_no = data["batchNo"]
        batch_size = data["batchSize"]
        headers = data["headers"]
        data_items = data["dataItems"]
        claim_columns = data["claimColumns"]
        mandatory_column_names = data["mandatoryColumnNames"]
        multiple_elements = data["multipleElements"]
        currency_list = data["currencyList"]
        lookup_claim_elements = data["lookupClaimElements"]
        sftp = data["sftp"]
        sftp_key = data["sftpKey"]

        modified_data_list = []
        row_count = batch_no * batch_size + 1

        if multiple_elements:
            data_items = custom_data_modifier_broadway(data_items, row_count)
        for row in data_items:
            if multiple_elements:
                row_count = row["initial_row_count"]
            else:
                row_count += 1
            if set(row.values()).union({"None", "", None}) == {"None", "", None}:
                continue
            row = {
                custom_filter(k.replace("*", "")): v.strip() if v is not None else None
                for k, v in row.items()
                if k not in (None, "None", "initial_row_count")
            }

            data = {}
            if row.get("leadguestfirstname") and row.get("leadguestlastname"):
                row["leadpax"] = f"{row.get('leadguestfirstname')} {row.get('leadguestlastname')}"
                row.pop("leadguestfirstname")
                row.pop("leadguestlastname")

            for key, value in row.items():
                if not value or value == "None":
                    value = None
                if (
                    not key
                    or not claim_columns.get(key)
                    or (claim_columns[key]["column_name"] not in mandatory_column_names and not value)
                ):
                    continue
                if claim_columns[key]["column_name"] in mandatory_column_names and value is None:
                    cancel_claims_file(
                        file_id,
                        f"Value of mandatory field {claim_columns[key]['column_name']} is missing at row {row_count}",
                    )
                    raise Exception(
                        f"Value of mandatory field {claim_columns[key]['column_name']} is missing at row {row_count}"
                    )
                if (claim_columns[key]["column_name"] == "element") and (
                    value.casefold().strip() not in lookup_claim_elements
                ):
                    cancel_claims_file(
                        file_id,
                        f"{value} is not a valid element type at row {row_count}",
                    )
                    raise Exception(f"{value} is not a valid element type at row {row_count}")

                if claim_columns[key]["column_name"] == "currency_code":
                    value = value.upper()
                    currency_validation(currency_list, value, file_id)
                if claim_columns[key]["column_name"] == "booking_ref" and value[-2:] == ".0":
                    value = value[:-2]
                data[claim_columns[key]["column_name"]] = validate_type(
                    value, claim_columns[key]["data_type"], file_id, key, row_count, client_id
                )
            modified_data_list.append(data)

        if not modified_data_list:
            cancel_claims_file(
                file_id,
                "The uploaded claim file contains empty rows",
            )
            raise Exception("The uploaded claim file contains empty rows")

        data_key = f"claim/file-validator/{file_id}/{batch_no}.json"
        s3.put_object(Body=json.dumps(modified_data_list), Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=data_key)

        response = {
            "fileId": file_id,
            "clientId": client_id,
            "dataKey": data_key,
            "bucket": STATE_MACHINE_PAYLOAD_BUCKET,
            "sftp": sftp,
            "sftpKey": sftp_key,
        }
        return response
