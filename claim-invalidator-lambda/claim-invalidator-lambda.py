from datetime import datetime
import json
import os
from mongomock import ObjectId
from pymongo import MongoClient, ReadPreference
from helpers.secret_manager_handler import get_secret
import boto3
from helpers import round


STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    s3 = boto3.client("s3")
    data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=event["Key"])
    data = json.loads(data_object["Body"].read().decode("utf-8"))
    client_id, transactions = (data["clientId"], data["transactions"])
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    transaction_ids = []
    with client.start_session() as session:
        with session.start_transaction(read_preference=ReadPreference.PRIMARY):
            for transaction in transactions:
                transaction_ids.append(ObjectId(transaction["_id"]))
                db[collection_name].update_one(
                    {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                    [
                        {
                            "$set": {
                                "lead_pax": transaction.get("lead_pax"),
                                "pax_count": transaction.get("pax_count"),
                                "booking_date": transaction.get("booking_date"),
                                "departure_date": transaction.get("departure_date"),
                                "return_date": transaction.get("return_date"),
                                "total_booking_value": transaction.get("total_booking_value"),
                                "bonding": transaction.get("bonding"),
                                "updated_at": datetime.utcnow(),
                            }
                        },
                        {
                            "$set": {
                                "balance": {
                                    "$cond": {
                                        "if": {"$ne": [transaction.get("payment_type"), "Protected Deposit - Applied"]},
                                        "then": {"$round": [{"$add": ["$balance", transaction["amount"]]}, 2]},
                                        "else": {"$round": ["$balance", 2]},
                                    }
                                },
                                "total_claimed": {
                                    "$round": [{"$add": ["$total_claimed", -1 * transaction["amount"]]}, 2]
                                },
                            }
                        },
                    ],
                    collation={"locale": "en", "strength": 1},
                    session=session,
                )
            db.claims_file_details.update_many(
                {"_id": {"$in": transaction_ids}},
                {"$set": {"deleted": True, "status": "Cancelled", "updated_at": datetime.utcnow()}},
                session=session,
            )
