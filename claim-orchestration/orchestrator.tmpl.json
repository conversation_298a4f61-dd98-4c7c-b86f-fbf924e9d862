{"Comment": "Claim Workflow Orchestration", "StartAt": "Replace Previous Claim File?", "States": {"Replace Previous Claim File?": {"Type": "Choice", "Choices": [{"Variable": "$.replace", "BooleanEquals": true, "Next": "Fetch Previous Claim File"}], "Default": "Generate Claim from TBR?"}, "Fetch Previous Claim File": {"Type": "Task", "Resource": "${claim_file_fetch_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Invalidate", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}]}, "Fail": {"Type": "Fail"}, "Invalidate": {"Type": "Map", "Iterator": {"StartAt": "Invalidate Previous Claim Transactions", "States": {"Invalidate Previous Claim Transactions": {"Type": "Task", "Resource": "${claim_invalidator_lambda}", "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 20, "BackoffRate": 1.1}], "End": true}}, "ProcessorConfig": {"Mode": "DISTRIBUTED", "ExecutionType": "STANDARD"}}, "Next": "Generate Claim from TBR?", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "ResultPath": null, "MaxConcurrency": 1, "Label": "Invalidate", "ItemReader": {"Resource": "arn:aws:states:::s3:listObjectsV2", "Parameters": {"Bucket.$": "$.bucket", "Prefix.$": "$.prefix"}}}, "Generate Claim from TBR?": {"Type": "Choice", "Choices": [{"Variable": "$.claimFromTBR", "BooleanEquals": true, "Next": "Claim File Generator"}], "Default": "File Validator (Extract)"}, "Claim File Generator": {"Type": "Task", "Resource": "${claim_file_generator_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "File Validator (Extract)"}, "File Validator (Extract)": {"Type": "Task", "Resource": "${claim_file_validator_lambda}", "Parameters": {"state": "extract", "clientId.$": "$.clientId", "fileId.$": "$.fileId", "fileType.$": "$.fileType", "sftp.$": "$.sftp", "sftpKey.$": "$.sftpKey"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Validate", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}]}, "Validate": {"Type": "Map", "Next": "Persist", "Iterator": {"StartAt": "File Validator", "States": {"File Validator": {"Type": "Task", "Resource": "${claim_file_validator_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}, "ProcessorConfig": {"Mode": "DISTRIBUTED", "ExecutionType": "STANDARD"}}, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "Label": "Validate", "MaxConcurrency": 500, "ItemReader": {"Resource": "arn:aws:states:::s3:listObjectsV2", "Parameters": {"Bucket.$": "$.bucket", "Prefix.$": "$.prefix"}}}, "Persist": {"Type": "Map", "Iterator": {"StartAt": "<PERSON><PERSON><PERSON>", "States": {"Claim Persistor": {"Type": "Task", "Resource": "${claim_persistor_lambda}", "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 20, "BackoffRate": 1.1}], "End": true}}}, "Next": "<PERSON><PERSON><PERSON> Aggregator", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "OutputPath": "$[0]", "MaxConcurrency": 1}, "Claim Aggregator": {"Type": "Task", "Resource": "${claim_aggregator_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Calculate Anomalies"}, "Calculate Anomalies": {"Type": "Map", "Iterator": {"StartAt": "Anomaly Detector", "States": {"Anomaly Detector": {"Type": "Task", "Resource": "${claim_anomaly_detector_lambda}", "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 20, "BackoffRate": 1.1}], "End": true}}}, "Next": "Claim Status Updator", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}], "OutputPath": "$[0]", "MaxConcurrency": 1}, "Claim Status Updator": {"Type": "Task", "Resource": "${claim_status_updator_lambda}", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Success", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Fail"}]}, "Success": {"Type": "Succeed"}}}