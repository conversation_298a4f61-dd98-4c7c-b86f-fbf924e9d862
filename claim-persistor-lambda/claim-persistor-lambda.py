import json
import os
from pymongo import MongoClient, ReadPreference, read_concern
from datetime import datetime
import boto3
from helpers.amount_modifier import escrow_handler
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
import tempfile
from helpers import round


TEMP_DIR = tempfile.gettempdir()
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    client_id, file_id, data_key, bucket = (
        event["clientId"],
        event["fileId"],
        event["dataKey"],
        event["bucket"],
    )
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    s3 = boto3.client("s3")
    data_object = s3.get_object(Bucket=bucket, Key=data_key)
    data = json.loads(data_object["Body"].read().decode("utf-8"))
    claims_metadata = db.claims_metadata.find_one(
        {"client_id": ObjectId(client_id), "claim_files": {"$elemMatch": {"file_id": file_id}}}
    )
    file_date = claims_metadata["claim_files"][-1]["file_date"]
    file_name = claims_metadata["claim_files"][-1]["file_name"]
    basic_info = db.client_basic_info.find_one({"_id": ObjectId(client_id)})
    trust_account = db.lookup_trust_type.find_one({"_id": basic_info["type_of_trust_account"]})
    trust_type = trust_account["name"] if trust_account else None
    escrow_multiplier = basic_info.get("escrow_multiplier")
    escrow_multiplier_date = f'{datetime.strptime(file_date, "%Y-%m-%d").strftime("%Y-%m")}-01'
    client_escrow_multiplier = db.client_escrow_multiplier.find_one(
        {"client_id": ObjectId(client_id), "date": escrow_multiplier_date}
    )
    if client_escrow_multiplier and client_escrow_multiplier.get("multiplier"):
        escrow_multiplier = client_escrow_multiplier.get("multiplier")
        if client_id == os.environ.get("IGLU_ESCROW") and file_name.endswith("-generated.xlsx"):
            escrow_multiplier = client_escrow_multiplier.get("multiplier_difference")

    with client.start_session() as session:
        with session.start_transaction(
            read_preference=ReadPreference.PRIMARY, read_concern=read_concern.ReadConcern(level="snapshot")
        ):
            item_count = {}
            claim_total = {}
            checks = {}
            checked_amount = {}
            claim_transactions = []
            for transaction in data:
                transaction.update({"file_date": file_date})

                if trust_type == "ATOL Escrow" or trust_type == "Escrow Trigger":
                    if client_id in [os.environ.get("ANGLIA_TOURS"), os.environ.get("WST_TRAVEL")]:
                        escrow_handler_response = escrow_handler(
                            transaction["amount"],
                            client_id,
                            transaction["booking_date"],
                        )
                    else:
                        if escrow_multiplier:
                            escrow_handler_response = escrow_handler(
                                transaction["amount"],
                                client_id,
                                transaction["booking_date"],
                                escrow_multiplier=escrow_multiplier,
                            )
                        else:
                            escrow_handler_response = escrow_handler(
                                transaction["amount"],
                                client_id,
                                transaction["booking_date"],
                            )
                    
                    transaction["amount"] = round(transaction["amount"], 2)
                    transaction["original_amount"] = transaction["amount"]
                    transaction["escrow_multiplier"] = escrow_handler_response[1]

                    # Skip subtraction for BLUESTYLE client
                    if client_id != os.environ.get("BLUESTYLE"):
                        transaction["amount"] = round(escrow_handler_response[0], 2)

                transaction["currency_code"] = (
                    transaction["currency_code"] if transaction.get("currency_code") else "GBP"
                )
                transaction.update(
                    {
                        "client_id": ObjectId(client_id),
                        "claims_id": claims_metadata["_id"],
                        "file_id": file_id,
                        "deleted": False,
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                    }
                )
                claim_transactions.append(transaction)
                existing_trust_fund = db[collection_name].find_one(
                    {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                    collation={"locale": "en", "strength": 1},
                    session=session,
                )
                if not existing_trust_fund:
                    db[collection_name].insert_one(
                        {
                            "client_id": ObjectId(client_id),
                            "booking_ref": transaction["booking_ref"],
                            "balance": -1 * round(transaction["amount"], 2)
                            if transaction.get("payment_type") != "Protected Deposit - Applied"
                            else 0.0,
                            "total_in_trust": 0,
                            "total_claimed": transaction["amount"],
                            "currency_code": [transaction["currency_code"]],
                            "lead_pax": transaction.get("lead_pax"),
                            "pax_count": transaction.get("pax_count"),
                            "booking_date": transaction["booking_date"],
                            "departure_date": transaction.get("departure_date"),
                            "return_date": transaction.get("return_date"),
                            "total_booking_value": transaction.get("total_booking_value"),
                            "bonding": transaction.get("bonding"),
                            "booking_status": transaction.get("booking_status"),
                            "created_at": datetime.utcnow(),
                            "updated_at": datetime.utcnow(),
                        },
                        session=session,
                    )
                else:
                    currency_code = existing_trust_fund["currency_code"]
                    if transaction["currency_code"] not in currency_code:
                        currency_code.append(transaction["currency_code"])
                    db[collection_name].update_one(
                        {"client_id": ObjectId(client_id), "booking_ref": transaction["booking_ref"]},
                        [
                            {
                                "$set": {
                                    "currency_code": currency_code,
                                    "lead_pax": transaction.get("lead_pax"),
                                    "pax_count": transaction.get("pax_count"),
                                    "booking_date": transaction.get("booking_date"),
                                    "departure_date": transaction.get("departure_date"),
                                    "return_date": transaction.get("return_date"),
                                    "total_booking_value": transaction.get("total_booking_value"),
                                    "bonding": transaction.get("bonding"),
                                    "updated_at": datetime.utcnow(),
                                }
                            },
                            {
                                "$set": {
                                    "total_claimed": {"$add": ["$total_claimed", transaction["amount"]]},
                                    "balance": {
                                        "$cond": {
                                            "if": {
                                                "$ne": [transaction.get("payment_type"), "Protected Deposit - Applied"]
                                            },
                                            "then": {"$round": [{"$add": ["$balance", -1 * transaction["amount"]]}, 2]},
                                            "else": {"$round": ["$balance", 2]},
                                        }
                                    },
                                }
                            },
                        ],
                        collation={"locale": "en", "strength": 1},
                        session=session,
                    )
                if not item_count.get(transaction["currency_code"]):
                    item_count[transaction["currency_code"]] = 1
                    claim_total[transaction["currency_code"]] = transaction["amount"]
                    checks[transaction["currency_code"]] = 0
                    checked_amount[transaction["currency_code"]] = 0
                else:
                    item_count[transaction["currency_code"]] += 1
                    claim_total[transaction["currency_code"]] += transaction["amount"]
            if claim_transactions:
                db.claims_file_details.insert_many(claim_transactions, session=session)

            response = []
            output_data_key = f"claim/persistor/output/{file_id}.json"
            try:
                s3.download_file(bucket, output_data_key, f"{TEMP_DIR}/persistor_output")
                with open(f"{TEMP_DIR}/persistor_output", "rb") as file_obj:
                    data = json.load(file_obj)
                if data:
                    response = data
                else:
                    response = []
            except Exception as e:
                print(f"An error occurred: {str(e)}")

            response.append(
                {
                    "fileId": file_id,
                    "clientId": client_id,
                    "itemCount": item_count,
                    "claimTotal": claim_total,
                    "checks": checks,
                    "checkedAmount": checked_amount,
                    "sftp": event["sftp"],
                    "sftpKey": event["sftpKey"],
                }
            )

    s3.put_object(
        Body=json.dumps(response),
        Bucket=bucket,
        Key=output_data_key,
    )

    return {
        "fileId": file_id,
        "clientId": client_id,
        "bucket": bucket,
        "dataKey": output_data_key,
        "sftp": event["sftp"],
        "sftpKey": event["sftpKey"],
    }
