from datetime import datetime
import json
import os
from mongomock import ObjectId
from pymongo import MongoClient, ReadPreference
from helpers import track_opening_closing_balance_changes
from helpers.secret_manager_handler import get_secret
import boto3
from helpers import round


STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def track_removed_booking_ref(transaction, file_id, session):
    """Track removed booking reference for anomaly detection, excluding specific elements"""
    excluded_elements = ["performance", "refund", "cancellation"]
    element = transaction.get("element", "").lower().strip()
    
    # Skip tracking for excluded elements
    if element in excluded_elements:
        return
        
    removed_booking_data = {
        "client_id": transaction["client_id"],
        "booking_ref": transaction["booking_ref"],
        "file_id": file_id,
        "element": transaction.get("element"),
        "removal_date": datetime.utcnow(),
        "return_date": transaction.get("return_date"),
        "reason": "cancelled",
        "notes": f"Removed from claim file {file_id}",
        "created_at": datetime.utcnow(),
        "expired": False
    }
    
    # Insert or update the removed booking reference
    db.removed_booking_refs.update_one(
        {
            "client_id": transaction["client_id"],
            "booking_ref": transaction["booking_ref"],
            "file_id": file_id
        },
        {"$set": removed_booking_data},
        upsert=True,
        session=session
    )
    
    print(f"Tracked removed booking reference: {transaction['booking_ref']} "
          f"for client {transaction['client_id']} in file {file_id}")


def remove_from_tracking(transaction, session):
    """Remove booking reference from tracking when reinstated"""
    db.removed_booking_refs.delete_many(
        {
            "client_id": transaction["client_id"],
            "booking_ref": transaction["booking_ref"]
        },
        session=session
    )
    
    print(f"Removed tracking for reinstated booking: {transaction['booking_ref']} "
          f"for client {transaction['client_id']}")


def lambda_handler(event, context):
    if not event["state"] == "final":
        s3 = boto3.client("s3")
        data_object = s3.get_object(Bucket=STATE_MACHINE_PAYLOAD_BUCKET, Key=event["input"]["Key"])
        data = json.loads(data_object["Body"].read().decode("utf-8"))
        (client_id, created_at, file_date, is_deleted, transaction_ids) = (
            data["clientId"],
            datetime.fromisoformat(data["createdAt"]),
            data["fileDate"],
            data["isDeleted"],
            data["transaction_ids"],
        )

        transaction_ids = [ObjectId(transaction_id) for transaction_id in transaction_ids]
        with client.start_session() as session:
            with session.start_transaction(read_preference=ReadPreference.PRIMARY):
                multiplier = 1
                if not is_deleted:  # Delete valid transactions
                    claims_file_details = list(
                        db.claims_file_details.find({"_id": {"$in": transaction_ids}, "deleted": False})
                    )
                    
                    # Track removed booking references for anomaly detection
                    for transaction in claims_file_details:
                        track_removed_booking_ref(transaction, data.get("fileId", "unknown"), session)
                    
                    db.claims_file_details.update_many(
                        {"_id": {"$in": transaction_ids}, "deleted": False},
                        {"$set": {"deleted": True, "status": "Cancelled"}},
                        session=session,
                    )
                    db.anomaly_claims.update_many(
                        {"transaction_id": {"$in": transaction_ids}},
                        {"$set": {"deleted": True, "updated_at": datetime.utcnow()}},
                        session=session,
                    )
                else:  # Reinstate deleted transactions
                    multiplier = -1
                    claims_file_details = list(
                        db.claims_file_details.find({"_id": {"$in": transaction_ids}, "deleted": True})
                    )

                    # Remove from removed booking references when reinstated
                    for transaction in claims_file_details:
                        remove_from_tracking(transaction, session)

                    db.claims_file_details.update_many(
                        {"_id": {"$in": transaction_ids}, "deleted": True},
                        {"$set": {"deleted": False, "status": "Reinstated"}},
                        session=session,
                    )
                    db.anomaly_claims.update_many(
                        {"transaction_id": {"$in": transaction_ids}},
                        {"$set": {"deleted": False, "updated_at": datetime.utcnow()}},
                        session=session,
                    )
                amount_changes = {}
                item_count_changes = {}
                claim_total_changes = {}
                checks_changes = {}
                checked_amount_changes = {}
                for item in claims_file_details:
                    collection_name = "trust_fund" if str(item["client_id"]) == os.environ.get("NAS") else "trust_fund_v2"
                    inc_balance = multiplier * round(item["amount"],2)
                    inc_total_claimed = -1 * multiplier * round(item["amount"],2)
                    db[collection_name].update_one(
                        {"client_id": item["client_id"], "booking_ref": item["booking_ref"]},
                        [
                            {
                                "$set": {
                                    "updated_at": datetime.utcnow(),
                                }
                            },
                            {
                                "$set": {
                                    "balance": {
                                        "$cond": {
                                            "if": {"$ne": [item.get("payment_type"), "Protected Deposit - Applied"]},
                                            "then": {"$round": [{"$add": ["$balance", inc_balance]}, 2]},
                                            "else": {"$round": ["$balance", 2]}
                                        }
                                    },
                                    "total_claimed": {"$add": ["$total_claimed", inc_total_claimed]}
                                }
                            }
                        ],
                        collation={"locale": "en", "strength": 1},
                        session=session,
                    )
                    if item["currency_code"] in item_count_changes.keys():
                        amount_changes[item["currency_code"]] += inc_balance
                        item_count_changes[item["currency_code"]] += -1 * multiplier
                        claim_total_changes[item["currency_code"]] += inc_total_claimed
                    else:
                        amount_changes[item["currency_code"]] = inc_balance
                        item_count_changes[item["currency_code"]] = -1 * multiplier
                        claim_total_changes[item["currency_code"]] = inc_total_claimed
                        checks_changes[item["currency_code"]] = 0
                        checked_amount_changes[item["currency_code"]] = 0
                    if item.get("check") == "full-check" or item.get("check") == "quick-check":
                        checks_changes[item["currency_code"]] += -1 * multiplier
                        checked_amount_changes[item["currency_code"]] += inc_total_claimed
        return {
            "amountChanges": amount_changes,
            "itemCountChanges": item_count_changes,
            "claimTotalChanges": claim_total_changes,
            "checksChanges": checks_changes,
            "checkedAmountChanges": checked_amount_changes,
        }
    else:
        client_id, claim_id, file_id, file_date, new_status, created_at = (
            event["data"]["clientId"],
            event["data"]["claimId"],
            event["data"]["fileId"],
            event["data"]["fileDate"],
            event["data"]["newStatus"],
            datetime.fromisoformat(event["data"]["createdAt"]),
        )

        item_count = {}
        claim_total = {}
        checks = {}
        checked_amount = {}
        amount_changes = {}
        for data in event["data"]["input"]:
            for currency, count in data["itemCountChanges"].items():
                if currency not in item_count:
                    item_count[currency] = count
                    claim_total[currency] = data["claimTotalChanges"][currency]
                    checks[currency] = data["checksChanges"][currency]
                    checked_amount[currency] = data["checkedAmountChanges"][currency]
                    amount_changes[currency] = data["amountChanges"][currency]
                else:
                    item_count[currency] += count
                    claim_total[currency] += data["claimTotalChanges"][currency]
                    checks[currency] += data["checksChanges"][currency]
                    checked_amount[currency] += data["checkedAmountChanges"][currency]
                    amount_changes[currency] += data["amountChanges"][currency]

        inc_items = {}
        for currency in item_count.keys():
            inc_items.update(
                {
                    f"claim_files.$.item_count.{currency}": item_count[currency],
                    f"claim_files.$.claim_total.{currency}": claim_total[currency],
                    f"claim_files.$.checks.{currency}": checks[currency],
                    f"claim_files.$.checked_amount.{currency}": checked_amount[currency],
                }
            )
        inc_query = {"$inc": inc_items} if inc_items else {}

        with client.start_session() as session:
            with session.start_transaction(read_preference=ReadPreference.PRIMARY):
                db.claims_metadata.update_one(
                    {"_id": ObjectId(claim_id), "claim_files.file_id": file_id},
                    {
                        "$set": {
                            "status": new_status,
                            "claim_files.$.status": new_status,
                            "updated_at": datetime.utcnow(),
                        },
                        **inc_query,
                    },
                    session=session,
                )
                for currency, amount_difference in amount_changes.items():
                    track_opening_closing_balance_changes(
                        db,
                        {"client_id": ObjectId(client_id), "created_at": created_at, "currency_code": currency},
                        amount_difference,
                        file_date,
                        session,
                    )
