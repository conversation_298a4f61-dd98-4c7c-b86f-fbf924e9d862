from math import ceil
import os
import json
import boto3
from botocore.exceptions import ClientError
from pymongo import MongoClient
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
import logging
import uuid

STATE_MACHINE_PAYLOAD_BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))
logger = logging.getLogger(__name__)
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def find(stepfunctions_client, state_machine_name):
    """
    Finds a state machine by name. This function iterates the state machines for
    the current account until it finds a match and returns the first matching
    state machine.

    :param state_machine_name: The name of the state machine to find.
    :return: The ARN of the named state machine when found; otherwise, None.
    """
    try:
        paginator = stepfunctions_client.get_paginator("list_state_machines")
        for page in paginator.paginate():
            for machine in page["stateMachines"]:
                if machine["name"] == state_machine_name:
                    state_machine_name = state_machine_name
                    state_machine_arn = machine["stateMachineArn"]
                    break
            if state_machine_arn is not None:
                break
        if state_machine_arn is not None:
            logger.info("Found state machine %s with ARN %s.", state_machine_name, state_machine_arn)
        else:
            logger.info("Couldn't find state machine %s.", state_machine_name)
    except ClientError:
        logger.exception("Couldn't find state machine %s.", state_machine_name)
        raise
    else:
        return state_machine_arn


def lambda_handler(event, context):
    file_id = event["fileId"]
    claim_id = event["claimId"]
    sfn_client = boto3.client("stepfunctions")
    s3 = boto3.client("s3")
    state_machine_arn = find(sfn_client, f'claim-status-change-orchestration-{os.environ["ENVIRONMENT"]}')

    match_condition = {"claims_id": ObjectId(claim_id), "deleted": event["isDeleted"]}
    if event["isDeleted"]:
        match_condition["file_id"] = file_id
    claim_transactions = list(
        db.claims_file_details.find(
            match_condition,
            projection={"_id": 1},
        )
    )
    transaction_ids = [str(transaction["_id"]) for transaction in claim_transactions]

    id = uuid.uuid4()
    for i in range(ceil(len(transaction_ids) / BATCH_SIZE)):
        data = {**event, "transaction_ids": transaction_ids[i * BATCH_SIZE : i * BATCH_SIZE + BATCH_SIZE]}
        data_key = f"claim-status-change/{file_id}/{id}/{i}.json"
        s3.put_object(
            Body=json.dumps(data),
            Bucket=STATE_MACHINE_PAYLOAD_BUCKET,
            Key=data_key,
        )

    response = {**event, "bucket": STATE_MACHINE_PAYLOAD_BUCKET, "prefix": f"claim-status-change/{file_id}/{id}/"}

    sfn_client.start_execution(stateMachineArn=state_machine_arn, input=json.dumps(response))
    return {"statusCode": 200, "body": json.dumps("Step Function Triggered!")}
