import os
import boto3
from botocore.exceptions import ClientError


def custom_email_template(custom_message, link):
    s3 = boto3.client("s3")
    try:
        logo_image = s3.generate_presigned_url(
            "get_object",
            Params={"Bucket": os.environ["LOGO_BUCKET"], "Key": "ptt-logo.png"},
            ExpiresIn=240,
            HttpMethod="GET",
        )
    except ClientError as error:
        raise error

    template = f"""<html lang="en">
     <body
        style="
          background-color: #333;
          font-family: PT Sans, Trebuchet MS, sans-serif;
        ">
        <div
          style="
            margin: 0 auto;
            width: 100%;
            height:100%;
            background-color: #fff;
            font-size: 1.2rem;
            font-style: normal;
            font-weight: normal;
            line-height: 19px;
          "
           align="center"
        >
            <div style="padding: 20">
                <img
                style="
                border: 0;
                display: block;
                height: 96;
                width: 96;
                max-width: 373px;"
                alt="ptt-logo"
                height="96"
                width="96"
                src='{logo_image}'/>
        <p
        style="
        margin-top: 20px;
        margin-bottom: 0;
        font-size: 16px;
        line-height: 24px;
        color: #000;
        text-align:center;"
        >
        {custom_message}
        </p>
        <div style="Margin-left: 20px;Margin-right: 20px;Margin-top: 24px;">
                <div style="Margin-bottom: 20px;text-align: center;">
                    <a
                    style="border-radius: 4px;display: block;font-size: 14px;font-weight: bold;line-height: 24px;padding: 12px 24px 13px 24px;text-align: center;text-decoration: none !important;transition: opacity 0.1s ease-in;color: #ffffff !important;box-shadow: inset 0 -2px 0 0 rgba(0, 0, 0, 0.2);background-color: #3b5998;font-family: PT Sans, Trebuchet MS, sans-serif; letter-spacing: 0.05rem;"
                    href={link}>CLICK HERE TO SIGN IN</a>
                </div>
        </div>

        </div>
        </div>
        </body>
    </html>"""
    return template


def lambda_handler(event, context):
    if event["triggerSource"] == "CustomMessage_AdminCreateUser":
        code = event["request"]["codeParameter"]
        username = event["request"]["usernameParameter"]
        link = f'https://{os.environ.get("ENVIRONMENT")}.pttapp.com/login'
        message = (
            f"Hi,<br>Here is your PTT APP temporary login details<br>Username: {username}<br>Temporary Password: {code}"
        )
        event["response"]["emailMessage"] = custom_email_template(message, link)
    if event["triggerSource"] == "CustomMessage_ForgotPassword":
        code = event["request"]["codeParameter"]
        message = f"Hi,<br>Here is your PTT APP verification code: {code}"
        link = f'https://{os.environ.get("ENVIRONMENT")}.pttapp.com/forgot-password'
        event["response"]["emailMessage"] = custom_email_template(message, link)

    return event
