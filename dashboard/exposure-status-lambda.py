from datetime import date, datetime, timedelta
import os
from pymongo import MongoClient, ReadPreference, read_concern
from helpers.secret_manager_handler import get_secret

client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    today = date.today().strftime("%Y-%m-%d")
    x_days_from_departure = (date.today() - timedelta(days=120)).strftime("%Y-%m-%d")
    x_days_from_booking = (date.today() - timedelta(days=540)).strftime("%Y-%m-%d")

    match_condition = {}
    if event.get("userId"):
        users = db.user.find_one({"user_id": event["userId"]}, projection={"clients": 1, "_id": 0})
        match_condition.update({"client_id": {"$in": users["clients"]}})

    trust_funds = list(
        db.trust_fund_v2.aggregate(
            [
                {"$unwind": "$currency_code"},
                {"$match": match_condition},
                {
                    "$project": {
                        "client_id": "$client_id",
                        "currency_code": "$currency_code",
                        "flown": {"$cond": [{"$lt": ["$return_date", today]}, 1, 0]},
                        "flownAmount": {"$cond": [{"$lt": ["$return_date", today]}, "$balance", 0]},
                        "refunded": {
                            "$cond": [
                                {"$and": [{"$gte": ["$return_date", today]}, {"$eq": ["$type", "refunded"]}]},
                                1,
                                0,
                            ]
                        },
                        "refundedAmount": {
                            "$cond": [
                                {"$and": [{"$gte": ["$return_date", today]}, {"$eq": ["$type", "refunded"]}]},
                                "$balance",
                                0,
                            ]
                        },
                        "chargeBack": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$in": ["$type", ["chargeback", "chargebacks", "chargeback reversal"]]},
                                    ]
                                },
                                1,
                                0,
                            ]
                        },
                        "chargeBackAmount": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$in": ["$type", ["chargeback", "chargebacks", "chargeback reversal"]]},
                                    ]
                                },
                                "$balance",
                                0,
                            ]
                        },
                        "expiredVisa": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$eq": ["$payment_type", "VI"]},
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$lt": ["$departure_date", x_days_from_departure]},
                                                {"$lt": ["$booking_date", x_days_from_booking]},
                                            ]
                                        },
                                    ]
                                },
                                1,
                                0,
                            ]
                        },
                        "expiredVisaAmount": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$eq": ["$payment_type", "VI"]},
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$lt": ["$departure_date", x_days_from_departure]},
                                                {"$lt": ["$booking_date", x_days_from_booking]},
                                            ]
                                        },
                                    ]
                                },
                                "$balance",
                                0,
                            ]
                        },
                        "expiredMasterCard": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$eq": ["$payment_type", "MC"]},
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$lt": ["$departure_date", x_days_from_departure]},
                                                {"$lt": ["$booking_date", x_days_from_booking]},
                                            ]
                                        },
                                    ]
                                },
                                1,
                                0,
                            ]
                        },
                        "expiredMasterCardAmount": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$eq": ["$payment_type", "MC"]},
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$lt": ["$departure_date", x_days_from_departure]},
                                                {"$lt": ["$booking_date", x_days_from_booking]},
                                            ]
                                        },
                                    ]
                                },
                                "$balance",
                                0,
                            ]
                        },
                        "rebooked": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$not": {"$in": ["$payment_type", ["MC", "VI"]]}},
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "VI"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "MC"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                            ]
                                        },
                                        {"$eq": ["$booking_status", "rebooked"]},
                                    ]
                                },
                                1,
                                0,
                            ]
                        },
                        "rebookedAmount": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$not": {"$in": ["$payment_type", ["MC", "VI"]]}},
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "VI"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "MC"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                            ]
                                        },
                                        {"$eq": ["$booking_status", "rebooked"]},
                                    ]
                                },
                                "$balance",
                                0,
                            ]
                        },
                        "vouchered": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$not": {"$in": ["$payment_type", ["MC", "VI"]]}},
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "VI"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "MC"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                            ]
                                        },
                                        {"$eq": ["$booking_status", "vouchered"]},
                                    ]
                                },
                                1,
                                0,
                            ]
                        },
                        "voucheredAmount": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$not": {"$in": ["$payment_type", ["MC", "VI"]]}},
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "VI"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "MC"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                            ]
                                        },
                                        {"$eq": ["$booking_status", "vouchered"]},
                                    ]
                                },
                                "$balance",
                                0,
                            ]
                        },
                        "open": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$not": {"$in": ["$payment_type", ["MC", "VI"]]}},
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "VI"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "MC"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                            ]
                                        },
                                        {"$in": ["$booking_status", ["Active", "Live", None]]},
                                    ]
                                },
                                1,
                                0,
                            ]
                        },
                        "openAmount": {
                            "$cond": [
                                {
                                    "$and": [
                                        {"$gte": ["$return_date", today]},
                                        {"$not": {"$in": ["$type", ["refunded", "chargeback"]]}},
                                        {
                                            "$or": [
                                                {"$not": {"$in": ["$payment_type", ["MC", "VI"]]}},
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "VI"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                                {
                                                    "$and": [
                                                        {"$eq": ["$payment_type", "MC"]},
                                                        {
                                                            "$or": [
                                                                {"$gte": ["$departure_date", x_days_from_departure]},
                                                                {"$gte": ["$booking_date", x_days_from_booking]},
                                                            ]
                                                        },
                                                    ]
                                                },
                                            ]
                                        },
                                        {"$in": ["$booking_status", ["Active", "Live", None]]},
                                    ]
                                },
                                "$balance",
                                0,
                            ]
                        },
                    }
                },
                {
                    "$group": {
                        "_id": {"client_id": "$client_id", "currency_code": "$currency_code"},
                        "Open": {"$sum": "$open"},
                        "OpenAmount": {"$sum": "$openAmount"},
                        "Flown": {"$sum": "$flown"},
                        "FlownAmount": {"$sum": "$flownAmount"},
                        "Vouchered": {"$sum": "$vouchered"},
                        "VoucheredAmount": {"$sum": "$voucheredAmount"},
                        "Rebooked": {"$sum": "$rebooked"},
                        "RebookedAmount": {"$sum": "$rebookedAmount"},
                        "Refunded": {"$sum": "$refunded"},
                        "RefundedAmount": {"$sum": "$refundedAmount"},
                        "ExpiredVisa": {"$sum": "$expiredVisa"},
                        "ExpiredVisaAmount": {"$sum": "$expiredVisaAmount"},
                        "ExpiredMasterCard": {"$sum": "$expiredMasterCard"},
                        "ExpiredMasterCardAmount": {"$sum": "$expiredMasterCardAmount"},
                        "ChargeBack": {"$sum": "$chargeBack"},
                        "ChargeBackAmount": {"$sum": "$chargeBackAmount"},
                    }
                },
            ],
            collation={"locale": "en", "strength": 1},
        )
    )

    for data in trust_funds:
        content = {}
        with client.start_session() as session:
            with session.start_transaction(
                read_preference=ReadPreference.PRIMARY, read_concern=read_concern.ReadConcern(level="snapshot")
            ):
                content.update(
                    {
                        "open": {"count": data["Open"], "amount": data["OpenAmount"]},
                        "flown": {"count": data["Flown"], "amount": data["FlownAmount"]},
                        "vouchered": {"count": data["Vouchered"], "amount": data["VoucheredAmount"]},
                        "rebooked": {"count": data["Rebooked"], "amount": data["RebookedAmount"]},
                        "refunded": {"count": data["Refunded"], "amount": data["RefundedAmount"]},
                        "expired": {
                            "visa": {"count": data["ExpiredVisa"], "amount": data["ExpiredVisaAmount"]},
                            "master_card": {
                                "count": data["ExpiredMasterCard"],
                                "amount": data["ExpiredMasterCardAmount"],
                            },
                        },
                        "charge_back": {"count": data["ChargeBack"], "amount": data["ChargeBackAmount"]},
                    }
                )

                db.dashboard_exposure_status.update_one(
                    data["_id"],
                    {"$set": {**data["_id"], "exposure_status": content, "updated_at": datetime.utcnow()}},
                    upsert=True,
                )
