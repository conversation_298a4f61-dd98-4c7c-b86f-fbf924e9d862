import os
import json
import logging
import tempfile
from datetime import datetime
from collections import OrderedDict
from collections.abc import Generator

from pymongo import MongoClient
from bson import ObjectId
import boto3

from helpers.transforms import (
    transform_dates,
    generate_csv_and_excel_report,
    trust_balance_report_nas,
    trust_balance_report_gtl,
    trust_balance_report,
    upload_file
)

from helpers.secret_manager_handler import get_secret

# Configure logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize MongoDB & S3 clients
_mongo_uri = os.environ.get("DATABASE_URI") or get_secret("eu-west-2")
mongo_client = MongoClient(_mongo_uri)
db = mongo_client.get_database()
s3_client = boto3.client("s3")


def lambda_handler(event, context):
    """
    Handles trust balance report generation in AWS Lambda.
    
    Expects event with keys:
      - client: client_id (string)
      - currency: optional currency code
      - file_id_csv: filename for CSV
      - file_id_xlsx: filename for XLSX
    """
    client_id    = event["client"]
    currency     = event.get("currency", "")
    file_id_csv  = event["file_id_csv"]
    file_id_xlsx = event["file_id_xlsx"]
    bucket       = os.environ["REPORTS_BUCKET"]

    # 1) Mark status = Generating
    now = datetime.utcnow()
    db.report_files.update_many(
        {"file_id": {"$in": [file_id_csv, file_id_xlsx]}},
        {"$set": {"status": "Generating New Report", "updated_at": now}}
    )

    # 2) Load client_basic_info for header logic if NAS or FLYPOP
    client_basic_info = None
    nas_clients = [os.environ.get("NAS"), os.environ.get("FLYPOP")]
    
    if client_id in nas_clients:
        client_basic_info = db.client_basic_info.find_one({"_id": ObjectId(client_id)})
        logger.info("client_basic_info: %s", client_basic_info)

    # 3) Run appropriate report pipeline
    logger.info(f"Starting report generation for client {client_id}")
    svc_data = {"client": client_id, "currency": currency}
    
    if client_id in nas_clients:
        logger.info("inside NAS report pipeline")
        details = trust_balance_report_nas(svc_data)
        # materialize generator if present
        if isinstance(details.get("content"), Generator):
            details["content"] = list(details["content"])
    elif client_id == os.environ.get("GTL"):
        logger.info("inside GTL report pipeline")
        details = trust_balance_report_gtl(svc_data)
    else:
        logger.info("inside default report pipeline")
        details = trust_balance_report(svc_data)

    # 4) Apply date formatting - transform all data
    logger.info("Transforming dates in report data")
    transformed = list(transform_dates(client_id, details["content"]))

    # 5) Build header dict
    header_dict = OrderedDict([
        ("cId", "Client Id"),
        ("friendlyName", "Client Name"),
        ("bookingRef", "Booking Ref"),
        ("leadPax", "Lead Pax"),
        ("bookingDate", "Booking Date"),
        ("departureDate", "Date of Travel"),
        ("returnDate", "Date of Return"),
        ("supplierName", "Supplier Names"),
        ("currency", "Currency"),
        ("deposits", "Deposits"),
        ("refundsFromDepositFile", "Refund From Deposit File"),
        ("totalBanked", "Total Banked"),
        ("totalClaimed", "Total Claimed"),
        ("balance", "Balance"),
        ("status", "Status"),
        ("type", "Type"),
        ("totalBookingValue", "Total Booking Value"),
    ])
    
    # Remove supplierName if NAS without new_workflow
    if client_id in nas_clients and client_basic_info and not client_basic_info.get("new_workflow", False):
        logger.info("Removing supplierName from header for NAS client without new_workflow")
        header_dict.pop("supplierName", None)

    # 6) Generate CSV + XLSX in temporary directories
    tmp_dir = tempfile.gettempdir()
    
    # Create separate directories for CSV and XLSX to avoid file naming conflicts
    csv_dir = os.path.join(tmp_dir, "csv")
    xlsx_dir = os.path.join(tmp_dir, "xlsx")
    
    # Create the directories
    os.makedirs(csv_dir, exist_ok=True)
    os.makedirs(xlsx_dir, exist_ok=True)
    
    logger.info(f"Generating CSV and XLSX reports in {csv_dir} and {xlsx_dir}")
    generate_csv_and_excel_report(header_dict, transformed, file_id_csv, file_id_xlsx, csv_dir, xlsx_dir)
    
    # 7) Upload to S3
    csv_full_path = os.path.join(csv_dir, file_id_csv)
    xlsx_full_path = os.path.join(xlsx_dir, file_id_xlsx)
    
    logger.info(f"Uploading files to S3 bucket {bucket}")
    upload_file(bucket, file_id_csv, csv_full_path)
    upload_file(bucket, file_id_xlsx, xlsx_full_path)
    
    row_count = len(transformed)
    logger.info("Generated reports with %d rows for client %s", row_count, client_id)

    # 8) Mark status = Generated
    now = datetime.utcnow()
    db.report_files.update_many(
        {"file_id": {"$in": [file_id_csv, file_id_xlsx]}},
        {"$set": {
            "status": "Generated New Report",
            "generated_at": now,
            "updated_at": now,
        }}
    )
    logger.info("Report generation completed successfully")

    return {
        "statusCode": 200,
        "body": json.dumps({
            "message": "Report generated successfully",
            "row_count": row_count
        })
    }