import os
import boto3
from pymongo import MongoClient
from pymongo.collation import Collation
from helpers.secret_manager_handler import get_secret

client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def add_admins():
    cognito_client = boto3.client("cognito-idp")
    user_pool_id = os.environ.get("USER_POOL_ID")
    ptt_admins = cognito_client.list_users_in_group(UserPoolId=user_pool_id, GroupName="ptt-admin")["Users"]
    for admin in ptt_admins:
        user_id = next(filter(lambda x: x["Name"] == "sub", admin["Attributes"]))["Value"]
        user = db.user.find_one({"user_id": user_id})
        if not user:
            db.user.insert_one(
                {"user_id": user_id, "confirmation_status": "Confirmed", "role": "ptt-admin", "clients": []}
            )


def lambda_handler(event, context):
    # Create indexes
    db.client_basic_info.create_index([("c_id", 1)], unique=True, collation=Collation(locale="en", strength=1))
    db.client_basic_info.create_index([("full_name", 1)], collation=Collation(locale="en", strength=1))
    db.client_basic_info.create_index([("friendly_name", 1)], collation=Collation(locale="en", strength=1))
    db.client_basic_info.create_index([("sftp_location", 1)])
    db.user.create_index([("user_id", 1)], unique=True)

    db.banking_metadata.create_index([("client_id", 1)])
    db.claims_metadata.create_index([("client_id", 1)])
    db.banking_file_details.create_index([("banking_id", 1)])
    db.claims_file_details.create_index([("claims_id", 1)])
    db.banking_file_details.create_index([("file_id", 1)])
    db.claims_file_details.create_index([("file_id", 1)])
    db.banking_file_details.create_index([("client_id", 1), ("booking_ref", 1)])
    db.claims_file_details.create_index([("client_id", 1), ("booking_ref", 1)])
    db.banking_file_details.create_index(
        [("client_id", 1), ("booking_ref", 1)],
        name="client_id_1_booking_ref_1_collation",
        collation=Collation(locale="en", strength=1),
    )
    db.claims_file_details.create_index(
        [("client_id", 1), ("booking_ref", 1)],
        name="client_id_1_booking_ref_1_collation",
        collation=Collation(locale="en", strength=1),
    )
    db.banking_file_details.create_index([("payment_type", 1)], collation=Collation(locale="en", strength=1))
    db.banking_file_details.create_index([("created_at", 1)])
    db.claims_file_details.create_index([("element", 1)], collation=Collation(locale="en", strength=1))
    db.claims_file_details.create_index([("client_id", 1), ("return_date", 1), ("element", 1), ("deleted", 1)])

    db.anomaly_banking.create_index([("banking_id", 1)])
    db.anomaly_claims.create_index([("claims_id", 1)])
    db.anomaly_banking.create_index([("file_id", 1)])
    db.anomaly_claims.create_index([("file_id", 1)])
    db.anomaly_banking.create_index([("client_id", 1), ("booking_ref", 1)])
    db.anomaly_claims.create_index([("client_id", 1), ("booking_ref", 1)])
    db.anomaly_banking.create_index(
        [("client_id", 1), ("booking_ref", 1)],
        name="client_id_1_booking_ref_1_collation",
        collation=Collation(locale="en", strength=1),
    )
    db.anomaly_claims.create_index(
        [("client_id", 1), ("booking_ref", 1)],
        name="client_id_1_booking_ref_1_collation",
        collation=Collation(locale="en", strength=1),
    )
    db.anomaly_banking.create_index([("transaction_id", 1)])
    db.anomaly_claims.create_index([("transaction_id", 1)])
    db.anomaly_claims.create_index([("client_id", 1), ("anomaly_type", 1), ("status", 1), ("deleted", 1)])

    db.trust_fund_v2.create_index([("client_id", 1), ("booking_ref", 1)], collation=Collation(locale="en", strength=1))
    db.trust_fund_v2.create_index(
        [("client_id", 1), ("original_booking_ref", 1)], collation=Collation(locale="en", strength=1)
    )
    db.trust_fund_v2.create_index([("client_id", 1), ("created_at", -1)])
    db.trust_fund_v2.create_index([("client_id", 1), ("balance", 1)])
    db.trust_fund_v2.create_index([("client_id", 1), ("currency_code", 1), ("balance", 1)])
    db.trust_fund_v2.create_index([("booking_status", 1)], collation=Collation(locale="en", strength=1))
    db.trust_fund_v2.create_index([("type", 1)], collation=Collation(locale="en", strength=1))
    db.trust_fund_v2.create_index([("payment_type", 1)], collation=Collation(locale="en", strength=1))



    db.trust_fund_v2.create_index([("client_id", 1), ("booking_ref", 1)], collation=Collation(locale="en", strength=1))
    db.trust_fund_v2.create_index(
        [("client_id", 1), ("original_booking_ref", 1)], collation=Collation(locale="en", strength=1)
    )
    db.trust_fund_v2.create_index([("client_id", 1), ("created_at", -1)])
    db.trust_fund_v2.create_index([("client_id", 1), ("balance", 1)])
    db.trust_fund_v2.create_index([("client_id", 1), ("currency_code", 1), ("balance", 1)])
    db.trust_fund_v2.create_index([("booking_status", 1)], collation=Collation(locale="en", strength=1))
    db.trust_fund_v2.create_index([("type", 1)], collation=Collation(locale="en", strength=1))
    db.trust_fund_v2.create_index([("payment_type", 1)], collation=Collation(locale="en", strength=1))

    db.booking_claim_checks.create_index([("transaction_id", 1)])

    db.opening_closing_balance.create_index([("client_id", 1), ("currency", 1), ("date", 1)], unique=True)

    db.dashboard_risk_exposure.create_index([("client_id", 1), ("currency_code", 1), ("date", 1)], unique=True)
    db.dashboard_risk_exposure.create_index([("updated_at", -1)])

    db.dashboard_exposure_status.create_index([("client_id", 1), ("currency_code", 1)], unique=True)
    db.dashboard_exposure_status.create_index([("updated_at", -1)])

    db.banking_file_details.create_index([("client_id", 1), ("supplier_name", 1)])

    # Create indexes for the new removed_booking_refs collection
    db.removed_booking_refs.create_index([("client_id", 1), ("booking_ref", 1)], collation=Collation(locale="en", strength=1))
    db.removed_booking_refs.create_index([("client_id", 1), ("expired", 1), ("return_date", 1)])
    db.removed_booking_refs.create_index([("file_id", 1)])
    db.removed_booking_refs.create_index([("return_date", 1)])
    db.removed_booking_refs.create_index([("expired", 1)])
    db.removed_booking_refs.create_index([("created_at", 1)])

    # Load Data
    trust_types = [
        {"name": "ATOL Standard"},
        {"name": "Tripartite MA"},
        {"name": "Tripartite Tour Op"},
        {"name": "Non-Flight PTR 2018"},
        {"name": "Non PTR 2018"},
        {"name": "ATOL Gold"},
        {"name": "ATOL Escrow"},
        {"name": "Tripartite Insurer"},
        {"name": "Escrow Trigger"},
    ]

    anomalies = [
        {"name": "Negative Funds in Trust", "description": "Negative cumulative Funds in Trust"},
        {
            "name": "Agent Balance",
            "description": "Agent Balance Funds to be sent into Trust no less than 30 days before Departure Date.",
        },
        {
            "name": "Movement of funds between bookings",
            "description": "Funds transferred from old booking ref to new booking ref should be exactly equal to balance in Trust on old booking.",
        },
        {
            "name": "Funds in Trust Exceed/less than Total Booking Value",
            "description": "Funds in Trust exceed/less than Total Booking Value",
        },
        {
            "name": "Claim Performance Before Return",
            "description": "Claiming funds under Performance Claim when the booking has not completed. Generate this anomaly when Return Date is ahead of the Claim Submitted Date.",
            "custom_field_name": "Number of Days",
        },
        {"name": "Duplicate", "description": "Duplicate claim value"},
        {
            "name": "Claim Too Early For Departure Date",
            "description": "The claim was attempted too early from the configured limits",
            "custom_field_name": "Number of Days",
        },
        {
            "name": "Claim Exceeds Cost Per Pax",
            "description": "Claim value exceeds the maximum allowed for the number of passengers",
            "custom_field_name": "Cost Per Pax",
            "dependencies": ["elements"],
        },
        {"name": "Multi-currency Booking", "description": "Multiple currencies detected for this booking"},
        {
            "name": "Insurance Cover In Date",
            "description": "Insurance date already expired",
            "dependencies": ["elements"],
        },
        {
            "name": "Claim Exceeds agreed % per booking",
            "description": "Claim amount is more than the agreed amount/%age amount per booking",
            "custom_field_name": "Percentage (%)",
        },
        {
            "name": "Max Cap Claim Anomaly (Rollover)",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "custom_field_name": "Max Cap Amount Per Claim",
            "dependencies": ["elements"],
        },
        {
            "name": "Claim SupplierList Anomaly",
            "description": "Compare the supplier names with the client-supplier list",
            "dependencies": ["elements"],
            "from_date_name": "From Date",
            "to_date_name": "To Date",
        },
        {
            "name": "SAFI Insurance Tracker",
            "description": "To check if the amount claimed for a supplier exceeds the configured cap amount.",
        },
        {
            "name": "Commission Claim Anomaly",
            "description": "The commission can be claimed x days within the departure date. So, the anomaly should detect if the departure date exceeds x days from the Claim date.",
            "custom_field_name": "Number of Days",
        },
        {
            "name": "Cruise Claim Anomaly",
            "description": "The claim was attempted too early from the configured limits",
            "custom_field_name": "Number of Days",
        },
        {
            "name": "Max Claim % Exceeded",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "custom_field_name": "Max % Of Elements",
            "dependencies": ["elements"],
        },
        {
            "name": "Claim Exceeds Cost %",
            "description": "Amount exceeds configured percentage of Total Booking Value.",
            "custom_field_name": "Max % Of Total booking value",
            "guarantee_value_name": "Bank Guarantee Value",
            "dependencies": ["elements"],
        },
        {
            "name": "Claim before t+x days",
            "description": "This anomaly is to be enabled if the claim date for any booking is <=t+x to payment date for that booking on that banking report",
            "custom_field_name": "Number of Days",
        },
        {
            "name": "Claim before x days from booking",
            "description": "This anomaly is to be enabled if the claim date is before x days from booking",
            "custom_field_name": "Number of Days",
            "dependencies": ["elements"],
        },
        {
            "name": "Max Cap Limit Anomaly",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "custom_field_name": "Max Cap Amount Per Claim",
            "dependencies": ["elements"],
            "from_date_name": "From Date",
            "to_date_name": "To Date",
            "insurance_name": "Name of Insurance",
        },
        {
            "name": "Departure Within x Days From Booking Date",
            "description": "This anomaly is to be enabled if the departure date is not within x days from booking date",
            "custom_field_name": "Number of Days",
            "dependencies": ["elements"],
        },
        {
            "name": "Trust Bookings Check",
            "description": "This anomaly is to be enabled if the difference between the return date and departure date for any booking is greater than the Number of Days Given",
            "custom_field_name": "Number of Days",
        },
        {
            "name": "Non-Trust Bookings Check",
            "description": "This anomaly is to be enabled if the difference between the return date and departure date for any booking is less than the Number of Days Given or if the booking has multiple elements",
            "custom_field_name": "Number of Days",
        },
        {
            "name": "Bookings Before Live",
            "description": "To check if the booking date is lower than the live date 2024-03-18",
        },
        {
            "name": "Re-claiming Removed Booking",
            "description": "This anomaly is triggered when a booking reference that was previously removed/cancelled from a claim file is being claimed again before its return date has passed. Excludes Performance, Refund, and Cancellation elements.",
        },
    ]

    claim_column = [
        {"name": "Total Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "TotalPax", "column_name": "pax_count", "data_type": "number"},
        {"name": "Total Passengers", "column_name": "pax_count", "data_type": "number"},
        {"name": "Total Passenger", "column_name": "pax_count", "data_type": "number"},
        {"name": "No. of Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "No of Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "Pax Number", "column_name": "pax_count", "data_type": "number"},
        {"name": "PaxCount", "column_name": "pax_count", "data_type": "number", "preferred": True},
        {
            "name": "BookingRef",
            "column_name": "booking_ref",
            "data_type": "string",
            "preferred": True,
            "required": True,
        },
        {
            "name": "Booking Reference",
            "column_name": "booking_ref",
            "data_type": "string",
        },
        {"name": "pnr", "column_name": "booking_ref", "data_type": "string"},
        {"name": "File Number", "column_name": "booking_ref", "data_type": "string"},
        {"name": "File", "column_name": "booking_ref", "data_type": "string"},
        {"name": "Type", "column_name": "type", "data_type": "string", "preferred": True},
        {"name": "Agent/Direct", "column_name": "type", "data_type": "string"},
        {"name": "Trade Agent", "column_name": "type", "data_type": "string"},
        {"name": "Booking Type", "column_name": "type", "data_type": "string"},
        {"name": "TotalBookingValue", "column_name": "total_booking_value", "data_type": "number", "preferred": True},
        {"name": "TBV", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Total Booking Revenue", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Total", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Total Price", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Customer Amount to Pay", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Booking Booking Value", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "LeadPax", "column_name": "lead_pax", "data_type": "string", "preferred": True},
        {"name": "Lead Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Pax Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Guest First Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "First Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Last Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Guest Last Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Operator", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Full Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Operator/Pax", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Client Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Passenger", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Passengers", "column_name": "lead_pax", "data_type": "string"},
        {"name": "LeadName", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Element", "column_name": "element", "data_type": "string", "preferred": True, "required": True},
        {"name": "Element Type", "column_name": "element", "data_type": "string"},
        {"name": "Performance ct", "column_name": "element", "data_type": "string"},
        {"name": "Claim Element", "column_name": "element", "data_type": "string"},
        {"name": "Reason for claim", "column_name": "element", "data_type": "string"},
        {"name": "Deposit or Balance", "column_name": "element", "data_type": "string"},
        {"name": "Claim Type", "column_name": "element", "data_type": "string"},
        {"name": "Cumulative FIT", "column_name": "cumulative_fit", "data_type": "number", "preferred": True},
        {"name": "Claim Amount", "column_name": "amount", "data_type": "number"},
        {"name": "Claims", "column_name": "amount", "data_type": "number"},
        {"name": "Amount", "column_name": "amount", "data_type": "number", "preferred": True, "required": True},
        {"name": "Payment Amount", "column_name": "amount", "data_type": "number"},
        {"name": "Today FIT", "column_name": "amount", "data_type": "number"},
        {"name": "Balance Due", "column_name": "amount", "data_type": "number"},
        {"name": "ATOL Amount", "column_name": "amount", "data_type": "number"},
        {"name": "Remaining Amount", "column_name": "amount", "data_type": "number"},
        {"name": "LCF Amount", "column_name": "amount", "data_type": "number"},
        {"name": "Refund Amount", "column_name": "amount", "data_type": "number"},
        {"name": "Rush Amount", "column_name": "amount", "data_type": "number"},
        {"name": "Amount transferred to Trust", "column_name": "amount", "data_type": "number"},
        {"name": "Today's Claim", "column_name": "amount", "data_type": "number"},
        {"name": "Claimed Value", "column_name": "amount", "data_type": "number"},
        {"name": "DepositAmount", "column_name": "amount", "data_type": "number"},
        {"name": "Claim Value", "column_name": "amount", "data_type": "number"},
        {"name": "Claim Suggested Value", "column_name": "amount", "data_type": "number"},
        {"name": "Trust Balance", "column_name": "amount", "data_type": "number"},
        {"name": "ReturnDate", "column_name": "return_date", "data_type": "date", "preferred": True},
        {"name": "Date of Return", "column_name": "return_date", "data_type": "date"},
        {"name": "End Date", "column_name": "return_date", "data_type": "date"},
        {"name": "Returns", "column_name": "return_date", "data_type": "date"},
        {
            "name": "BookingDate",
            "column_name": "booking_date",
            "data_type": "date",
            "preferred": True,
            "required": True,
        },
        {"name": "Book Date", "column_name": "booking_date", "data_type": "date"},
        {"name": "Booked Date", "column_name": "booking_date", "data_type": "date"},
        {"name": "ConfirmedDate", "column_name": "booking_date", "data_type": "date"},
        {"name": "Vat on Comm", "column_name": "vat_on_commission", "data_type": "string"},
        {"name": "Funds in Trust", "column_name": "funds_in_trust", "data_type": "boolean", "preferred": True},
        {"name": "Cruise Code", "column_name": "supplier_ref", "data_type": "string"},
        {"name": "Payment reference", "column_name": "supplier_ref", "data_type": "string"},
        {"name": "SupplierRef", "column_name": "supplier_ref", "data_type": "string", "preferred": True},
        {"name": "Tour Ref", "column_name": "supplier_ref", "data_type": "string"},
        {"name": "CurrencyCode", "column_name": "currency_code", "data_type": "string", "preferred": True},
        {"name": "Currency", "column_name": "currency_code", "data_type": "string"},
        {
            "name": "DepartureDate",
            "column_name": "departure_date",
            "data_type": "date",
            "preferred": True,
            "required": True,
        },
        {"name": "Date of Travel", "column_name": "departure_date", "data_type": "date"},
        {"name": "Start Date", "column_name": "departure_date", "data_type": "date"},
        {"name": "Head Office", "column_name": "head_office", "data_type": "string", "preferred": True},
        {"name": "Cancellation Date", "column_name": "cancellation_date", "data_type": "date", "preferred": True},
        {"name": "PaymentDate", "column_name": "payment_date", "data_type": "date", "preferred": True},
        {"name": "Receipt/Paid Date", "column_name": "payment_date", "data_type": "date"},
        {"name": "Payments Payment Date Date", "column_name": "payment_date", "data_type": "date"},
        {"name": "Booking Status", "column_name": "booking_status", "data_type": "string", "preferred": True},
        {"name": "Note(Pay In/Withdraw)", "column_name": "note", "data_type": "string", "preferred": True},
        {"name": "Add On Tour", "column_name": "add_on_tour", "data_type": "number", "preferred": True},
        {"name": "DeptBalLeadTime", "column_name": "dept_bal_lead_time", "data_type": "string", "preferred": True},
        {"name": "TicketNo", "column_name": "ticket_no", "data_type": "number", "preferred": True},
        {"name": "Company", "column_name": "company", "data_type": "string", "preferred": True},
        {"name": "Reference", "column_name": "reference", "data_type": "alphanumeric", "preferred": True},
        {"name": "Post/Pre", "column_name": "post_or_pre", "data_type": "string", "preferred": True},
        {"name": "Agent Comm", "column_name": "agent_comm", "data_type": "number", "preferred": True},
        {"name": "Tour", "column_name": "tour", "data_type": "string", "preferred": True},
        {"name": "TDateRule", "column_name": "t_date_rule", "data_type": "number", "preferred": True},
        {"name": "Date Returned", "column_name": "date_returned", "data_type": "date", "preferred": True},
        {"name": "ID", "column_name": "id", "data_type": "alphanumeric", "preferred": True},
        {"name": "Claimed before", "column_name": "claimed_before", "data_type": "string", "preferred": True},
        {"name": "SupplierNames", "column_name": "supplier_names", "data_type": "string", "preferred": True},
        {"name": "SupplierName", "column_name": "supplier_names", "data_type": "string"},
        {"name": "BSP Flag", "column_name": "bsp_flag", "data_type": "string", "preferred": True},
        {"name": "DdateRule", "column_name": "d_date_rule", "data_type": "number", "preferred": True},
        {"name": "Generic", "column_name": "generic", "data_type": "number", "preferred": True},
        {
            "name": "Date Paid/Withdrawn fron Trust",
            "column_name": "date_paid_or_withdrawn_from_trust",
            "data_type": "date",
            "preferred": True,
        },
        {"name": "OptionedDate", "column_name": "optioned_date", "data_type": "date", "preferred": True},
        {"name": "CUM'L CLAIMED VALUE", "column_name": "cuml_claimed_value", "data_type": "number", "preferred": True},
        {"name": "Element Ref", "column_name": "element_ref", "data_type": "string", "preferred": True},
        {"name": "Nights", "column_name": "nights", "data_type": "number", "preferred": True},
        {"name": "Customer", "column_name": "lead_pax", "data_type": "string", "preferred": True},
        {"name": "TRVL INS", "column_name": "trvl_ins", "data_type": "number", "preferred": True},
        {"name": "Fees", "column_name": "fees", "data_type": "number", "preferred": True},
        {"name": "ABTA No", "column_name": "abta_no", "data_type": "string", "preferred": True},
        {"name": "Claim Date", "column_name": "claim_date", "data_type": "date", "preferred": True},
        {"name": "Bonding", "column_name": "bonding", "data_type": "string", "preferred": True},
        {"name": "Non-Trust", "column_name": "non_trust", "data_type": "number", "preferred": True},
        {"name": "Amount Paid", "column_name": "amount_paid", "data_type": "number", "preferred": True},
        {"name": "Payment Method", "column_name": "payment_method", "data_type": "string", "preferred": True},
        {"name": "CustomerType", "column_name": "customer_type", "data_type": "string", "preferred": True},
        {"name": "Difference", "column_name": "difference", "data_type": "number", "preferred": True},
        {"name": "Payment Reference", "column_name": "payment_reference", "data_type": "string", "preferred": True},
        {"name": "Scheduled Flight", "column_name": "scheduled_flight", "data_type": "number", "preferred": True},
        {"name": "Cruise", "column_name": "cruise", "data_type": "number", "preferred": True},
        {"name": "CLAIMED IN MTH", "column_name": "claimed_in_mth", "data_type": "number", "preferred": True},
        {"name": "Receipt/paid", "column_name": "receipt_or_paid", "data_type": "string", "preferred": True},
        {"name": "Car Hire", "column_name": "car_hire", "data_type": "number", "preferred": True},
        {"name": "Parking", "column_name": "parking", "data_type": "number", "preferred": True},
        {"name": "Transfer ", "column_name": "transfer", "data_type": "number", "preferred": True},
        {"name": "Exhibit B Type", "column_name": "exhibit_b_type", "data_type": "string", "preferred": True},
        {"name": "Date Paid", "column_name": "date_paid", "data_type": "string", "preferred": True},
        {"name": "Local Value", "column_name": "local_value", "data_type": "number", "preferred": True},
        {"name": "PaymentType", "column_name": "payment_type", "data_type": "string", "preferred": True},
        {"name": "PaymentType ct", "column_name": "payment_type", "data_type": "string", "preferred": True},
        {"name": "TotalAmount", "column_name": "total_amount", "data_type": "number", "preferred": True},
        {"name": "Days To Process", "column_name": "days_to_process", "data_type": "number", "preferred": True},
        {"name": "Charter Flight", "column_name": "charter_flight", "data_type": "number", "preferred": True},
        {"name": "Validity Date", "column_name": "validity_date", "data_type": "date", "preferred": True},
        {"name": "Total Paid", "column_name": "total_paid", "data_type": "number", "preferred": True},
        {"name": "ATOL", "column_name": "atol", "data_type": "boolean", "preferred": True},
        {"name": "Accom", "column_name": "accom", "data_type": "number", "preferred": True},
        {
            "name": "Method of Payment Summary",
            "column_name": "method_of_payment_summary",
            "data_type": "string",
            "preferred": True,
        },
        {"column_name": "entry_no", "data_type": "number", "name": "EntryNo", "preferred": True},
        {"column_name": "entry_date", "data_type": "date", "name": "EntryDate", "preferred": True},
        {"column_name": "non_bonded_amount", "data_type": "number", "name": "NonBondedAmount", "preferred": True},
        {"column_name": "total_bonded_value", "data_type": "number", "name": "TotalBondedValue", "preferred": True},
        {"column_name": "current_res_status", "data_type": "string", "name": "CurrentResStatus", "preferred": True},
        {"column_name": "res_payment_status", "data_type": "string", "name": "ResPaymentStatus", "preferred": True},
        {"column_name": "cancel_date", "data_type": "date", "name": "CancelDate", "preferred": True},
        {"column_name": "trust_status", "data_type": "string", "name": "TrustStatus"},
        {"column_name": "date_claim_file", "data_type": "date", "name": "DateClaimFile", "preferred": True},
        {"column_name": "ref_claim_file", "data_type": "string", "name": "RefClaimFile", "preferred": True},
        {"column_name": "claim_date", "data_type": "date", "name": "ClaimDate"},
    ]

    banking_column = [
        {"name": "PaxCount", "column_name": "pax_count", "data_type": "number", "preferred": True},
        {"name": "PaxCount / Passenger count", "column_name": "pax_count", "data_type": "number"},
        {"name": "TotalPax", "column_name": "pax_count", "data_type": "number"},
        {"name": "Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "No. of Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "No of Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "No Pax", "column_name": "pax_count", "data_type": "number"},
        {"name": "Total Passengers", "column_name": "pax_count", "data_type": "number"},
        {"name": "Total Passenger", "column_name": "pax_count", "data_type": "number"},
        {"name": "Pax Number", "column_name": "pax_count", "data_type": "number"},
        {"name": "Number of PAX", "column_name": "pax_count", "data_type": "number"},
        {
            "name": "BookingRef",
            "column_name": "booking_ref",
            "data_type": "string",
            "preferred": True,
            "required": True,
        },
        {"name": "File", "column_name": "booking_ref", "data_type": "string"},
        {
            "name": "Booking Reference",
            "column_name": "booking_ref",
            "data_type": "string",
        },
        {"name": "pnr", "column_name": "booking_ref", "data_type": "string"},
        {"name": "Res. No", "column_name": "booking_ref", "data_type": "string"},
        {"name": "Reservation No.", "column_name": "booking_ref", "data_type": "string"},
        {"name": "Tour Ref", "column_name": "supplier_ref", "data_type": "string"},
        {"name": "Payment reference", "column_name": "supplier_ref", "data_type": "string"},
        {"name": "Cruise Code", "column_name": "supplier_ref", "data_type": "string"},
        {"name": "CCTransaction Id", "column_name": "supplier_ref", "data_type": "string"},
        {"name": "SupplierRef", "column_name": "supplier_ref", "data_type": "string", "preferred": True},
        {
            "name": "Payment Method",
            "column_name": "payment_method",
            "data_type": "string",
            "preferred": True,
        },
        {"name": "Pay Methods", "column_name": "payment_method", "data_type": "string"},
        {"name": "PaymentDate", "column_name": "payment_date", "data_type": "date", "preferred": True},
        {"name": "Date", "column_name": "payment_date", "data_type": "date"},
        {"name": "FDMS Receipt Date", "column_name": "payment_date", "data_type": "date"},
        {"name": "Date Entered on Traveller", "column_name": "payment_date", "data_type": "date"},
        {"name": "PXP Receipt Date", "column_name": "payment_date", "data_type": "date"},
        {"name": "Receipt/Paid Date", "column_name": "payment_date", "data_type": "date"},
        {"name": "BookingStatus", "column_name": "booking_status", "data_type": "string", "preferred": True},
        {"name": "Reserv. Status TV on Payment Date", "column_name": "booking_status", "data_type": "string"},
        {"name": "Book Date", "column_name": "booking_date", "data_type": "date"},
        {"name": "Booked Date", "column_name": "booking_date", "data_type": "date"},
        {
            "name": "BookingDate",
            "column_name": "booking_date",
            "data_type": "date",
            "preferred": True,
            "required": True,
        },
        {"name": "ConfirmedDate", "column_name": "booking_date", "data_type": "date"},
        {"name": "Sale Date", "column_name": "booking_date", "data_type": "date"},
        {"name": "Confirmation Date", "column_name": "booking_date", "data_type": "date"},
        {"name": "Receipt or Paid", "column_name": "receipt_or_paid", "data_type": "string"},
        {"name": "Receipt/Paid", "column_name": "receipt_or_paid", "data_type": "string"},
        {"name": "Receipt or Paid Date", "column_name": "receipt_or_paid_date", "data_type": "date"},
        {"name": "Amount", "column_name": "amount", "data_type": "number", "preferred": True, "required": True},
        {"name": "Booking Credit", "column_name": "amount", "data_type": "number"},
        {"name": "Payment Amount", "column_name": "amount", "data_type": "number"},
        {"name": "Payment Made on Last Working Day", "column_name": "amount", "data_type": "number"},
        {"name": "Trust Balance", "column_name": "amount", "data_type": "number"},
        {"name": "Owed to TF", "column_name": "amount", "data_type": "number"},
        {"name": "Amount transferred to Trust", "column_name": "amount", "data_type": "number"},
        {"name": "Today FIT", "column_name": "amount", "data_type": "number"},
        {"name": "Deemed Debit Card Receipts", "column_name": "amount", "data_type": "number"},
        {"name": "SettlementAmount", "column_name": "amount", "data_type": "number"},
        {"name": "ProcessingAmount", "column_name": "processing_amount", "data_type": "number"},
        {"name": "ReturnDate", "column_name": "return_date", "data_type": "date", "preferred": True},
        {"name": "Date of Return", "column_name": "return_date", "data_type": "date"},
        {"name": "End Date", "column_name": "return_date", "data_type": "date"},
        {"name": "Returns", "column_name": "return_date", "data_type": "date"},
        {"name": "Customer Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Pax Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "LeadPax", "column_name": "lead_pax", "data_type": "string", "preferred": True},
        {"name": "Customer", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Guest First Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Guest Last Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "First Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Last Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Operator", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Full Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Operator/Pax", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Client Name", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Leader", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Passenger", "column_name": "lead_pax", "data_type": "string"},
        {"name": "Lead Passengers", "column_name": "lead_pax", "data_type": "string"},
        {"name": "LeadName", "column_name": "lead_pax", "data_type": "string"},
        {"name": "PaymentType", "column_name": "payment_type", "data_type": "string", "preferred": True},
        {"name": "PaymentType ct", "column_name": "payment_type", "data_type": "string", "preferred": True},
        {"name": "Agent/Direct", "column_name": "payment_type", "data_type": "string"},
        {"name": "TotalBookingValue", "column_name": "total_booking_value", "data_type": "number", "preferred": True},
        {"name": "Total Bkg Revenue", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "TTV", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Booking Gross", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Total Price", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Customer Amount to Pay", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Booking Booking Value", "column_name": "total_booking_value", "data_type": "number"},
        {"name": "Dept Date", "column_name": "departure_date", "data_type": "date"},
        {
            "name": "DepartureDate",
            "column_name": "departure_date",
            "data_type": "date",
            "preferred": True,
            "required": True,
        },
        {"name": "Date of Travel", "column_name": "departure_date", "data_type": "date"},
        {"name": "Start Date", "column_name": "departure_date", "data_type": "date"},
        {"name": "Begin Date", "column_name": "departure_date", "data_type": "date"},
        {"name": "Operator or Pax", "column_name": "operator_or_pax", "data_type": "string"},
        {"name": "Txn. Date", "column_name": "txn_date", "data_type": "date", "preferred": True},
        {
            "name": "Total Paid By Customer",
            "column_name": "total_paid_by_customer",
            "data_type": "number",
            "preferred": True,
        },
        {"name": "TicketNo", "column_name": "ticket_no", "data_type": "number", "preferred": True},
        {
            "name": "Non-Supplier Element Gross",
            "column_name": "non_supplier_element_gross",
            "data_type": "number",
            "preferred": True,
        },
        {"name": "Card No.", "column_name": "card_no", "data_type": "string", "preferred": True},
        {
            "name": "Supplier Element Gross",
            "column_name": "supplier_element_gross",
            "data_type": "number",
            "preferred": True,
        },
        {"name": "Balance Due Date", "column_name": "balance_due_date", "data_type": "date", "preferred": True},
        {"name": "Total Deposit", "column_name": "total_deposit", "data_type": "number", "preferred": True},
        {"name": "In Trust", "column_name": "in_trust", "data_type": "boolean", "preferred": True},
        {"name": "Date Received", "column_name": "date_received", "data_type": "date", "preferred": True},
        {"name": "CurrencyCode", "column_name": "currency_code", "data_type": "string", "preferred": True},
        {"name": "SettlementCurrencyCode", "column_name": "currency_code", "data_type": "string"},
        {"name": "ProcessingCurrencyCode", "column_name": "processing_currency_code", "data_type": "string"},
        {"name": "Due", "column_name": "due", "data_type": "number", "preferred": True},
        {"name": "Payment received", "column_name": "payment_received", "data_type": "date", "preferred": True},
        {"name": "Report Date", "column_name": "report_date", "data_type": "date", "preferred": True},
        {"name": "Credit Receipts", "column_name": "credit_receipts", "data_type": "number", "preferred": True},
        {"name": "Ref", "column_name": "ref", "data_type": "string", "preferred": True},
        {"name": "ID", "column_name": "id", "data_type": "number", "preferred": True},
        {"name": "Booking Gross", "column_name": "booking_gross", "data_type": "string", "preferred": True},
        {
            "name": "Date Paid into Trust",
            "column_name": "date_paid_into_trust",
            "data_type": "date",
            "preferred": True,
        },
        {
            "name": "Month Paid into Trust",
            "column_name": "month_paid_into_trust",
            "data_type": "month",
            "preferred": True,
        },
        {"name": "AgentId", "column_name": "agent_id", "data_type": "string", "preferred": True},
        {"name": "Payment Ref", "column_name": "payment_ref", "data_type": "string", "preferred": True},
        {"name": "Cumulative FIT", "column_name": "cumulative_fit", "data_type": "number", "preferred": True},
        {"name": "Trade Agent", "column_name": "trade_agent", "data_type": "string", "preferred": True},
        {
            "name": "Customer Type",
            "column_name": "customer_type",
            "data_type": "string",
            "preferred": True,
        },
        {"name": "Type", "column_name": "type", "data_type": "string", "preferred": True},
        {"name": "Selling Agent Code", "column_name": "type", "data_type": "string"},
        {"name": "Bonding", "column_name": "bonding", "data_type": "string", "preferred": True},
        {"name": "Package Type Code", "column_name": "bonding", "data_type": "string", "preferred": True},
        {"name": "Transfer of Funds", "column_name": "transfer_of_funds", "data_type": "string", "preferred": True},
        {"name": "Days To Process", "column_name": "days_to_process", "data_type": "number", "preferred": True},
        {"name": "SupplierNames", "column_name": "supplier_names", "data_type": "string", "preferred": True},
        {"name": "SupplierName", "column_name": "supplier_names", "data_type": "string"},
        {"name": "Hotel", "column_name": "supplier_names", "data_type": "string"},
        {"name": "Funds Collected", "column_name": "funds_collected", "data_type": "number", "preferred": True},
        {"name": "Entry Number", "column_name": "entry_no", "data_type": "number", "preferred": True},
        {"name": "Entry No.", "column_name": "entry_no", "data_type": "number", "preferred": True},
        {"name": "Entry Date", "column_name": "entry_date", "data_type": "date", "preferred": True},
        {"name": "Entry Type", "column_name": "entry_type", "data_type": "string", "preferred": True},
        {"name": "Current Reservation", "column_name": "current_reservation", "data_type": "string", "preferred": True},
        {
            "name": "Current Reservation Status",
            "column_name": "current_reservation",
            "data_type": "string",
            "preferred": True,
        },
        {"name": "Last Status Update", "column_name": "last_status_update", "data_type": "date"},
        {"name": "Cancel Date", "column_name": "cancel_date", "data_type": "date"},
        {"name": "Customer Number", "column_name": "customer_no", "data_type": "number", "preferred": True},
        {"name": "Customer No.", "column_name": "customer_no", "data_type": "number", "preferred": True},
        {"name": "Journal ID", "column_name": "journal_id", "data_type": "number", "preferred": True},
        {"name": "JournalID", "column_name": "journal_id", "data_type": "number", "preferred": True},
        {"name": "Payment Relation ID", "column_name": "alloc_rec_id", "data_type": "number", "preferred": True},
        {"name": "PaymentRelationID", "column_name": "alloc_rec_id", "data_type": "number", "preferred": True},
        {"name": "AllocRecID", "column_name": "alloc_rec_id", "data_type": "number", "preferred": True},
        {"name": "Alloc Rec ID", "column_name": "alloc_rec_id", "data_type": "number", "preferred": True},
        {"name": "Alloc Date", "column_name": "alloc_date", "data_type": "date"},
        {"name": "AllocDate", "column_name": "alloc_date", "data_type": "date"},
        {"name": "Allocated Amount", "column_name": "allocated_amount", "data_type": "number", "preferred": True},
        {"name": "Remaining Amount", "column_name": "remaining_amount", "data_type": "number", "preferred": True},
        {"name": "Bond Proportion", "column_name": "bond_proportion", "data_type": "number", "preferred": True},
        {"name": "NonBondedAmount", "column_name": "non_bonded_amount", "data_type": "number", "preferred": True},
        {
            "name": "Non-bonded Sales Amount",
            "column_name": "non_bonded_amount",
            "data_type": "number",
            "preferred": True,
        },
        {"name": "Non-Bonded Amount", "column_name": "non_bonded_amount", "data_type": "number", "preferred": True},
        {"name": "Total Bonded Value", "column_name": "total_bonded_value", "data_type": "number", "preferred": True},
        {"name": "Bonded Sales Amount", "column_name": "total_bonded_value", "data_type": "number", "preferred": True},
        {"name": "Date Banking File", "column_name": "date_banking_file", "data_type": "date", "preferred": True},
        {"name": "Date Bank File", "column_name": "date_banking_file", "data_type": "date", "preferred": True},
        {"name": "RefBankingFile", "column_name": "ref_banking_file", "data_type": "string", "preferred": True},
        {"name": "Bank File Reference", "column_name": "ref_banking_file", "data_type": "string", "preferred": True},
        {"name": "Bank File Amount", "column_name": "bank_file_amount", "data_type": "number", "preferred": True},
        {"name": "BankFileAmount", "column_name": "bank_file_amount", "data_type": "number", "preferred": True},
    ]

    frequencies = [{"name": "Daily"}, {"name": "Weekly"}, {"name": "Monthly"}, {"name": "One-off"}]

    default_checks = [
        {
            "name": "Claim Letter",
            "short_name": "LTTR",
            "description": "Agree total amount with claim letter and ensure compliance with requirements of the Trust Deed",
            "applicable_trust": ["All"],
        },
        {
            "name": "Element Totals Match Client Booking System",
            "short_name": "BKNG",
            "description": "Check bookings on client booking system and ensure all data agree with information as per claim report",
            "applicable_trust": ["All"],
        },
        {
            "name": "Payment Confirmation",
            "short_name": "PAYM",
            "description": "Check proof of payment by customer",
            "applicable_trust": ["All"],
        },
        {
            "name": "Booking Confirmation/Client invoice",
            "short_name": "BKNG CONF",
            "description": "Obtain the booking confirmation and ensure itinerary tallies with claim report (Name of lead pax, travel dates, amount etc.)",
            "applicable_trust": ["All"],
        },
        {
            "name": "Flight Tickets",
            "short_name": "TICKETS",
            "description": "Check flight tickets and agree travel details with claim report	",
            "applicable_trust": ["All"],
        },
        {
            "name": "Supplier invoice(s)/statement(s)",
            "short_name": "INV",
            "description": "Obtain the supplier invoices (flight, cruise, accommodation, transfer, etc - where applicable) and agree travel details with claim report",
            "applicable_trust": ["All"],
        },
        {
            "name": "Proof of payment to supplier(s)",
            "short_name": "PAYM SUP",
            "description": "Obtain the proof of payment to supplier and match with claim amount	",
            "applicable_trust": ["All"],
        },
        {
            "name": "Cancellation invoice/email",
            "short_name": "CNCL EVID",
            "description": "Note down reason of cancellation and check email correspondence from customer re the cancellation/refund (if any)",
            "applicable_trust": ["All"],
        },
        {
            "name": "Total claim does not exceed Funds in Trust",
            "short_name": "TC<FIT",
            "description": "Ensure that the amount claimed is less or equal to amount in trust",
            "applicable_trust": ["All"],
        },
        {
            "name": "Performance",
            "short_name": "PERF",
            "description": "Ensure customers have returned ",
            "applicable_trust": ["All"],
        },
        {
            "name": "Proof of refund",
            "short_name": "REF",
            "description": "Obtain proof of refund to customer(s) to confirm funds transferred to customer prior to claim date",
            "applicable_trust": ["All"],
        },
        {
            "name": "Booking Terms and Conditions",
            "short_name": "T&Cs",
            "description": "Obtain Booking T&Cs and confirm client forfeited right for a refund	",
            "applicable_trust": ["All"],
        },
        {
            "name": "SAFI",
            "short_name": "SAFI",
            "description": "For LCF and BSP, ensure that SAFI has been approved and in date",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "SAFI cover",
            "short_name": "SAFI LMT",
            "description": "Ensure that total claim is <=  the annual aggregated policy limit",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "List of approved suppliers",
            "short_name": "APP SUP",
            "description": "Ensure that flight/cruise claim are for suppliers that are within the approved list of suppliers covered by the insurance",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "ATOL Certificate",
            "short_name": "ATOL CERT",
            "description": "Obtain the ATOL Certificate for ATOL bookings and ensure the booking is protected by ATOL. Check for client/supplier (the principle) ATOL number",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Cruise Protection Certificate",
            "short_name": "CPC",
            "description": "For cruise component - ensure that the supplier is covered under CPC or any other relevant insurance",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Cruise Cover",
            "short_name": "Cruise COV",
            "description": "Ensure that the departure date is within xxx days of the date the booking was created",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Proof of payment to IATA",
            "short_name": "IATA",
            "description": "Check proof of payment to IATA",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Billing Statement",
            "short_name": "STATM",
            "description": "Obtained BSP Statament and check ticket number and amount",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Proof of payment via card issuer",
            "short_name": "CARD STMNT",
            "description": "Obtain statement from card issuer and match claim amount",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "CAA approval of card issuer",
            "short_name": "CAA APP",
            "description": "Obtain confirmation from CAA for approval of card issuer",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Proof of payment to ATT",
            "short_name": "APC",
            "description": "For APC claim, obtain proof that payment has been made to the CAA",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Acknowledgement from ATT for APC funds",
            "short_name": "ACK",
            "description": "Obtain confirmation from CAA that APC funds have been received by the ATT",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "ATOL Invoice Checked",
            "short_name": "ATOL",
            "description": "Obtain the ATOL Invoice and check for client ATOL number. ",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "ATOL Licence",
            "short_name": "ATOL LIC",
            "description": "Check that ATOL Holder's Licence is in date",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Claim date <= xx days/weeks from dep. date",
            "short_name": "CD<=DEP-xx days/weeks",
            "description": "Claim date should be less or equal to xx days/weeks from departure date",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "Profit report",
            "short_name": "PRFT",
            "description": "For commission claims, request and check profit reports",
            "applicable_trust": ["ATOL Standard", "ATOL Gold", "ATOL Escrow"],
        },
        {
            "name": "ASPP",
            "short_name": "ASPP",
            "description": "Ensure ASPP approved by the insurer and is in date",
            "applicable_trust": ["Non-Flight PTR 2018"],
        },
        {
            "name": "ASPP Claim Amount",
            "short_name": "AMT",
            "description": "Ensure that claim amount does not exceed xx% of the absolute value per booking",
            "applicable_trust": ["Non-Flight PTR 2018"],
        },
        {
            "name": "ASPP Claim Limit",
            "short_name": "LIM",
            "description": "Ensure that the total amount per claim is <= £xx",
            "applicable_trust": ["Non-Flight PTR 2018"],
        },
        {
            "name": "Claim date <= xx days/weeks from dep. date",
            "short_name": "CD<=DEP-xx days/weeks",
            "description": "Claim date should be less or equal to xx days/weeks from departure date",
            "applicable_trust": ["Tripartite MA"],
        },
        {
            "name": "Deposit",
            "short_name": "DEP",
            "description": "Ensure that Deposit is no more than x% of the funds in trust",
            "applicable_trust": ["Tripartite MA"],
        },
        {
            "name": "Deposit TO",
            "short_name": "DEP TO",
            "description": "Ensure that Deposit amount claimed is no more than £xx per pax",
            "applicable_trust": ["Tripartite Tour Op"],
        },
        {
            "name": "Claim date <= xx days/weeks from dep. date",
            "short_name": "CD<=DEP-xx days/weeks",
            "description": "Claim date should be less or equal to xx days/weeks from departure date",
            "applicable_trust": ["Tripartite Tour Op"],
        },
    ]

    currencies_list = [
        {"code": "GBP", "name": "British Pound", "symbol": "£"},
        {"code": "EUR", "name": "Euro", "symbol": "€"},
        {"code": "USD", "name": "United States Dollar", "symbol": "$"},
        {"code": "NOK", "name": "Norwegian Krone", "symbol": "kr"},
        {"code": "CAD", "name": "Canadian Dollar", "symbol": "$"},
        {"code": "DKK", "name": "Danish Krone", "symbol": "kr"},
        {"code": "ALL", "name": "Albanian Lek", "symbol": "Lek"},
        {"code": "XCD", "name": "East Caribbean Dollar", "symbol": "$"},
        {"code": "BBD", "name": "Barbadian Dollar", "symbol": "$"},
        {"code": "BTN", "name": "Bhutanese Ngultrum", "symbol": ""},
        {"code": "BND", "name": "Brunei Dollar", "symbol": "$"},
        {"code": "XAF", "name": "Central African CFA Franc", "symbol": ""},
        {"code": "CUP", "name": "Cuban Peso", "symbol": "$"},
        {"code": "FKP", "name": "Falkland Islands Pound", "symbol": "£"},
        {"code": "GIP", "name": "Gibraltar Pound", "symbol": "£"},
        {"code": "HUF", "name": "Hungarian Forint", "symbol": "Ft"},
        {"code": "IRR", "name": "Iranian Rial", "symbol": "﷼"},
        {"code": "JMD", "name": "Jamaican Dollar", "symbol": "J$"},
        {"code": "AUD", "name": "Australian Dollar", "symbol": "$"},
        {"code": "LAK", "name": "Lao Kip", "symbol": "₭"},
        {"code": "LYD", "name": "Libyan Dinar", "symbol": ""},
        {"code": "MKD", "name": "Macedonian Denar", "symbol": "дeн"},
        {"code": "XOF", "name": "West African CFA Franc", "symbol": ""},
        {"code": "NZD", "name": "New Zealand Dollar", "symbol": "$"},
        {"code": "OMR", "name": "Omani Rial", "symbol": "﷼"},
        {"code": "PGK", "name": "Papua New Guinean Kina", "symbol": ""},
        {"code": "RWF", "name": "Rwandan Franc", "symbol": ""},
        {"code": "WST", "name": "Samoan Tala", "symbol": ""},
        {"code": "RSD", "name": "Serbian Dinar", "symbol": "Дин."},
        {"code": "SEK", "name": "Swedish Krona", "symbol": "kr"},
        {"code": "TZS", "name": "Tanzanian Shilling", "symbol": "TSh"},
        {"code": "AMD", "name": "Armenian Dram", "symbol": ""},
        {"code": "BSD", "name": "Bahamian Dollar", "symbol": "$"},
        {"code": "BAM", "name": "Bosnia And Herzegovina Konvertibilna Marka", "symbol": "KM"},
        {"code": "CVE", "name": "Cape Verdean Escudo", "symbol": ""},
        {"code": "CNY", "name": "Chinese Yuan", "symbol": "¥"},
        {"code": "CRC", "name": "Costa Rican Colon", "symbol": "₡"},
        {"code": "CZK", "name": "Czech Koruna", "symbol": "Kč"},
        {"code": "ERN", "name": "Eritrean Nakfa", "symbol": ""},
        {"code": "GEL", "name": "Georgian Lari", "symbol": ""},
        {"code": "HTG", "name": "Haitian Gourde", "symbol": ""},
        {"code": "INR", "name": "Indian Rupee", "symbol": "₹"},
        {"code": "JOD", "name": "Jordanian Dinar", "symbol": ""},
        {"code": "KRW", "name": "South Korean Won", "symbol": "₩"},
        {"code": "LBP", "name": "Lebanese Lira", "symbol": "£"},
        {"code": "MWK", "name": "Malawian Kwacha", "symbol": ""},
        {"code": "MRO", "name": "Mauritanian Ouguiya", "symbol": ""},
        {"code": "MZN", "name": "Mozambican Metical", "symbol": ""},
        {"code": "ANG", "name": "Netherlands Antillean Gulden", "symbol": "ƒ"},
        {"code": "PEN", "name": "Peruvian Nuevo Sol", "symbol": "S/."},
        {"code": "QAR", "name": "Qatari Riyal", "symbol": "﷼"},
        {"code": "STD", "name": "Sao Tome And Principe Dobra", "symbol": ""},
        {"code": "SLL", "name": "Sierra Leonean Leone", "symbol": ""},
        {"code": "SOS", "name": "Somali Shilling", "symbol": "S"},
        {"code": "SDG", "name": "Sudanese Pound", "symbol": ""},
        {"code": "SYP", "name": "Syrian Pound", "symbol": "£"},
        {"code": "AOA", "name": "Angolan Kwanza", "symbol": ""},
        {"code": "AWG", "name": "Aruban Florin", "symbol": "ƒ"},
        {"code": "BHD", "name": "Bahraini Dinar", "symbol": ""},
        {"code": "BZD", "name": "Belize Dollar", "symbol": "BZ$"},
        {"code": "BWP", "name": "Botswana Pula", "symbol": "P"},
        {"code": "BIF", "name": "Burundi Franc", "symbol": ""},
        {"code": "KYD", "name": "Cayman Islands Dollar", "symbol": ""},
        {"code": "COP", "name": "Colombian Peso", "symbol": "$"},
        {"code": "GTQ", "name": "Guatemalan Quetzal", "symbol": "Q"},
        {"code": "HNL", "name": "Honduran Lempira", "symbol": "L"},
        {"code": "IDR", "name": "Indonesian Rupiah", "symbol": "Rp"},
        {"code": "ILS", "name": "Israeli New Sheqel", "symbol": "₪"},
        {"code": "KZT", "name": "Kazakhstani Tenge", "symbol": ""},
        {"code": "KWD", "name": "Kuwaiti Dinar", "symbol": ""},
        {"code": "LSL", "name": "Lesotho Loti", "symbol": ""},
        {"code": "MYR", "name": "Malaysian Ringgit", "symbol": "RM"},
        {"code": "MUR", "name": "Mauritian Rupee", "symbol": "₨"},
        {"code": "MNT", "name": "Mongolian Tugrik", "symbol": "₮"},
        {"code": "MMK", "name": "Myanma Kyat", "symbol": ""},
        {"code": "NGN", "name": "Nigerian Naira", "symbol": "₦"},
        {"code": "PAB", "name": "Panamanian Balboa", "symbol": "B/."},
        {"code": "PHP", "name": "Philippine Peso", "symbol": "₱"},
        {"code": "RON", "name": "Romanian Leu", "symbol": "lei"},
        {"code": "SAR", "name": "Saudi Riyal", "symbol": "﷼"},
        {"code": "SGD", "name": "Singapore Dollar", "symbol": "$"},
        {"code": "ZAR", "name": "South African Rand", "symbol": "R"},
        {"code": "SRD", "name": "Surinamese Dollar", "symbol": "$"},
        {"code": "TWD", "name": "New Taiwan Dollar", "symbol": "NT$"},
        {"code": "TOP", "name": "Paanga", "symbol": ""},
        {"code": "VEF", "name": "Venezuelan Bolivar", "symbol": ""},
        {"code": "DZD", "name": "Algerian Dinar", "symbol": ""},
        {"code": "ARS", "name": "Argentine Peso", "symbol": "$"},
        {"code": "AZN", "name": "Azerbaijani Manat", "symbol": "мaн"},
        {"code": "BYR", "name": "Belarusian Ruble", "symbol": "p."},
        {"code": "BOB", "name": "Bolivian Boliviano", "symbol": "$b"},
        {"code": "BGN", "name": "Bulgarian Lev", "symbol": "лв"},
        {"code": "CLP", "name": "Chilean Peso", "symbol": "$"},
        {"code": "CDF", "name": "Congolese Franc", "symbol": ""},
        {"code": "DOP", "name": "Dominican Peso", "symbol": "RD$"},
        {"code": "FJD", "name": "Fijian Dollar", "symbol": "$"},
        {"code": "GMD", "name": "Gambian Dalasi", "symbol": ""},
        {"code": "GYD", "name": "Guyanese Dollar", "symbol": "$"},
        {"code": "ISK", "name": "Icelandic Króna", "symbol": "kr"},
        {"code": "IQD", "name": "Iraqi Dinar", "symbol": ""},
        {"code": "JPY", "name": "Japanese Yen", "symbol": "¥"},
        {"code": "KPW", "name": "North Korean Won", "symbol": "₩"},
        {"code": "LVL", "name": "Latvian Lats", "symbol": "Ls"},
        {"code": "CHF", "name": "Swiss Franc", "symbol": "Fr."},
        {"code": "MGA", "name": "Malagasy Ariary", "symbol": ""},
        {"code": "MDL", "name": "Moldovan Leu", "symbol": ""},
        {"code": "MAD", "name": "Moroccan Dirham", "symbol": ""},
        {"code": "NPR", "name": "Nepalese Rupee", "symbol": "₨"},
        {"code": "NIO", "name": "Nicaraguan Cordoba", "symbol": "C$"},
        {"code": "PKR", "name": "Pakistani Rupee", "symbol": "₨"},
        {"code": "PYG", "name": "Paraguayan Guarani", "symbol": "Gs"},
        {"code": "SHP", "name": "Saint Helena Pound", "symbol": "£"},
        {"code": "SCR", "name": "Seychellois Rupee", "symbol": "₨"},
        {"code": "SBD", "name": "Solomon Islands Dollar", "symbol": "$"},
        {"code": "LKR", "name": "Sri Lankan Rupee", "symbol": "₨"},
        {"code": "THB", "name": "Thai Baht", "symbol": "฿"},
        {"code": "TRY", "name": "Turkish New Lira", "symbol": ""},
        {"code": "AED", "name": "UAE Dirham", "symbol": ""},
        {"code": "VUV", "name": "Vanuatu Vatu", "symbol": ""},
        {"code": "YER", "name": "Yemeni Rial", "symbol": "﷼"},
        {"code": "AFN", "name": "Afghan Afghani", "symbol": "؋"},
        {"code": "BDT", "name": "Bangladeshi Taka", "symbol": ""},
        {"code": "BRL", "name": "Brazilian Real", "symbol": "R$"},
        {"code": "KHR", "name": "Cambodian Riel", "symbol": "៛"},
        {"code": "KMF", "name": "Comorian Franc", "symbol": ""},
        {"code": "HRK", "name": "Croatian Kuna", "symbol": "kn"},
        {"code": "DJF", "name": "Djiboutian Franc", "symbol": ""},
        {"code": "EGP", "name": "Egyptian Pound", "symbol": "£"},
        {"code": "ETB", "name": "Ethiopian Birr", "symbol": ""},
        {"code": "XPF", "name": "CFP Franc", "symbol": ""},
        {"code": "GHS", "name": "Ghanaian Cedi", "symbol": ""},
        {"code": "GNF", "name": "Guinean Franc", "symbol": ""},
        {"code": "HKD", "name": "Hong Kong Dollar", "symbol": "$"},
        {"code": "XDR", "name": "Special Drawing Rights", "symbol": ""},
        {"code": "KES", "name": "Kenyan Shilling", "symbol": "KSh"},
        {"code": "KGS", "name": "Kyrgyzstani Som", "symbol": "лв"},
        {"code": "LRD", "name": "Liberian Dollar", "symbol": "$"},
        {"code": "MOP", "name": "Macanese Pataca", "symbol": ""},
        {"code": "MVR", "name": "Maldivian Rufiyaa", "symbol": ""},
        {"code": "MXN", "name": "Mexican Peso", "symbol": "$"},
        {"code": "NAD", "name": "Namibian Dollar", "symbol": "$"},
        {"code": "PLN", "name": "Polish Zloty", "symbol": "zł"},
        {"code": "RUB", "name": "Russian Ruble", "symbol": "py6"},
        {"code": "SZL", "name": "Swazi Lilangeni", "symbol": ""},
        {"code": "TJS", "name": "Tajikistani Somoni", "symbol": ""},
        {"code": "TTD", "name": "Trinidad and Tobago Dollar", "symbol": "TT$"},
        {"code": "UGX", "name": "Ugandan Shilling", "symbol": "USh"},
        {"code": "UYU", "name": "Uruguayan Peso", "symbol": "$U"},
        {"code": "VND", "name": "Vietnamese Dong", "symbol": "₫"},
        {"code": "TND", "name": "Tunisian Dinar", "symbol": ""},
        {"code": "UAH", "name": "Ukrainian Hryvnia", "symbol": "₴"},
        {"code": "UZS", "name": "Uzbekistani Som", "symbol": "лв"},
        {"code": "TMT", "name": "Turkmenistan Manat", "symbol": ""},
        {"code": "ZMW", "name": "Zambian Kwacha", "symbol": ""},
        {"code": "BTC", "name": "Bitcoin", "symbol": "BTC"},
        {"code": "BYN", "name": "New Belarusian Ruble", "symbol": "p."},
    ]

    claim_elements = [
        {"name": "Charter Flight (Price)"},
        {"name": "Scheduled Flight (Price)"},
        {"name": "Accom (Cost)"},
        {"name": "Transfer (Cost)"},
        {"name": "Parking (Cost)"},
        {"name": "Car Hire (Cost)"},
        {"name": "Tour (Cost)"},
        {"name": "Add On Tour (Cost)"},
        {"name": "Generic (Cost)"},
        {"name": "Cruise (Cost)"},
        {"name": "TRVL INS (Cost)"},
        {"name": "Output VAT"},
        {"name": "Fees (with cost only)"},
        {"name": "TD Profit"},
        {"name": "Cancellation"},
        {"name": "Commission"},
        {"name": "VAT on Commission"},
        {"name": "EJH Deposit"},
        {"name": "EJH Balance"},
        {"name": "Performance"},
        {"name": "Accommodation"},
        {"name": "Parking"},
        {"name": "Car Hire"},
        {"name": "Transfer"},
        {"name": "Tour"},
        {"name": "Add On Tour"},
        {"name": "Generic"},
        {"name": "Cruise"},
        {"name": "Fees"},
        {"name": "Returning Customers"},
        {"name": "ATOL"},
        {"name": "Flight Plus"},
        {"name": "Package"},
        {"name": "Refund"},
        {"name": "Refunds"},
        {"name": "APC"},
        {"name": "Travel Insurance"},
        {"name": "Insurance"},
        {"name": "INS"},
        {"name": "CPC"},
        {"name": "SAFI"},
        {"name": "SFC"},
        {"name": "Flight"},
        {"name": "Low Cost Flight"},
        {"name": "Low Cost Flights"},
        {"name": "LCF"},
        {"name": "Charter Flight"},
        {"name": "Chartered Flight"},
        {"name": "Scheduled Flight"},
        {"name": "Non-Trust"},
        {"name": "Flight Deposit"},
        {"name": "Flight Balance"},
        {"name": "Corporate Credit Card Flight"},
        {"name": "Flights paid by CC"},
        {"name": "BSP"},
        {"name": "Cancellation charge adjustment"},
        {"name": "Assumption"},
        {"name": "Refund Assumptions Reversal"},
        {"name": "Trade Assumption Balance Reversal"},
        {"name": "Trade Assumption Deposit Reversal"},
        {"name": "Amendment fee"},
        {"name": "ASPP"},
        {"name": "Deposit"},
        {"name": "DEP"},
        {"name": "Balance"},
        {"name": "BAL"},
        {"name": "Non-Atol"},
        {"name": "Early Drawdown"},
    ]

    mandatory_checks = [
        {
            "applicable_trust": "ATOL Standard",
            "element": "performance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking confirmation",
                "Flight Tickets",
                "Supplier invoice(s)/statement(s)",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "refund",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Proof of refund",
                "Cancellation invoice/email",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "non-trust",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "cruise",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "SAFI",
                "List of approved suppliers",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "cancellation",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Cancellation invoice/email",
                "Booking Terms and Conditions",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "scheduled flight",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "ATOL Certificate",
                "Supplier invoice(s)/statement(s)",
                "List of approved suppliers",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "bsp",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "ATOL Certificate",
                "Proof of payment to supplier(s)",
                "SAFI",
                "List of approved suppliers",
                "Proof of payment to IATA",
                "Billing Statement",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "flight bsp",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "ATOL Certificate",
                "Proof of payment to supplier(s)",
                "SAFI",
                "List of approved suppliers",
                "Billing Statement",
                "Proof of payment via card issuer",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "chartered flight",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "ATOL Certificate",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "SAFI",
                "List of approved suppliers",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "lcf",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "ATOL Certificate",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "List of approved suppliers",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "flight lcf",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "ATOL Certificate",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "List of approved suppliers",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "flights paid by cc",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "ATOL Certificate",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
            ],
        },
        {
            "applicable_trust": "ATOL Standard",
            "element": "insurance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "SAFI",
            ],
        },
        {
            "applicable_trust": "ATOL Gold",
            "element": "insurance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Flight Tickets",
                "Supplier invoice(s)/statement(s)",
            ],
        },
        {
            "applicable_trust": "ATOL Gold",
            "element": "non-trust",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
            ],
        },
        {
            "applicable_trust": "ATOL Gold",
            "element": "refund",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Proof of refund",
                "Cancellation invoice/email",
            ],
        },
        {
            "applicable_trust": "ATOL Gold",
            "element": "cancellation",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Cancellation invoice/email",
                "Booking Terms and Conditions",
            ],
        },
        {
            "applicable_trust": "Tripartite Tour Op",
            "element": "deposit",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "Booking Terms and Conditions",
                "Deposit TO",
            ],
        },
        {
            "applicable_trust": "Tripartite Tour OP",
            "element": "balance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "Booking Terms and Conditions",
                "Claim date <= xx days/weeks from dep. date",
            ],
        },
        {
            "applicable_trust": "Tripartite Tour OP",
            "element": "non-trust",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
            ],
        },
        {
            "applicable_trust": "Tripartite Tour OP",
            "element": "refund",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "Proof of refund",
                "Cancellation invoice/email",
            ],
        },
        {
            "applicable_trust": "Tripartite Tour OP",
            "element": "cancellation",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Cancellation invoice/email",
                "Booking Terms and Conditions",
            ],
        },
        {
            "applicable_trust": "Tripartite Tour OP",
            "element": "commission",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "Proof of refund",
                "Booking Terms and Conditions",
                "Claim date <= xx days/weeks from dep. date",
            ],
        },
        {
            "applicable_trust": "Tripartite MA",
            "element": "performance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "Paysafe Approval",
                "Claim date <= xx days/weeks from dep. date",
            ],
        },
        {
            "applicable_trust": "Tripartite MA",
            "element": "refund",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of refund",
                "Cancellation invoice/email",
            ],
        },
        {
            "applicable_trust": "Tripartite MA",
            "element": "non-trust",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
            ],
        },
        {
            "applicable_trust": "Tripartite MA",
            "element": "cancellation",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Cancellation invoice/email",
                "Booking Terms and Conditions",
            ],
        },
        {
            "applicable_trust": "Tripartite MA",
            "element": "commission",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of payment to supplier(s)",
                "Proof of refund",
                "Booking Terms and Conditions",
                "Claim date <= xx days/weeks from dep. date",
            ],
        },
        {
            "applicable_trust": "Tripartite Insurer",
            "element": "performance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
            ],
        },
        {
            "applicable_trust": "Tripartite Insurer",
            "element": "refund",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Proof of refund",
                "Cancellation invoice/email",
            ],
        },
        {
            "applicable_trust": "Tripartite Insurer",
            "element": "non-trust",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
            ],
        },
        {
            "applicable_trust": "Tripartite Insurer",
            "element": "cancellation",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Cancellation invoice/email",
                "Booking Terms and Conditions",
            ],
        },
        {
            "applicable_trust": "Non-Flight PTR 2018",
            "element": "performance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Claim date <= xx days/weeks from dep. date",
            ],
        },
        {
            "applicable_trust": "Non-Flight PTR 2018",
            "element": "refund",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Supplier invoice(s)/statement(s)",
                "Proof of refund",
                "Cancellation invoice/email",
            ],
        },
        {
            "applicable_trust": "Non-Flight PTR 2018",
            "element": "non-trust",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
            ],
        },
        {
            "applicable_trust": "Non-Flight PTR 2018",
            "element": "cancellation",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
                "Cancellation invoice/email",
                "Booking Terms and Conditions",
            ],
        },
        {
            "applicable_trust": "Non-Flight PTR 2018",
            "element": "insurance",
            "mandatory_checks": [
                "Payment Confirmation",
                "Booking Confirmation/Client invoice",
            ],
        },
    ]

    banking_elements = [
        {"name": "Flight"},
        {"name": "Flight BSP"},
        {"name": "Flight Corp Card"},
        {"name": "Insurance"},
        {"name": "Cruise"},
        {"name": "Coach"},
        {"name": "Commission"},
        {"name": "Accommodation"},
        {"name": "Performance"},
        {"name": "Car hire"},
        {"name": "Tours"},
        {"name": "Deposit"},
        {"name": "Balance"},
        {"name": "Non-trust"},
        {"name": "Banking"},
        {"name": "Refund Assumptions Reversal"},
        {"name": "FlightCorpCard"},
        {"name": "Transfers"},
    ]

    for trust_type in trust_types:
        db.lookup_trust_type.update_one(trust_type, {"$set": trust_type}, upsert=True)
    for anomaly in anomalies:
        db.lookup_anomaly.update_one(anomaly, {"$set": anomaly}, upsert=True)
    for frequency in frequencies:
        db.lookup_frequency.update_one(frequency, {"$set": frequency}, upsert=True)
    for claim in claim_column:
        db.lookup_claim.update_one(claim, {"$set": claim}, upsert=True)
    for element in claim_elements:
        db.lookup_claim_elements.update_one(element, {"$set": element}, upsert=True)
    for item in banking_column:
        db.lookup_banking.update_one(item, {"$set": item}, upsert=True)
    for default_check in default_checks:
        db.lookup_default_checks.update_one(default_check, {"$set": default_check}, upsert=True)
    order = 0
    for currencies in currencies_list:
        db.lookup_currency.update_one(currencies, {"$set": {**currencies, "order": order}}, upsert=True)
        order += 1
    for checks in mandatory_checks:
        db.claim_mandatory_checks.update_one(checks, {"$set": checks}, upsert=True)
    for element in banking_elements:
        db.lookup_banking_elements.update_one(element, {"$set": element}, upsert=True)

    # add_admins()

    print("DB setup completed successfully")


if __name__ == "__main__":
    add_admins()