import os
import datetime
import boto3
import pymongo
from pymongo import MongoClient
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
import logging
import requests
import tempfile
import time

url = os.environ["URL"]
TEMP_DIR = tempfile.gettempdir()
details = get_secret("eu-west-2", False, True)
user_name, password, mongo_db = details
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()
if logging.getLogger().hasHandlers():
    logging.getLogger().setLevel(logging.INFO)
else:
    logging.basicConfig(level=logging.INFO)

client_id = os.environ.get("GTL")


def lambda_handler(event, context):
    s3 = boto3.client("s3")
    logging.info("user logging in")
    user_auth_token = requests.post(f"{url}/login", json={"username": user_name, "password": password}).json()
    token = user_auth_token["authenticationResult"]["AccessToken"]
    logging.info("returned auth_token")
    token_headers = {"Authorization": f"Bearer {token}"}

    logging.info("started generating gtl tbr")
    generate_tbr_url = f"{url}/reports/trust-balance/export?client={client_id}&size=&currency="

    presigned_details = requests.get(
        generate_tbr_url,
        headers=token_headers,
    )
    if presigned_details.status_code == 202:
        logging.info(presigned_details.json())
        logging.info("generated gtl tbr")
    else:
        logging.info(presigned_details.json())
        requests.get(f"{url}/logout", headers=token_headers)
        raise Exception("tbr generation failed")

    time.sleep(20)
    today = datetime.date.today()
    latest_tbr = db.report_files.find_one(
        {"client_id": ObjectId(client_id), "file_type": "csv"}, sort=[("generated_at", pymongo.DESCENDING)]
    )
    tbr_file_id = latest_tbr.get("file_id")
    logging.info("started downloading tbr file from s3 and saving to temp directory")
    s3.download_file(os.environ["REPORTS_BUCKET"], tbr_file_id, f"{TEMP_DIR}/{today.strftime('%Y%m%d')}-TBR-GTL")
    logging.info("file downloaded and saved")

    logging.info("started uploading tbr file to s3")
    file_key = f"GTouring/Reports/Trust-balance-report_G Touring Ltd {today}.csv"
    s3.upload_file(f"{TEMP_DIR}/{today.strftime('%Y%m%d')}-TBR-GTL", os.environ["PTT_BUCKET"], file_key)
    logging.info("file successfully uploaded")
    requests.get(f"{url}/logout", headers=token_headers)
    logging.info("user logged out")
