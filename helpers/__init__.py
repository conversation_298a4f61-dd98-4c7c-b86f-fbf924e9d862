import csv
from datetime import datetime
import math
import string
import xlsxwriter
import tempfile

TEMP_DIR = tempfile.gettempdir()


def round(n, decimals=0):
    multiplier = 10**decimals
    return math.floor(n * multiplier + 0.5) / multiplier


def track_opening_closing_balance_changes(
    db, transaction: dict, amount_diference: float, file_date: str, session
) -> None:
    datetime_today_min = datetime.combine(datetime.utcnow(), datetime.min.time())
    if transaction["created_at"] < datetime_today_min:
        db.opening_closing_balance_changes.update_one(
            {
                "client_id": transaction["client_id"],
                "currency": transaction["currency_code"],
                "file_date": file_date,
                "date": datetime_today_min,
            },
            {
                "$setOnInsert": {
                    "client_id": transaction["client_id"],
                    "currency": transaction["currency_code"],
                    "file_date": file_date,
                    "date": datetime_today_min,
                },
                "$inc": {"amount": amount_diference},
            },
            upsert=True,
            session=session,
        )


def date_format(value):
    for date in (
        "%Y-%m-%d",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y/%m/%d",
        "%d-%m-%Y",
        "%d-%m-%Y %H:%M:%S",
        "%d-%m-%Y %H:%M:%S.%f",
        "%d/%m/%Y",
    ):
        try:
            return datetime.strptime(value, date).strftime("%Y-%m-%d")
        except ValueError:
            pass
    raise ValueError()


def generate_excel_report(header_dict, data_list, name):
    amount_list = [
        "amounts",
        "amount",
        "funds_difference",
        "total_booking_value",
        "minAmount",
        "maxAmount",
        "total",
        "deposits",
        "totalBanked",
        "totalClaimed",
        "balance",
        "validClaimAmount",
        "voidClaimAmount",
        "claimFileTotal",
        "bankingFileTotal",
        "value",
        "refundsFromDepositFile",
        "totalAmount",
    ]
    workbook = xlsxwriter.Workbook(f"{TEMP_DIR}/{name}")
    worksheet = workbook.add_worksheet()
    letter = list(string.ascii_uppercase)
    for data in data_list:
        for i in range(len(data)):
            worksheet.set_column(f"{letter[i]}:{letter[i]}", 20)

    merge_left = workbook.add_format({"size": 11, "align": "left"})
    merge_left_head = workbook.add_format({"bold": 1, "size": 11, "align": "left"})

    amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right"})

    row = 0
    col = 0
    head_dict = {}
    for key, value in header_dict.items():
        worksheet.write(row, col, value, merge_left_head)
        head_dict[key] = col
        col += 1
    row = 1
    for data in data_list:
        for key, value in data.items():
            if key not in head_dict.keys():
                continue
            if key in amount_list:
                worksheet.write(row, head_dict[key], value, amount_format)
            else:
                worksheet.write(row, head_dict[key], value, merge_left)
        row += 1
    workbook.close()


def generate_csv_report(header_dict, data_list, name):
    amount_list = [
        "amounts",
        "amount",
        "minAmount",
        "maxAmount",
        "total",
        "deposits",
        "totalBanked",
        "totalClaimed",
        "balance",
        "validClaimAmount",
        "voidClaimAmount",
        "claimFileTotal",
        "bankingFileTotal",
        "value",
        "refundsFromDepositFile",
        "totalAmount",
    ]
    workbook = open(f"{TEMP_DIR}/{name}", "w")
    writer = csv.writer(workbook, delimiter=",")
    writer.writerow(header_dict.values())

    for data in data_list:
        value_list = []
        for key in header_dict.keys():
            value = data.get(key)
            if key in amount_list:
                value = round(value, 2)
            value_list.append(value)
        writer.writerow(value_list)

    workbook.close()
