from datetime import datetime
import os
from decimal import ROUND_HALF_UP, Decimal


MAJOR_TRAVEL = os.environ.get("MAJOR_TRAVEL")
ANGLIA = os.environ.get("ANGLIA_TOURS")
WST = os.environ.get("WST_TRAVEL")


def calculate_date_difference_in_days(payment_date, departure_date):
    return (datetime.strptime(departure_date, "%Y-%m-%d") - datetime.strptime(payment_date, "%Y-%m-%d")).days


def calculate_escrow_amount(amount, escrow_multiplier):
    amount_str = str(amount)
    escrow_str = str(escrow_multiplier)
    escrow_amount = Decimal(escrow_str) * Decimal(amount_str)
    rounded_result = escrow_amount.quantize(Decimal("0.00"), rounding=ROUND_HALF_UP)
    amount = float(rounded_result)
    return amount


def escrow_handler(amount, client_id=None, booking_date=None, escrow_multiplier=0.7):
    non_trust_flag = False
    if (
        client_id == MAJOR_TRAVEL
        and booking_date is not None
        and (datetime.strptime(booking_date, "%Y-%m-%d")) < datetime(2022, 4, 1)
    ):
        escrow_multiplier = 0.5
    elif (
        (client_id == ANGLIA or client_id == WST)
        and booking_date is not None
        and (datetime.strptime(booking_date, "%Y-%m-%d")) < datetime(2025, 4, 1)
    ):
        escrow_multiplier = 0.5
    elif client_id in [ANGLIA, WST]:
        escrow_multiplier = 0.7
    amount = calculate_escrow_amount(amount, escrow_multiplier)
    return (amount, escrow_multiplier, non_trust_flag)


def escrow_handler_bs(amount, payment_date=None, departure_date=None, escrow_multiplier=1, go_live_date=None):
    non_trust_flag = False
    if payment_date is not None and departure_date is not None:
        date_diff = calculate_date_difference_in_days(payment_date, departure_date)
        if date_diff < 1:
            escrow_multiplier = 0
            non_trust_flag = True

    if payment_date is not None:
        payment_date_obj = datetime.strptime(payment_date, "%Y-%m-%d")
        go_live_date_obj = datetime.strptime(go_live_date, "%Y-%m-%d")
        if payment_date_obj < go_live_date_obj:
            escrow_multiplier = 0
            non_trust_flag = True

    amount = calculate_escrow_amount(amount, escrow_multiplier)
    return (amount, escrow_multiplier, non_trust_flag)
