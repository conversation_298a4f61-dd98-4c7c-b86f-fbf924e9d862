from datetime import datetime, timed<PERSON><PERSON>


def get_next_date(date_str):
    if not date_str:
        return None
    """
    This function takes a date string in the format 'YYYY-MM-DD' 
    and returns the next day's date as a string in the same format.
    """
    new_date = datetime.strptime(date_str, "%Y-%m-%d")
    next_date = new_date + timedelta(days=1)
    return next_date.strftime("%Y-%m-%d")
