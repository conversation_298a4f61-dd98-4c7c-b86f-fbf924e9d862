"""
Helper functions for PowerBI data transformations.
Contains utilities for data cleaning, aggregation, and formatting.
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


def clean_banking_data(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    Clean and standardize banking data for PowerBI consumption.
    
    Args:
        data: Raw banking data from MongoDB
        
    Returns:
        Cleaned pandas DataFrame
    """
    if not data:
        return pd.DataFrame()
    
    df = pd.DataFrame(data)
    
    # Standardize column names
    column_mapping = {
        '_id': 'transaction_id',
        'client_id': 'client_id',
        'client_name': 'client_name',
        'booking_ref': 'booking_reference',
        'amount': 'transaction_amount',
        'currency_code': 'currency',
        'entry_type': 'entry_type',
        'payment_type': 'payment_type',
        'lead_pax': 'lead_passenger',
        'pax_count': 'passenger_count',
        'booking_date': 'booking_date',
        'departure_date': 'departure_date',
        'return_date': 'return_date',
        'total_booking_value': 'total_booking_value',
        'bonding': 'bonding_type',
        'booking_status': 'booking_status',
        'file_date': 'file_date',
        'created_at': 'created_timestamp',
        'updated_at': 'updated_timestamp',
        'balance': 'current_balance',
        'total_in_trust': 'total_in_trust',
        'total_claimed': 'total_claimed'
    }
    
    # Rename columns
    df = df.rename(columns=column_mapping)
    
    # Convert date columns
    date_columns = ['booking_date', 'departure_date', 'return_date', 'file_date', 
                   'created_timestamp', 'updated_timestamp']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')
    
    # Convert numeric columns
    numeric_columns = ['transaction_amount', 'passenger_count', 'total_booking_value',
                      'current_balance', 'total_in_trust', 'total_claimed']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Add calculated columns
    df['transaction_type'] = 'Banking'
    df['net_position'] = df.get('current_balance', 0) - df.get('total_claimed', 0)
    df['days_to_departure'] = (df['departure_date'] - df['created_timestamp']).dt.days
    df['booking_month'] = df['booking_date'].dt.to_period('M').astype(str)
    df['file_month'] = df['file_date'].dt.to_period('M').astype(str)
    
    # Clean text fields
    text_columns = ['client_name', 'booking_reference', 'currency', 'entry_type', 
                   'payment_type', 'lead_passenger', 'bonding_type', 'booking_status']
    for col in text_columns:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip()
    
    # Sort by created timestamp
    df = df.sort_values('created_timestamp', ascending=False)
    
    logger.info(f"Cleaned banking data: {len(df)} records")
    return df


def clean_claims_data(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    Clean and standardize claims data for PowerBI consumption.
    
    Args:
        data: Raw claims data from MongoDB
        
    Returns:
        Cleaned pandas DataFrame
    """
    if not data:
        return pd.DataFrame()
    
    df = pd.DataFrame(data)
    
    # Standardize column names (similar to banking but for claims)
    column_mapping = {
        '_id': 'transaction_id',
        'client_id': 'client_id',
        'client_name': 'client_name',
        'booking_ref': 'booking_reference',
        'amount': 'claim_amount',
        'currency_code': 'currency',
        'payment_type': 'claim_type',
        'lead_pax': 'lead_passenger',
        'pax_count': 'passenger_count',
        'booking_date': 'booking_date',
        'departure_date': 'departure_date',
        'return_date': 'return_date',
        'total_booking_value': 'total_booking_value',
        'bonding': 'bonding_type',
        'booking_status': 'booking_status',
        'file_date': 'file_date',
        'created_at': 'created_timestamp',
        'updated_at': 'updated_timestamp',
        'balance': 'current_balance',
        'total_in_trust': 'total_in_trust',
        'total_claimed': 'total_claimed'
    }
    
    # Rename columns
    df = df.rename(columns=column_mapping)
    
    # Convert date columns
    date_columns = ['booking_date', 'departure_date', 'return_date', 'file_date',
                   'created_timestamp', 'updated_timestamp']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')
    
    # Convert numeric columns
    numeric_columns = ['claim_amount', 'passenger_count', 'total_booking_value',
                      'current_balance', 'total_in_trust', 'total_claimed']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Add calculated columns
    df['transaction_type'] = 'Claims'
    df['claim_percentage'] = (df['claim_amount'] / df['total_booking_value'] * 100).round(2)
    df['days_since_departure'] = (df['created_timestamp'] - df['departure_date']).dt.days
    df['booking_month'] = df['booking_date'].dt.to_period('M').astype(str)
    df['file_month'] = df['file_date'].dt.to_period('M').astype(str)
    
    # Clean text fields
    text_columns = ['client_name', 'booking_reference', 'currency', 'claim_type',
                   'lead_passenger', 'bonding_type', 'booking_status']
    for col in text_columns:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip()
    
    # Sort by created timestamp
    df = df.sort_values('created_timestamp', ascending=False)
    
    logger.info(f"Cleaned claims data: {len(df)} records")
    return df


def create_summary_tables(banking_df: pd.DataFrame, claims_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """
    Create summary tables for PowerBI dashboards.
    
    Args:
        banking_df: Cleaned banking DataFrame
        claims_df: Cleaned claims DataFrame
        
    Returns:
        Dictionary of summary DataFrames
    """
    summaries = {}
    
    # Client summary
    if not banking_df.empty or not claims_df.empty:
        client_summary_data = []
        
        # Get unique clients from both datasets
        banking_clients = set(banking_df['client_name'].unique()) if not banking_df.empty else set()
        claims_clients = set(claims_df['client_name'].unique()) if not claims_df.empty else set()
        all_clients = banking_clients.union(claims_clients)
        
        for client in all_clients:
            client_banking = banking_df[banking_df['client_name'] == client] if not banking_df.empty else pd.DataFrame()
            client_claims = claims_df[claims_df['client_name'] == client] if not claims_df.empty else pd.DataFrame()
            
            summary_row = {
                'client_name': client,
                'total_banking_amount': client_banking['transaction_amount'].sum() if not client_banking.empty else 0,
                'total_claims_amount': client_claims['claim_amount'].sum() if not client_claims.empty else 0,
                'banking_transaction_count': len(client_banking),
                'claims_transaction_count': len(client_claims),
                'net_position': (client_banking['transaction_amount'].sum() if not client_banking.empty else 0) - 
                               (client_claims['claim_amount'].sum() if not client_claims.empty else 0),
                'latest_banking_date': client_banking['created_timestamp'].max() if not client_banking.empty else None,
                'latest_claims_date': client_claims['created_timestamp'].max() if not client_claims.empty else None
            }
            client_summary_data.append(summary_row)
        
        summaries['client_summary'] = pd.DataFrame(client_summary_data)
    
    # Currency summary
    if not banking_df.empty or not claims_df.empty:
        currency_summary_data = []
        
        # Get unique currencies
        banking_currencies = set(banking_df['currency'].unique()) if not banking_df.empty else set()
        claims_currencies = set(claims_df['currency'].unique()) if not claims_df.empty else set()
        all_currencies = banking_currencies.union(claims_currencies)
        
        for currency in all_currencies:
            currency_banking = banking_df[banking_df['currency'] == currency] if not banking_df.empty else pd.DataFrame()
            currency_claims = claims_df[claims_df['currency'] == currency] if not claims_df.empty else pd.DataFrame()
            
            summary_row = {
                'currency': currency,
                'total_banking_amount': currency_banking['transaction_amount'].sum() if not currency_banking.empty else 0,
                'total_claims_amount': currency_claims['claim_amount'].sum() if not currency_claims.empty else 0,
                'banking_transaction_count': len(currency_banking),
                'claims_transaction_count': len(currency_claims),
                'net_position': (currency_banking['transaction_amount'].sum() if not currency_banking.empty else 0) - 
                               (currency_claims['claim_amount'].sum() if not currency_claims.empty else 0)
            }
            currency_summary_data.append(summary_row)
        
        summaries['currency_summary'] = pd.DataFrame(currency_summary_data)
    
    # Monthly trend summary
    if not banking_df.empty or not claims_df.empty:
        monthly_data = []
        
        # Get all months from both datasets
        banking_months = set(banking_df['file_month'].unique()) if not banking_df.empty else set()
        claims_months = set(claims_df['file_month'].unique()) if not claims_df.empty else set()
        all_months = banking_months.union(claims_months)
        
        for month in sorted(all_months):
            month_banking = banking_df[banking_df['file_month'] == month] if not banking_df.empty else pd.DataFrame()
            month_claims = claims_df[claims_df['file_month'] == month] if not claims_df.empty else pd.DataFrame()
            
            summary_row = {
                'month': month,
                'banking_amount': month_banking['transaction_amount'].sum() if not month_banking.empty else 0,
                'claims_amount': month_claims['claim_amount'].sum() if not month_claims.empty else 0,
                'banking_count': len(month_banking),
                'claims_count': len(month_claims),
                'net_amount': (month_banking['transaction_amount'].sum() if not month_banking.empty else 0) - 
                             (month_claims['claim_amount'].sum() if not month_claims.empty else 0)
            }
            monthly_data.append(summary_row)
        
        summaries['monthly_summary'] = pd.DataFrame(monthly_data)
    
    logger.info(f"Created {len(summaries)} summary tables")
    return summaries


def add_powerbi_metadata(df: pd.DataFrame, data_type: str) -> pd.DataFrame:
    """
    Add metadata columns useful for PowerBI analysis.
    
    Args:
        df: DataFrame to enhance
        data_type: Type of data ('banking' or 'claims')
        
    Returns:
        Enhanced DataFrame
    """
    if df.empty:
        return df
    
    # Add refresh metadata
    df['data_refresh_timestamp'] = datetime.utcnow()
    df['data_source'] = 'MongoDB'
    df['data_type'] = data_type
    
    # Add time-based groupings for PowerBI
    if 'created_timestamp' in df.columns:
        df['created_year'] = df['created_timestamp'].dt.year
        df['created_quarter'] = df['created_timestamp'].dt.quarter
        df['created_month_name'] = df['created_timestamp'].dt.month_name()
        df['created_day_of_week'] = df['created_timestamp'].dt.day_name()
        df['created_week_of_year'] = df['created_timestamp'].dt.isocalendar().week
    
    if 'booking_date' in df.columns:
        df['booking_year'] = df['booking_date'].dt.year
        df['booking_quarter'] = df['booking_date'].dt.quarter
        df['booking_month_name'] = df['booking_date'].dt.month_name()
    
    # Add business logic flags
    if data_type == 'banking':
        df['is_large_transaction'] = df.get('transaction_amount', 0) > 10000
        df['is_recent_booking'] = df.get('days_to_departure', 999) <= 30
    elif data_type == 'claims':
        df['is_large_claim'] = df.get('claim_amount', 0) > 5000
        df['is_post_departure_claim'] = df.get('days_since_departure', -999) > 0
    
    return df


def validate_data_quality(df: pd.DataFrame, data_type: str) -> Dict[str, Any]:
    """
    Validate data quality and return metrics for monitoring.
    
    Args:
        df: DataFrame to validate
        data_type: Type of data being validated
        
    Returns:
        Dictionary with validation results
    """
    if df.empty:
        return {
            'record_count': 0,
            'validation_status': 'empty_dataset',
            'issues': ['No data available']
        }
    
    validation_results = {
        'record_count': len(df),
        'validation_status': 'passed',
        'issues': [],
        'warnings': []
    }
    
    # Check for required columns
    required_columns = ['client_name', 'booking_reference', 'currency']
    if data_type == 'banking':
        required_columns.append('transaction_amount')
    elif data_type == 'claims':
        required_columns.append('claim_amount')
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        validation_results['issues'].append(f"Missing required columns: {missing_columns}")
        validation_results['validation_status'] = 'failed'
    
    # Check for null values in critical columns
    for col in required_columns:
        if col in df.columns:
            null_count = df[col].isnull().sum()
            if null_count > 0:
                validation_results['warnings'].append(f"{col}: {null_count} null values")
    
    # Check for negative amounts
    amount_col = 'transaction_amount' if data_type == 'banking' else 'claim_amount'
    if amount_col in df.columns:
        negative_count = (df[amount_col] < 0).sum()
        if negative_count > 0:
            validation_results['warnings'].append(f"{negative_count} negative amounts found")
    
    # Check for future dates
    if 'created_timestamp' in df.columns:
        future_dates = (df['created_timestamp'] > datetime.utcnow()).sum()
        if future_dates > 0:
            validation_results['warnings'].append(f"{future_dates} future timestamps found")
    
    logger.info(f"Data validation for {data_type}: {validation_results['validation_status']}")
    return validation_results
