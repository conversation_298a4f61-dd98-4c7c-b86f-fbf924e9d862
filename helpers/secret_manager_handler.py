import os
import json
import boto3
from botocore.exceptions import ClientError


def get_secret(region_name, sftp=False, iglu=False):

    secret_name = os.environ["SECRET_NAME"]

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=region_name)

    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
    except ClientError as e:
        if e.response["Error"]["Code"] == "DecryptionFailureException":
            # Secrets Manager can't decrypt the protected secret text using the provided KMS key.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "InternalServiceErrorException":
            # An error occurred on the server side.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "InvalidParameterException":
            # You provided an invalid value for a parameter.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "InvalidRequestException":
            # You provided a parameter value that is not valid for the current state of the resource.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response["Error"]["Code"] == "ResourceNotFoundException":
            # We can't find the resource that you asked for.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
    else:
        # Decrypts secret using the associated KMS key.
        if "SecretString" in get_secret_value_response:
            secret = get_secret_value_response["SecretString"]
            if sftp is True:
                user_name = json.loads(secret).get("sftp-service-username")
                password = json.loads(secret).get("sftp-service-user-password")
                mongo_db = json.loads(secret).get("mongodb-uri")
                return user_name, password, mongo_db
            if iglu is True:
                user_name = json.loads(secret).get("iglu-escrow-username")
                password = json.loads(secret).get("iglu-escrow-user-password")
                mongo_db = json.loads(secret).get("mongodb-uri")
                return user_name, password, mongo_db
            else:
                return json.loads(secret).get("mongodb-uri")
