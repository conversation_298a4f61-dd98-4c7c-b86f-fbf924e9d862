import logging
import os
import boto3
import csv
import re
import json
from math import ceil
import xlsxwriter
from bson import ObjectId
from boto3.exceptions import S3UploadFailedError
from botocore.exceptions import ClientError
from datetime import datetime
from pymongo import MongoClient
from collections.abc import Generator
from helpers.secret_manager_handler import get_secret
import time
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize MongoDB & S3 clients
_mongo_uri = os.environ.get("DATABASE_URI") or get_secret("eu-west-2")
mongo_client = MongoClient(_mongo_uri)
db = mongo_client.get_database()


def change_date_format(dt):
    return re.sub(r"(\d{4})-(\d{1,2})-(\d{1,2})", "\\3/\\2/\\1", dt) if dt else None

def __transform_tbr_content(data, client: str):
    """
    Enhanced transform function that properly handles both generators and iterables
    Uses batching and processing time limits to avoid timeouts with 15-minute Lambda limit
    """
    start_time = time.time()
    logger.info(f"Starting __transform_tbr_content for client: {client}")
    result = []
    
    # Count records processed
    count = 0
    
    # Set timeout threshold (800 seconds to be safe with 900s/15-min Lambda timeout)
    timeout_threshold = 800  
    
    # Process in batches to reduce memory pressure
    batch_size = 1000
    current_batch = []
    
    # Check if we're close to timing out
    def is_approaching_timeout():
        elapsed = time.time() - start_time
        return elapsed > timeout_threshold
    
    try:
        # Convert cursor to iterator to avoid cursor timeout issues
        data_iter = iter(data)
        
        if client == os.environ.get("NAS"):
            while True:
                try:
                    # Check if we're approaching timeout before fetching next item
                    if is_approaching_timeout():
                        logger.warning(
                            f"Approaching timeout after processing {count} records. "
                            f"Returning partial results of {len(result) + len(current_batch)} items."
                        )
                        break
                        
                    # Get next record with timeout protection
                    try:
                        row = next(data_iter)
                    except StopIteration:
                        break
                    
                    count += 1
                    
                    # Process the row efficiently
                    currency_list = row.get("currency", [])
                    currency_value = ""
                    
                    # For NAS, take the first non-None currency if available
                    if isinstance(currency_list, list) and currency_list:
                        for cur in currency_list:
                            if cur is not None:
                                currency_value = cur
                                break
                    
                    # Create processed row with minimal operations
                    processed_row = {
                        **row,
                        "currency": currency_value,
                        "_id": str(row["_id"]) if row.get("_id") else None,
                        "clientId": str(row["clientId"]) if row.get("clientId") else None,
                    }
                    
                    current_batch.append(processed_row)
                    
                    # When we reach batch size, extend result and clear batch
                    if len(current_batch) >= batch_size:
                        result.extend(current_batch)
                        current_batch = []
                        
                        # Log progress
                        if count % 5000 == 0:
                            elapsed = time.time() - start_time
                            logger.info(f"Processed {count} records in {elapsed:.2f} seconds")
                    
                except Exception as row_error:
                    logger.error(f"Error processing row {count}: {str(row_error)}")
                    # Continue with next row instead of failing entire batch
        else:
            while True:
                try:
                    # Check if we're approaching timeout before fetching next item
                    if is_approaching_timeout():
                        logger.warning(
                            f"Approaching timeout after processing {count} records. "
                            f"Returning partial results of {len(result) + len(current_batch)} items."
                        )
                        break
                        
                    # Get next record with timeout protection
                    try:
                        row = next(data_iter)
                    except StopIteration:
                        break
                    
                    count += 1
                    
                    # Optimize currency processing
                    currency_value = ""
                    currency_data = row.get("currency", "")
                    
                    if isinstance(currency_data, list):
                        # Use list comprehension only once
                        currencies = [cur for cur in currency_data if cur is not None]
                        currency_value = ", ".join(currencies) if currencies else ""
                    else:
                        currency_value = currency_data
                    
                    # Create processed row efficiently
                    processed_row = {
                        **row,
                        "currency": currency_value,
                        "_id": str(row["_id"]) if row.get("_id") else None,
                        "clientId": str(row["clientId"]) if row.get("clientId") else None,
                    }
                    
                    current_batch.append(processed_row)
                    
                    # When we reach batch size, extend result and clear batch
                    if len(current_batch) >= batch_size:
                        result.extend(current_batch)
                        current_batch = []
                        
                        # Log progress at less frequent intervals
                        if count % 5000 == 0:
                            elapsed = time.time() - start_time
                            logger.info(f"Processed {count} records in {elapsed:.2f} seconds")
                    
                except Exception as row_error:
                    logger.error(f"Error processing row {count}: {str(row_error)}")
                    # Continue with next row instead of failing entire batch
        
        # Don't forget to add the final batch
        if current_batch:
            result.extend(current_batch)
            
    except Exception as e:
        logger.error(f"Error in __transform_tbr_content: {str(e)}")
        # Return whatever we've processed so far rather than nothing
    
    elapsed = time.time() - start_time
    logger.info(f"Completed __transform_tbr_content in {elapsed:.2f}s, processed {count} records into {len(result)} results")
    
    # Return what we have, even if incomplete
    return result

def __transform_tbr_content_nas(trust_funds: Generator) -> Generator:
    """
    Transform trust funds by consolidating at booking reference level
    """
    logger.info("Starting __transform_tbr_content_nas consolidation")
    count = 0
    processed_booking_refs = {}
    
    for trust_fund in trust_funds:
        count += 1
        booking_ref = trust_fund.get("bookingRef")
        currency = trust_fund.get("currency")
        
        # Create a unique key for this booking + currency
        key = f"{booking_ref}_{currency}"
        
        # Skip if already processed
        if key in processed_booking_refs:
            continue
        
        processed_booking_refs[key] = True
        
        try:
            # Create a consolidated record
            consolidated = {
                "_id": trust_fund.get("_id"),
                "clientId": trust_fund.get("clientId"),
                "cId": trust_fund.get("cId"),
                "clientName": trust_fund.get("clientName"),
                "friendlyName": trust_fund.get("friendlyName"),
                "bookingRef": booking_ref,
                "leadPax": trust_fund.get("leadPax"),
                "departureDate": trust_fund.get("departureDate"),
                "returnDate": trust_fund.get("returnDate"),
                "bookingDate": trust_fund.get("bookingDate"),
                "currency": currency,
                "deposits": trust_fund.get("deposits", 0.0),
                "refundsFromDepositFile": trust_fund.get("refundsFromDepositFile", 0.0),
                "totalClaimed": trust_fund.get("totalClaimed", 0.0),
                "totalBanked": trust_fund.get("deposits", 0.0) - trust_fund.get("refundsFromDepositFile", 0.0),
                "balance": (trust_fund.get("deposits", 0.0) - trust_fund.get("refundsFromDepositFile", 0.0)) - trust_fund.get("totalClaimed", 0.0),
                "status": trust_fund.get("bookingStatus", "Active"),
                "totalBookingValue": trust_fund.get("totalBookingValue", 0.0),
            }
            
            # Set type based on whether this is primarily a purchase or refund
            types = trust_fund.get("types", [])
            if "Purchase" in types:
                consolidated["type"] = "Purchase"
            elif "Refund (Credit)" in types:
                consolidated["type"] = "Refund (Credit)"
            elif types and len(types) > 0:
                consolidated["type"] = types[0]
            else:
                consolidated["type"] = "Purchase" if consolidated["deposits"] > consolidated["refundsFromDepositFile"] else "Refund (Credit)"
            
            # Set supplier name if available
            supplier_names = trust_fund.get("supplierNames", [])
            if supplier_names and len(supplier_names) > 0:
                consolidated["supplierName"] = next((s for s in supplier_names if s is not None), None)
            else:
                consolidated["supplierName"] = None
            
            # Only include if balance is significant
            if abs(consolidated["balance"]) >= 0.005:
                yield consolidated
                
        except Exception as e:
            logger.error(f"Error processing booking {booking_ref}: {str(e)}", exc_info=True)
    
    logger.info(f"Completed processing {count} records, yielded {len(processed_booking_refs)} consolidated records")

def generate_csv_and_excel_report(header_dict, data_list, csv_name, xlsx_name, csv_path, xlsx_path):
    logger.info(f"CSV File Path: {csv_name}")
    logger.info(f"XLSX File Path: {xlsx_name}")
    logger.info(f"Data list length: {len(data_list)}")

    amount_list = [
        "amounts",
        "amount",
        "minAmount",
        "maxAmount",
        "total",
        "deposits",
        "totalBanked",
        "totalClaimed",
        "balance",
        "validClaimAmount",
        "voidClaimAmount",
        "claimFileTotal",
        "bankingFileTotal",
        "value",
        "refundsFromDepositFile",
        "totalAmount",
        "totalAgentAmountInTrust",
        "totalInTrust",
        "totalBookingValue",
        "totalClaimAmount",
        "deposits",
        "revisedClaim",
    ]
    csv_file = open(f"{csv_path}/{csv_name}", "w", newline="")

    writer = csv.writer(csv_file, delimiter=",")
    writer.writerow(header_dict.values())

    workbook = xlsxwriter.Workbook(f"{xlsx_path}/{xlsx_name}")
    worksheet = workbook.add_worksheet()
    merge_left = workbook.add_format({"size": 11, "align": "left"})
    merge_left_head = workbook.add_format({"bold": 1, "size": 11, "align": "left"})

    amount_format = workbook.add_format({"num_format": "#,##0.00", "align": "right"})

    row = 0
    col = 0
    head_dict = {}
    logger.info("Writing headers to Excel and setting column formats...")
    for key, value in header_dict.items():
        worksheet.write(row, col, value, merge_left_head)
        if key in amount_list:
            worksheet.set_column(col, col, 20, amount_format)
        else:
            worksheet.set_column(col, col, 20, merge_left)

        head_dict[key] = col
        col += 1
    row = 1

    logger.info("Processing records")
    for i, data in enumerate(data_list):
        try:
            value_list = []
            for key in header_dict.keys():
                try:
                    value = data.get(key)
                    # Convert unsupported types to string
                    if isinstance(value, list):
                        value = ", ".join(map(str, value))
                    elif isinstance(value, dict):
                        value = json.dumps(value)
                    worksheet.write(row, head_dict[key], value)
                    if key in amount_list and value:
                        value = round(value, 2)
                    value_list.append(value)
                except Exception as inner_e:
                    logger.error(f"Error processing key '{key}' in record {i}: {inner_e}")
            row += 1
            writer.writerow(value_list)
        except Exception as outer_e:
            logger.error(f"Error processing record {i}: {outer_e}")

    logger.info("Closing workbook and CSV file")
    csv_file.close()
    workbook.close()

def upload_file(bucket, key, file_path):
    s3_client = boto3.client("s3")

    try:
        response = s3_client.upload_file(file_path, bucket, key)
    except (S3UploadFailedError, ClientError) as e:
        logging.error(e)
        raise ValueError("Failed to upload")
    return response

def transform_dates(client: str, data: Generator[dict, None, None]) -> Generator[dict, None, None]:
    """
    For NAS/FLYPOP clients, format dates with full timestamp in format %d-%m-%Y %H:%M:%S.%f
    For others, use change_date_format which gives date in DD/MM/YYYY format
    """
    nas_clients = {
        os.environ.get("NAS"),
        os.environ.get("FLYPOP")
    }

    for row in data:
        bd = row.get("bookingDate")
        dd = row.get("departureDate")
        rd = row.get("returnDate")

        if client in nas_clients:
            # Format for NAS clients as in Celery: %d-%m-%Y %H:%M:%S.%f
            bookingDate = datetime.strptime(bd, "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3] if bd else None
            departureDate = datetime.strptime(dd, "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3] if dd else None
            returnDate = datetime.strptime(rd, "%Y-%m-%d").strftime("%d-%m-%Y %H:%M:%S.%f")[:-3] if rd else None
        else:
            # For non-NAS clients, use DD/MM/YYYY format
            bookingDate = change_date_format(bd)
            departureDate = change_date_format(dd)
            returnDate = change_date_format(rd)

        yield {
            **row,
            "bookingDate": bookingDate,
            "departureDate": departureDate,
            "returnDate": returnDate,
        }

def trust_balance_report_gtl(data):
    """
    Enhanced implementation of trust balance report for GTL clients
    Includes additional logging and performance optimizations
    """
    # Start timing for performance analysis
    start_time = datetime.utcnow()
    
    currency_projection_criteria = {"currency": "$currency_code"}
    client = data.get("client")
    currency = data.get("currency") or ""
    page = int(data.get("page") or 1)
    size = int(data.get("size") or 10)  # Default will be overridden by sys.maxsize from lambda_handler
    offset = (page - 1) * size
    collection_name = "trust_fund_v2"
    
    logger.info(f"Starting GTL report generation for client: {client}, currency: {currency}, size: {size}")
    
    # Special booking references that need inclusion regardless of other criteria
    special_booking_refs = ["A420005", "A459418", "A470309"]
    logger.info(f"Special booking references with override: {special_booking_refs}")
    
    # Set up match conditions
    client_and_currency_match_condition = [{"$match": {"$and": []}}] if client or currency else []
    client_and_currency_match_condition[0]["$match"]["$and"].append(
        {"payment_type": {"$nin": ["Protected Deposit - Applied", "Gift Voucher", ""]}}
    )

    # Add lookup for restricted bank records
    client_and_currency_match_condition.append(
        {
            "$lookup": {
                "from": "banking_file_details",
                "let": {"current_client_id": "$client_id", "current_booking_ref": "$booking_ref"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$client_id", "$$current_client_id"]},
                                    {"$eq": ["$booking_ref", "$$current_booking_ref"]},
                                    {"$eq": ["$deleted", False]},
                                    {"$in": ["$payment_type", ["Protected Deposit - Applied", "Gift Voucher", ""]]},
                                ]
                            }
                        }
                    },
                    {"$limit": 1},
                ],
                "as": "restricted_bank_records",
            }
        }
    )

    # Exclude restricted records unless they're in the special refs list
    client_and_currency_match_condition.append({
        "$match": {
            "$or": [
                {"restricted_bank_records": {"$eq": []}},
                {"booking_ref": {"$in": special_booking_refs}}
            ]
        }
    })

    # Add client-specific criteria
    if client:
        client_and_currency_match_condition[0]["$match"]["$and"].append({
            "$or": [
                {"return_date": {"$gte": "2020-01-01"}},
                {"booking_ref": {"$in": special_booking_refs}}
            ]
        })
        client_and_currency_match_condition[0]["$match"]["$and"].append({"client_id": ObjectId(client)})
        logger.info(f"Added client filter for ObjectId: {client}")

    # Add currency filter if specified
    if currency:
        client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
        logger.info(f"Added currency filter for: {currency}")
    
    # Log the match conditions for debugging
    logger.info(f"Match conditions: {json.dumps(str(client_and_currency_match_condition), default=str)}")
    
    # Main pipeline
    pipeline = [
        *client_and_currency_match_condition,
        {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
        {"$sort": {"balance": 1}},
        {
            "$lookup": {
                "from": "client_basic_info",
                "localField": "client_id",
                "foreignField": "_id",
                "as": "client",
            }
        },
        {"$unwind": "$client"},
        {
            "$project": {
                "_id": "$_id",
                "clientId": "$client._id",
                "cId": "$client.c_id",
                "clientName": "$client.full_name",
                "friendlyName": "$client.friendly_name",
                "bookingRef": "$booking_ref",
                "leadPax": "$lead_pax",
                "departureDate": "$departure_date",
                "returnDate": "$return_date",
                "bookingDate": "$booking_date",
                **currency_projection_criteria,
                "totalBanked": {"$ifNull": ["$total_in_trust", 0.0]},
                "totalClaimed": {"$ifNull": ["$total_claimed", 0.0]},
                "balance": {"$ifNull": ["$balance", 0.0]},
                "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                "type": {"$arrayElemAt": ["$bank.type", 0]},
                "status": {"$ifNull": ["$booking_status", "Active"]},
                "total": "$totalCount",
            }
        },
    ]
    
    # First, get total count without pagination
    count_pipeline = [
        *client_and_currency_match_condition,
        {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
        {"$count": "count"},
    ]
    
    # Log that we're about to run the count query
    logger.info("Executing count query for GTL report")
    count_start = datetime.utcnow()
    
    total_elements_cursor = db[collection_name].aggregate(count_pipeline)
    total_elements = next(total_elements_cursor, {"count": 0})["count"]
    
    count_duration = (datetime.utcnow() - count_start).total_seconds()
    logger.info(f"Count query completed in {count_duration}s. Total records for GTL report: {total_elements}")
    
    # Add pagination if needed (though with sys.maxsize, this likely won't limit anything)
    paginated_pipeline = [*pipeline, {"$skip": offset}, {"$limit": size}]
    
    # Execute the main query
    logger.info(f"Executing main GTL report query with skip={offset}, limit={size}")
    query_start = datetime.utcnow()
    
    data_cursor = db[collection_name].aggregate(
        paginated_pipeline,
        collation={"locale": "en", "strength": 1},
        allowDiskUse=True,
    )
    
    # Transform the data
    logger.info("Transforming GTL report data")
    transform_start = datetime.utcnow()
    
    content = __transform_tbr_content(data_cursor, client)
    
    # If content is a generator, materialize it for count checks
    if isinstance(content, Generator):
        logger.info("Converting generator to list")
        content = list(content)
    
    transform_duration = (datetime.utcnow() - transform_start).total_seconds()
    logger.info(f"Data transformation completed in {transform_duration}s. Records processed: {len(content)}")
    
    # Look for special booking references in the results
    special_refs_found = [item["bookingRef"] for item in content if item["bookingRef"] in special_booking_refs]
    if special_refs_found:
        logger.info(f"Found special booking references in results: {special_refs_found}")
    
    # Calculate pagination metadata
    empty = len(content) == 0
    first = page == 1
    total_pages = ceil(total_elements / size) if size > 0 else 0
    last = page >= total_pages
    
    total_duration = (datetime.utcnow() - start_time).total_seconds()
    logger.info(f"GTL Report generated with {len(content)} records in {total_duration}s")
    
    response = {
        "content": content,
        "empty": empty,
        "first": first,
        "last": last,
        "pageNumber": page,
        "totalElements": total_elements,
        "totalPages": total_pages,
    }
    
    return response

def trust_balance_report(data):
    """
    Implementation of standard trust balance report
    Updated to match Celery implementation and handle pagination properly
    """
    currency_projection_criteria = {"currency": "$currency_code"}
    client = data.get("client")
    currency = data.get("currency") or ""
    page = int(data.get("page") or 1)
    size = int(data.get("size") or 10)  # Default will be overridden by sys.maxsize from lambda_handler
    offset = (page - 1) * size
    
    # Special handling for different clients
    client_list = [
        os.environ.get("BARRHEAD"),
        os.environ.get("HAYS"),
        os.environ.get("SUNSHINE"),
        os.environ.get("BROADWAY"),
    ]
    collection_name = "trust_fund" if client == os.environ.get("NAS") else "trust_fund_v2"
    
    # Set up match conditions
    client_and_currency_match_condition = [{"$match": {"$and": []}}] if client or currency else []
    
    if client:
        if client == os.environ.get("NAS"):
            currency_projection_criteria = {
                "currency": [{"$arrayElemAt": [{"$arrayElemAt": ["$bank.currency_code", -1]}, -1]}]
            }
        client_and_currency_match_condition[0]["$match"]["$and"].append({"client_id": ObjectId(client)})
        
        # Special conditions for specific clients
        if client in client_list:
            if client == os.environ.get("BARRHEAD"):
                client_and_currency_match_condition[0]["$match"]["$and"].append(
                    {
                        "$or": [
                            {"return_date": {"$gte": "2020-01-01"}},
                            {
                                "return_date": {
                                    "$gte": "1900-01-01",
                                    "$lte": "1900-12-31",
                                }
                            },
                        ],
                        "booking_ref": {"$nin": ["********", "********", "********"]},
                    }
                )
            else:
                client_and_currency_match_condition[0]["$match"]["$and"].append(
                    {"return_date": {"$gte": "2020-01-01"}}
                )

    if currency:
        client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
    
    # Main pipeline
    pipeline = [
        *client_and_currency_match_condition,
        {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
        {"$sort": {"balance": 1}},
        {
            "$lookup": {
                "from": "banking_file_details",
                "localField": "booking_ref",
                "foreignField": "booking_ref",
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$client_id", "$$current_client_id"]},
                                    {"$eq": ["$deleted", False]},
                                ]
                            }
                        }
                    },
                    {"$sort": {"updated_at": 1}},
                    {
                        "$group": {
                            "_id": None,
                            "type": {"$last": "$type"},
                            "refund": {"$sum": {"$cond": [{"$lt": ["$amount", 0]}, "$amount", 0]}},
                            "currency_code": {"$push": "$currency_code"},
                        }
                    },
                ],
                "as": "bank",
            }
        },
        {
            "$lookup": {
                "from": "client_basic_info",
                "localField": "client_id",
                "foreignField": "_id",
                "as": "client",
            }
        },
        {"$unwind": "$client"},
        {
            "$project": {
                "_id": "$_id",
                "clientId": "$client._id",
                "cId": "$client.c_id",
                "clientName": "$client.full_name",
                "friendlyName": "$client.friendly_name",
                "bookingRef": "$booking_ref",
                "leadPax": "$lead_pax",
                "departureDate": "$departure_date",
                "returnDate": "$return_date",
                "bookingDate": "$booking_date",
                **currency_projection_criteria,
                "totalBanked": {"$ifNull": ["$total_in_trust", 0.0]},
                "totalClaimed": {"$ifNull": ["$total_claimed", 0.0]},
                "balance": {"$ifNull": ["$balance", 0.0]},
                "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                "refundsFromDepositFile": {"$ifNull": [{"$abs": {"$arrayElemAt": ["$bank.refund", 0]}}, 0]},
                "deposits": {
                    "$add": [
                        {"$ifNull": [{"$abs": {"$arrayElemAt": ["$bank.refund", 0]}}, 0]},
                        {"$ifNull": ["$total_in_trust", 0]},
                    ]
                },
                "type": {"$arrayElemAt": ["$bank.type", 0]},
                "status": {"$ifNull": ["$booking_status", "Active"]},
                "total": "$totalCount",
            }
        },
    ]
    
    # First, get total count without pagination
    count_pipeline = [
        *client_and_currency_match_condition,
        {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
        {"$count": "count"},
    ]
    
    total_elements_cursor = db[collection_name].aggregate(count_pipeline)
    total_elements = next(total_elements_cursor, {"count": 0})["count"]
    
    # Log the total count
    logger.info(f"Total records for standard report: {total_elements}")
    
    # Add pagination if needed (though with sys.maxsize, this likely won't limit anything)
    paginated_pipeline = [*pipeline, {"$skip": offset}, {"$limit": size}]
    
    # Execute the query
    data_cursor = db[collection_name].aggregate(
        paginated_pipeline,
        collation={"locale": "en", "strength": 1},
        allowDiskUse=True,
    )
    
    # Transform the data
    content = __transform_tbr_content(data_cursor, client)
    
    # If content is a generator, materialize it for count checks
    if isinstance(content, Generator):
        content = list(content)
    
    # Calculate pagination metadata
    empty = len(content) == 0
    first = page == 1
    total_pages = ceil(total_elements / size) if size > 0 else 0
    last = page >= total_pages
    
    logger.info(f"Standard report generated with {len(content)} records")
    
    response = {
        "content": content,
        "empty": empty,
        "first": first,
        "last": last,
        "pageNumber": page,
        "totalElements": total_elements,
        "totalPages": total_pages,
    }
    
    return response

def trust_balance_report_nas(data):
    """
    Implementation of trust balance report for NAS clients
    Uses the same logic as the Celery task to ensure consistent results
    """
    client_id = data.get("client")
    if not client_id:
        raise ValueError("Client Id is missing")
    
    currency = data.get("currency") or ""
    collection_name = "trust_fund" if client_id == os.environ.get("NAS") else "trust_fund_v2"
    
    # Set up matching conditions
    client_and_currency_match_condition = [{"$match": {"$and": [{"client_id": ObjectId(client_id)}]}}]
    if currency:
        client_and_currency_match_condition[0]["$match"]["$and"].append({"currency_code": {"$all": [currency]}})
    
    logger.info(f"Starting trust_balance_report_nas for client: {client_id}")
    
    # Use the same aggregation pipeline as in Celery task
    trust_funds = db[collection_name].aggregate(
        [
            *client_and_currency_match_condition,
            {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
            {
                "$lookup": {
                    "from": "banking_file_details",
                    "let": {"current_client_id": "$client_id", "current_booking_ref": "$booking_ref"},
                    "localField": "booking_ref",
                    "foreignField": "booking_ref",
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$client_id", "$$current_client_id"]},
                                        {"$eq": ["$booking_ref", "$$current_booking_ref"]},
                                        {"$eq": ["$deleted", False]},
                                    ]
                                }
                            }
                        },
                        {"$sort": {"created_at": 1}},
                        {
                            "$project": {
                                "_id": 1,
                                "amount": 1,
                                "type": {"$ifNull": ["$type", None]},
                                "supplierNames": {"$ifNull": ["$supplier_names", None]},
                            }
                        },
                    ],
                    "as": "bank",
                }
            },
            {
                "$lookup": {
                    "from": "claims_file_details",
                    "let": {"current_client_id": "$client_id", "current_booking_ref": "$booking_ref"},
                    "localField": "booking_ref",
                    "foreignField": "booking_ref",
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$client_id", "$$current_client_id"]},
                                        {"$eq": ["$booking_ref", "$$current_booking_ref"]},
                                        {"$eq": ["$deleted", False]},
                                    ]
                                }
                            }
                        },
                        {
                            "$project": {
                                "amount": 1,
                            }
                        },
                    ],
                    "as": "claim",
                }
            },
            # Add the $unionWith operation from Celery implementation
            {
                "$unionWith": {
                    "coll": "banking_metadata",
                    "pipeline": [
                        {
                            "$match": {
                                "client_id": ObjectId(client_id),
                                "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                            },
                        },
                        {"$addFields": {"recent_file": {"$arrayElemAt": ["$banking_files", -1]}}},
                        {"$sort": {"recent_file.submitted_date": -1}},
                        {"$limit": 1},
                        {
                            "$project": {
                                "_id": 0,
                                "file_id": "$recent_file.file_id",
                                "submitted_date": "$recent_file.submitted_date",
                            }
                        },
                        {
                            "$lookup": {
                                "from": "claims_metadata",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "client_id": ObjectId(client_id),
                                            "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
                                        },
                                    },
                                    {"$addFields": {"recent_file": {"$arrayElemAt": ["$claim_files", -1]}}},
                                    {"$sort": {"recent_file.submitted_date": -1}},
                                    {"$limit": 1},
                                    {
                                        "$project": {
                                            "_id": 0,
                                            "file_id": "$recent_file.file_id",
                                            "submitted_date": "$recent_file.submitted_date",
                                        }
                                    },
                                ],
                                "as": "claim",
                            }
                        },
                        {"$unwind": "$claim"},
                        {"$match": {"$expr": {"$lt": ["$claim.submitted_date", "$submitted_date"]}}},
                        {
                            "$lookup": {
                                "from": "banking_file_details",
                                "localField": "file_id",
                                "foreignField": "file_id",
                                "pipeline": [
                                    {"$match": {"deleted": False}},
                                    {
                                        "$group": {
                                            "_id": "$booking_ref",
                                            "client_id": {"$first": "$client_id"},
                                        }
                                    },
                                    {"$project": {"client_id": 1, "booking_ref": "$_id"}},
                                ],
                                "as": "bank",
                            }
                        },
                        {"$unwind": "$bank"},
                        {"$project": {"booking_ref": "$bank.booking_ref"}},
                        {
                            "$lookup": {
                                "from": "trust_fund_v2",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    *client_and_currency_match_condition,
                                    {
                                        "$match": {
                                            "$and": [{"balance": {"$lt": 0.005}}, {"balance": {"$gt": -0.005}}]
                                        }
                                    },
                                ],
                                "as": "trust_fund",
                            }
                        },
                        {"$unwind": "$trust_fund"},
                        {
                            "$lookup": {
                                "from": "banking_file_details",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "client_id": ObjectId(client_id),
                                            "deleted": False,
                                        }
                                    },
                                    {"$sort": {"created_at": 1}},
                                    {
                                        "$project": {
                                            "amount": 1,
                                            "type": {"$ifNull": ["$type", None]},
                                            "supplierNames": {"$ifNull": ["$supplier_names", None]},
                                        }
                                    },
                                ],
                                "as": "bank",
                            }
                        },
                        {
                            "$lookup": {
                                "from": "claims_file_details",
                                "localField": "booking_ref",
                                "foreignField": "booking_ref",
                                "pipeline": [
                                    {
                                        "$match": {
                                            "client_id": ObjectId(client_id),
                                            "deleted": False,
                                        }
                                    },
                                    {"$sort": {"created_at": 1}},
                                ],
                                "as": "claim",
                            }
                        },
                        {
                            "$project": {
                                "booking_ref": "$trust_fund.booking_ref",
                                "pax_count": {"$toInt": {"$ifNull": ["$trust_fund.pax_count", 0]}},
                                "lead_pax": "$trust_fund.lead_pax",
                                "total_booking_value": {"$ifNull": ["$trust_fund.total_booking_value", 0.0]},
                                "bonding": "$trust_fund.bonding",
                                "booking_date": "$trust_fund.booking_date",
                                "departure_date": "$trust_fund.departure_date",
                                "return_date": "$trust_fund.return_date",
                                "booking_status": "$trust_fund.booking_status",
                                "currency_code": "$trust_fund.currency_code",
                                "client_id": "$trust_fund.client_id",
                                "bank": 1,
                                "claim": 1,
                            }
                        },
                    ],
                }
            },
            {
                "$lookup": {
                    "from": "client_basic_info",
                    "localField": "client_id",
                    "foreignField": "_id",
                    "as": "client",
                }
            },
            {"$unwind": "$client"},
            {
                "$project": {
                    "_id": "$_id",
                    "clientId": "$client._id",
                    "cId": "$client.c_id",
                    "clientName": "$client.full_name",
                    "friendlyName": "$client.friendly_name",
                    "bookingRef": "$booking_ref",
                    "leadPax": "$lead_pax",
                    "departureDate": "$departure_date",
                    "returnDate": "$return_date",
                    "bookingDate": "$booking_date",
                    "banking_amount": "$bank.amount",
                    "supplierNames": "$bank.supplierNames",
                    "claim_amount": "$claim.amount",
                    "bookingStatus": "$booking_status",
                    "balance": {"$ifNull": ["$balance", 0.0]},
                    "currency": {"$last": "$currency_code"},
                    "types": "$bank.type",
                    "totalBookingValue": {"$ifNull": ["$total_booking_value", 0.0]},
                }
            },
        ],
        collation={"locale": "en", "strength": 1},
        allowDiskUse=True,
    )
    
    # Process the data using same approach as in Celery
    processed_data = []
    seen_bookings = {}
    
    logger.info("Processing trust funds data to calculate fields")
    for trust_fund in trust_funds:
        booking_ref = trust_fund.get("bookingRef")
        currency = trust_fund.get("currency")
        
        try:
            # Calculate deposits and refunds
            banking_amounts = trust_fund.get("banking_amount", [])
            deposits = 0
            refunds = 0
            
            for amount in banking_amounts:
                if amount > 0:
                    deposits += amount
                else:
                    refunds += abs(amount)
            
            # Calculate claims
            claim_amounts = trust_fund.get("claim_amount", [])
            total_claimed = sum(claim_amounts) if claim_amounts else 0
            
            # Determine type (preserve the original if available)
            types = trust_fund.get("types", [])
            type_value = None
            if types:
                if "Purchase" in types:
                    type_value = "Purchase"
                elif "Refund (Credit)" in types:
                    type_value = "Refund (Credit)"
                elif types and len(types) > 0:
                    type_value = types[0]
                else:
                    type_value = "Purchase" if deposits > refunds else "Refund (Credit)"
            else:
                type_value = "Purchase" if deposits > refunds else "Refund (Credit)"
            
            # Create final record with all required fields
            record = {
                "_id": trust_fund.get("_id"),
                "clientId": trust_fund.get("clientId"),
                "cId": trust_fund.get("cId"),
                "clientName": trust_fund.get("clientName"),
                "friendlyName": trust_fund.get("friendlyName"),
                "bookingRef": booking_ref,
                "leadPax": trust_fund.get("leadPax"),
                "departureDate": trust_fund.get("departureDate"),
                "returnDate": trust_fund.get("returnDate"),
                "bookingDate": trust_fund.get("bookingDate"),
                "currency": currency,
                "deposits": deposits,
                "refundsFromDepositFile": refunds,
                "totalBanked": deposits - refunds,
                "totalClaimed": total_claimed,
                "balance": (deposits - refunds) - total_claimed,
                "status": trust_fund.get("bookingStatus", "Active"),
                "type": type_value,
                "totalBookingValue": trust_fund.get("totalBookingValue", 0.0),
                "supplierName": next((s for s in trust_fund.get("supplierNames", []) if s is not None), None)
            }
            
            # Only add if balance is significant
            if abs(record["balance"]) >= 0.005:
                # Check if we've seen this booking reference before
                key = f"{booking_ref}_{currency}"
                if key not in seen_bookings:
                    seen_bookings[key] = True
                    processed_data.append(record)
                    
        except Exception as e:
            logger.error(f"Error processing booking {booking_ref}: {str(e)}", exc_info=True)
    
    logger.info(f"Generated {len(processed_data)} records for NAS trust balance report")
    
    return {"content": processed_data}  