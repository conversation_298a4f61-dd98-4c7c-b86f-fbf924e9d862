from collections import OrderedDict
import os
import datetime
import boto3
import pymongo
from pymongo import MongoClient
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
from helpers import round, generate_excel_report
import logging
import requests
import tempfile

url = os.environ["URL"]
TEMP_DIR = tempfile.gettempdir()
details = get_secret("eu-west-2", False, True)
user_name, password, mongo_db = details
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()
if logging.getLogger().hasHandlers():
    logging.getLogger().setLevel(logging.INFO)
else:
    logging.basicConfig(level=logging.INFO)

client_id = os.environ.get("IGLU_ESCROW")


def lambda_handler(event, context):
    today = datetime.date.today()
    basic_info = db.client_basic_info.find_one({"_id": ObjectId(client_id)})
    current_escrow_multiplier = basic_info.get("escrow_multiplier")
    next_escrow_multiplier = current_escrow_multiplier

    latest_tbr = db.report_files.find_one(
        {"client_id": ObjectId(client_id)}, sort=[("generated_at", pymongo.DESCENDING)]
    )
    tbr_file_id = latest_tbr.get("file_id")
    s3 = boto3.client("s3")
    logging.info("started downloading tbr file from s3 and saving to temp directory")
    s3.download_file(
        os.environ["REPORTS_BUCKET"], tbr_file_id, f"{TEMP_DIR}/{today.strftime('%Y%m%d')}-TBR-IGLU-ESCROW"
    )
    logging.info("file downloaded and saved")

    logging.info("started uploading tbr file to s3")
    file_key = f"{today.strftime('%Y%m%d')}-TBR-IGLU-ESCROW.xlsx"
    s3.upload_file(f"{TEMP_DIR}/{today.strftime('%Y%m%d')}-TBR-IGLU-ESCROW", os.environ["IGLU_TBR_BUCKET"], file_key)
    logging.info("file successfully uploaded")

    current_escrow_multiplier_date = f'{today.strftime("%Y-%m")}-01'
    current_client_escrow_multiplier = db.client_escrow_multiplier.find_one(
        {"client_id": ObjectId(client_id), "date": current_escrow_multiplier_date}
    )
    if current_client_escrow_multiplier and current_client_escrow_multiplier.get("multiplier"):
        current_escrow_multiplier = current_client_escrow_multiplier.get("multiplier")

    next_day = today + datetime.timedelta(days=1)
    next_escrow_multiplier_date = f'{next_day.strftime("%Y-%m")}-01'
    next_client_escrow_multiplier = db.client_escrow_multiplier.find_one(
        {"client_id": ObjectId(client_id), "date": next_escrow_multiplier_date}
    )
    if next_client_escrow_multiplier and next_client_escrow_multiplier.get("multiplier"):
        next_escrow_multiplier = next_client_escrow_multiplier.get("multiplier")

    if current_escrow_multiplier != next_escrow_multiplier:
        multiplier_difference = round(abs(current_escrow_multiplier - next_escrow_multiplier), 2)
        db.client_escrow_multiplier.find_one_and_update(
            {"client_id": ObjectId(client_id), "date": next_escrow_multiplier_date},
            {"$set": {"multiplier_difference": multiplier_difference}},
        )

    trust_funds = list(
        db.trust_fund_v2.aggregate(
            [
                {"$match": {"client_id": ObjectId(client_id)}},
                {"$match": {"$or": [{"balance": {"$gte": 0.005}}, {"balance": {"$lte": -0.005}}]}},
                {
                    "$project": {
                        "_id": 0,
                        "booking_ref": 1,
                        "balance": 1,
                        "pax_count": 1,
                        "lead_pax": 1,
                        "total_booking_value": {"$ifNull": ["$total_booking_value", 0.0]},
                        "bonding": 1,
                        "booking_date": 1,
                        "departure_date": 1,
                        "return_date": 1,
                        "booking_status": {"$ifNull": ["$booking_status", "Active"]},
                        "currency": {"$last": "$currency_code"},
                        "element": "Non-trust",
                        "dept_bal_lead_time": {"$toInt": {"$ifNull": ["$dept_bal_lead_time", 0]}},
                    }
                },
            ],
        )
    )

    data_list = []
    for trust_fund in trust_funds:
        funds_out_from_trust = 0
        funds_in_to_trust = 0
        if trust_fund.get("pax_count") == 0:
            trust_fund["pax_count"] = None
        escrow_balance = trust_fund["balance"]
        original_balance = escrow_balance / current_escrow_multiplier
        new_balance = original_balance * next_escrow_multiplier
        if next_escrow_multiplier < current_escrow_multiplier:
            funds_out_from_trust = escrow_balance - new_balance
            if round(funds_out_from_trust) != 0:
                trust_fund["funds_difference"] = original_balance
                file_name = f"{next_day.strftime('%Y%m%d')}-I.CL-Claim-generated.xlsx"
                upload_presigned_url = f"{url}/claim/upload/presigned-url"
                params = {"clientId": client_id, "fileName": file_name, "claimFromTBR": False}
                upload_url = f"{url}/claim/upload"
                payload = {"clientId": client_id, "fileName": file_name, "claimFromTBR": False}
            else:
                continue
        if next_escrow_multiplier > current_escrow_multiplier:
            funds_in_to_trust = new_balance - escrow_balance
            if round(funds_in_to_trust) != 0:
                trust_fund["funds_difference"] = original_balance
                file_name = f"{next_day.strftime('%Y%m%d')}-I.CL-Banking-generated.xlsx"
                upload_presigned_url = f"{url}/banking/upload/presigned-url"
                params = {"clientId": client_id, "fileName": file_name}
                upload_url = f"{url}/banking/upload"
                payload = {"clientId": client_id, "fileName": file_name}
            else:
                continue
        if next_escrow_multiplier == current_escrow_multiplier:
            raise Exception("current and next escrow multipliers are same")

        data_list.append(trust_fund)

    header_dict = OrderedDict(
        [
            ("booking_ref", "BookingRef"),
            ("lead_pax", "LeadPax"),
            ("pax_count", "Pax"),
            ("booking_date", "BookingDate"),
            ("departure_date", "DepartureDate"),
            ("return_date", "ReturnDate"),
            ("supplier_ref", "SupplierRef"),
            ("supplier_name", "SupplierNames"),
            ("element", "Element"),
            ("currency", "Currency"),
            ("funds_difference", "Amount"),
            ("booking_type", "BookingType"),
            ("bonding", "Bonding"),
            ("payment_type", "PaymentType"),
            ("type", "Type"),
            ("total_booking_value", "TotalBookingValue"),
            ("days_to_process", "DaysToProcess"),
            ("dept_bal_lead_time", "DeptBalLeadTime"),
            ("payment_date", "PaymentDate"),
            ("booking_status", "BookingStatus"),
        ]
    )
    login_and_file_upload(upload_presigned_url, params, upload_url, payload, file_name, header_dict, data_list)


def login_and_file_upload(upload_presigned_url, params, upload_url, payload, file_name, header_dict, data_list):
    logging.info("user logging in")
    user_auth_token = requests.post(f"{url}/login", json={"username": user_name, "password": password}).json()
    token = user_auth_token["authenticationResult"]["AccessToken"]
    logging.info("returned auth_token")
    token_headers = {"Authorization": f"Bearer {token}"}
    presigned_details = requests.get(
        upload_presigned_url,
        headers=token_headers,
        params={**params, "claimFromTBR": "true" if params["claimFromTBR"] else "false"}
        if "claimFromTBR" in params
        else params,
    )
    if presigned_details.status_code == 200:
        presigned_json = presigned_details.json()
        presigned_url = presigned_json["presignedUrl"].strip('"')
        payload.update({"fileId": presigned_json["fileId"]})
        generate_excel_report(header_dict, data_list, file_name)
        with open(f"{TEMP_DIR}/{file_name}", "rb") as file_obj:
            data = file_obj.read()
        requests.put(
            presigned_url,
            data=data,
            headers={"Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
        )
        response = requests.post(
            upload_url,
            headers=token_headers,
            json=payload,
        )
        if response.status_code == 201:
            logging.info("file uploaded successfully")
        else:
            requests.get(f"{url}/logout", headers=token_headers)
            raise Exception("file upload failed")
    else:
        requests.get(f"{url}/logout", headers=token_headers)
        raise Exception("failed to get presigned url")

    requests.get(f"{url}/logout", headers=token_headers)
    logging.info("user logged out")
