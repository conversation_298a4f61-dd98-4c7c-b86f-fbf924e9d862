import json
import os
import tempfile
from datetime import datetime, timedelta
from typing import Dict, List, Any
import boto3
import pandas as pd
from pymongo import MongoClient
from bson import ObjectId
from helpers.secret_manager_handler import get_secret
from helpers.powerbi_transforms import (
    clean_banking_data, clean_claims_data, create_summary_tables,
    add_powerbi_metadata, validate_data_quality
)
import logging

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Environment variables
TEMP_DIR = tempfile.gettempdir()
POWERBI_EXPORT_BUCKET = os.environ.get("POWERBI_EXPORT_BUCKET", "ptt-powerbi-exports")
DATABASE_URI = os.environ.get("DATABASE_URI")

# MongoDB connection
client = MongoClient(DATABASE_URI or get_secret("eu-west-2"))
db = client.get_database()


def get_banking_data(days_back: int = 30) -> List[Dict[str, Any]]:
    """
    Fetch banking data from MongoDB for the last N days.
    Combines data from banking_file_details and trust_fund collections.
    """
    logger.info(f"Fetching banking data for last {days_back} days")
    
    # Calculate date range
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days_back)
    
    # Aggregation pipeline to get comprehensive banking data
    pipeline = [
        {
            "$match": {
                "created_at": {"$gte": start_date, "$lte": end_date},
                "deleted": {"$ne": True}
            }
        },
        {
            "$lookup": {
                "from": "client_basic_info",
                "localField": "client_id",
                "foreignField": "_id",
                "as": "client_info"
            }
        },
        {
            "$lookup": {
                "from": "trust_fund_v2",
                "let": {"client_id": "$client_id", "booking_ref": "$booking_ref"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$client_id", "$$client_id"]},
                                    {"$eq": ["$booking_ref", "$$booking_ref"]}
                                ]
                            }
                        }
                    }
                ],
                "as": "trust_fund_data"
            }
        },
        {
            "$unwind": {
                "path": "$client_info",
                "preserveNullAndEmptyArrays": True
            }
        },
        {
            "$unwind": {
                "path": "$trust_fund_data",
                "preserveNullAndEmptyArrays": True
            }
        },
        {
            "$project": {
                "_id": {"$toString": "$_id"},
                "client_id": {"$toString": "$client_id"},
                "client_name": "$client_info.friendly_name",
                "booking_ref": 1,
                "amount": 1,
                "currency_code": 1,
                "entry_type": 1,
                "payment_type": 1,
                "lead_pax": 1,
                "pax_count": 1,
                "booking_date": 1,
                "departure_date": 1,
                "return_date": 1,
                "total_booking_value": 1,
                "bonding": 1,
                "booking_status": 1,
                "file_date": 1,
                "created_at": 1,
                "updated_at": 1,
                # Trust fund data
                "balance": "$trust_fund_data.balance",
                "total_in_trust": "$trust_fund_data.total_in_trust",
                "total_claimed": "$trust_fund_data.total_claimed"
            }
        },
        {"$sort": {"created_at": -1}}
    ]
    
    banking_data = list(db.banking_file_details.aggregate(pipeline))
    logger.info(f"Retrieved {len(banking_data)} banking records")
    return banking_data


def get_claims_data(days_back: int = 30) -> List[Dict[str, Any]]:
    """
    Fetch claims data from MongoDB for the last N days.
    Combines data from claims_file_details and trust_fund collections.
    """
    logger.info(f"Fetching claims data for last {days_back} days")
    
    # Calculate date range
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days_back)
    
    # Aggregation pipeline to get comprehensive claims data
    pipeline = [
        {
            "$match": {
                "created_at": {"$gte": start_date, "$lte": end_date},
                "deleted": {"$ne": True}
            }
        },
        {
            "$lookup": {
                "from": "client_basic_info",
                "localField": "client_id",
                "foreignField": "_id",
                "as": "client_info"
            }
        },
        {
            "$lookup": {
                "from": "trust_fund_v2",
                "let": {"client_id": "$client_id", "booking_ref": "$booking_ref"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$client_id", "$$client_id"]},
                                    {"$eq": ["$booking_ref", "$$booking_ref"]}
                                ]
                            }
                        }
                    }
                ],
                "as": "trust_fund_data"
            }
        },
        {
            "$unwind": {
                "path": "$client_info",
                "preserveNullAndEmptyArrays": True
            }
        },
        {
            "$unwind": {
                "path": "$trust_fund_data",
                "preserveNullAndEmptyArrays": True
            }
        },
        {
            "$project": {
                "_id": {"$toString": "$_id"},
                "client_id": {"$toString": "$client_id"},
                "client_name": "$client_info.friendly_name",
                "booking_ref": 1,
                "amount": 1,
                "currency_code": 1,
                "payment_type": 1,
                "lead_pax": 1,
                "pax_count": 1,
                "booking_date": 1,
                "departure_date": 1,
                "return_date": 1,
                "total_booking_value": 1,
                "bonding": 1,
                "booking_status": 1,
                "file_date": 1,
                "created_at": 1,
                "updated_at": 1,
                # Trust fund data
                "balance": "$trust_fund_data.balance",
                "total_in_trust": "$trust_fund_data.total_in_trust",
                "total_claimed": "$trust_fund_data.total_claimed"
            }
        },
        {"$sort": {"created_at": -1}}
    ]
    
    claims_data = list(db.claims_file_details.aggregate(pipeline))
    logger.info(f"Retrieved {len(claims_data)} claims records")
    return claims_data


def get_summary_data() -> Dict[str, Any]:
    """
    Get summary statistics for banking and claims data.
    """
    logger.info("Generating summary data")
    
    # Banking summary by client and currency
    banking_summary_pipeline = [
        {
            "$match": {"deleted": {"$ne": True}}
        },
        {
            "$lookup": {
                "from": "client_basic_info",
                "localField": "client_id",
                "foreignField": "_id",
                "as": "client_info"
            }
        },
        {
            "$unwind": "$client_info"
        },
        {
            "$group": {
                "_id": {
                    "client_id": "$client_id",
                    "client_name": "$client_info.friendly_name",
                    "currency_code": "$currency_code"
                },
                "total_amount": {"$sum": "$amount"},
                "transaction_count": {"$sum": 1},
                "avg_amount": {"$avg": "$amount"},
                "latest_transaction": {"$max": "$created_at"}
            }
        },
        {
            "$project": {
                "client_id": {"$toString": "$_id.client_id"},
                "client_name": "$_id.client_name",
                "currency_code": "$_id.currency_code",
                "total_amount": 1,
                "transaction_count": 1,
                "avg_amount": 1,
                "latest_transaction": 1
            }
        }
    ]
    
    banking_summary = list(db.banking_file_details.aggregate(banking_summary_pipeline))
    
    # Claims summary by client and currency
    claims_summary_pipeline = [
        {
            "$match": {"deleted": {"$ne": True}}
        },
        {
            "$lookup": {
                "from": "client_basic_info",
                "localField": "client_id",
                "foreignField": "_id",
                "as": "client_info"
            }
        },
        {
            "$unwind": "$client_info"
        },
        {
            "$group": {
                "_id": {
                    "client_id": "$client_id",
                    "client_name": "$client_info.friendly_name",
                    "currency_code": "$currency_code"
                },
                "total_amount": {"$sum": "$amount"},
                "transaction_count": {"$sum": 1},
                "avg_amount": {"$avg": "$amount"},
                "latest_transaction": {"$max": "$created_at"}
            }
        },
        {
            "$project": {
                "client_id": {"$toString": "$_id.client_id"},
                "client_name": "$_id.client_name",
                "currency_code": "$_id.currency_code",
                "total_amount": 1,
                "transaction_count": 1,
                "avg_amount": 1,
                "latest_transaction": 1
            }
        }
    ]
    
    claims_summary = list(db.claims_file_details.aggregate(claims_summary_pipeline))
    
    return {
        "banking_summary": banking_summary,
        "claims_summary": claims_summary,
        "generated_at": datetime.utcnow().isoformat()
    }


def create_excel_file(banking_data: List[Dict], claims_data: List[Dict], summary_data: Dict) -> str:
    """
    Create Excel file with multiple sheets for banking, claims, and summary data.
    Returns the local file path.
    """
    logger.info("Creating Excel file with banking and claims data")

    # Generate filename with timestamp
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    filename = f"ptt_banking_claims_export_{timestamp}.xlsx"
    filepath = os.path.join(TEMP_DIR, filename)

    # Clean and transform data using helper functions
    banking_df = clean_banking_data(banking_data)
    claims_df = clean_claims_data(claims_data)

    # Add PowerBI metadata
    banking_df = add_powerbi_metadata(banking_df, 'banking')
    claims_df = add_powerbi_metadata(claims_df, 'claims')

    # Validate data quality
    banking_validation = validate_data_quality(banking_df, 'banking')
    claims_validation = validate_data_quality(claims_df, 'claims')

    # Create summary tables
    summary_tables = create_summary_tables(banking_df, claims_df)

    # Create Excel writer object
    with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
        # Banking data sheet
        if not banking_df.empty:
            banking_df.to_excel(writer, sheet_name='Banking_Data', index=False)
            logger.info(f"Added {len(banking_df)} banking records to Excel")

        # Claims data sheet
        if not claims_df.empty:
            claims_df.to_excel(writer, sheet_name='Claims_Data', index=False)
            logger.info(f"Added {len(claims_df)} claims records to Excel")

        # Summary sheets from helper functions
        for summary_name, summary_df in summary_tables.items():
            if not summary_df.empty:
                sheet_name = summary_name.replace('_', ' ').title()
                summary_df.to_excel(writer, sheet_name=sheet_name, index=False)

        # Legacy summary sheets for backward compatibility
        if summary_data.get('banking_summary'):
            banking_summary_df = pd.DataFrame(summary_data['banking_summary'])
            if 'latest_transaction' in banking_summary_df.columns:
                banking_summary_df['latest_transaction'] = pd.to_datetime(banking_summary_df['latest_transaction'], errors='coerce')
            banking_summary_df.to_excel(writer, sheet_name='Banking_Summary_Legacy', index=False)

        if summary_data.get('claims_summary'):
            claims_summary_df = pd.DataFrame(summary_data['claims_summary'])
            if 'latest_transaction' in claims_summary_df.columns:
                claims_summary_df['latest_transaction'] = pd.to_datetime(claims_summary_df['latest_transaction'], errors='coerce')
            claims_summary_df.to_excel(writer, sheet_name='Claims_Summary_Legacy', index=False)

        # Enhanced metadata sheet with validation results
        metadata_df = pd.DataFrame([{
            'export_timestamp': summary_data['generated_at'],
            'banking_records_count': len(banking_df),
            'claims_records_count': len(claims_df),
            'file_name': filename,
            'banking_validation_status': banking_validation['validation_status'],
            'claims_validation_status': claims_validation['validation_status'],
            'banking_issues': '; '.join(banking_validation.get('issues', [])),
            'claims_issues': '; '.join(claims_validation.get('issues', [])),
            'banking_warnings': '; '.join(banking_validation.get('warnings', [])),
            'claims_warnings': '; '.join(claims_validation.get('warnings', []))
        }])
        metadata_df.to_excel(writer, sheet_name='Export_Metadata', index=False)

    logger.info(f"Excel file created successfully: {filepath}")
    return filepath


def upload_to_s3(filepath: str, bucket: str) -> str:
    """
    Upload Excel file to S3 bucket.
    Returns the S3 key.
    """
    s3 = boto3.client('s3')
    filename = os.path.basename(filepath)
    s3_key = f"powerbi-exports/{filename}"
    
    logger.info(f"Uploading {filename} to S3 bucket {bucket}")
    
    try:
        s3.upload_file(filepath, bucket, s3_key)
        logger.info(f"Successfully uploaded to s3://{bucket}/{s3_key}")
        return s3_key
    except Exception as e:
        logger.error(f"Failed to upload to S3: {str(e)}")
        raise


def lambda_handler(event, context):
    """
    Main Lambda handler function.
    """
    try:
        logger.info("Starting PowerBI data export process")
        
        # Get configuration from event or use defaults
        days_back = event.get('days_back', 30)
        bucket = event.get('bucket', POWERBI_EXPORT_BUCKET)
        
        # Fetch data from MongoDB
        banking_data = get_banking_data(days_back)
        claims_data = get_claims_data(days_back)
        summary_data = get_summary_data()
        
        # Create Excel file
        excel_filepath = create_excel_file(banking_data, claims_data, summary_data)
        
        # Upload to S3
        s3_key = upload_to_s3(excel_filepath, bucket)
        
        # Clean up local file
        os.remove(excel_filepath)
        
        # Return success response
        response = {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'PowerBI export completed successfully',
                'bucket': bucket,
                's3_key': s3_key,
                'banking_records': len(banking_data),
                'claims_records': len(claims_data),
                'export_timestamp': summary_data['generated_at']
            })
        }
        
        logger.info("PowerBI data export completed successfully")
        return response
        
    except Exception as e:
        logger.error(f"Error in PowerBI data export: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e),
                'message': 'PowerBI export failed'
            })
        }
