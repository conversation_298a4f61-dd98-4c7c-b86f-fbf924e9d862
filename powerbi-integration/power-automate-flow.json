{"definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"aws_access_key": {"type": "string", "metadata": {"description": "AWS Access Key ID for S3 access"}}, "aws_secret_key": {"type": "securestring", "metadata": {"description": "AWS Secret Access Key for S3 access"}}, "s3_bucket_name": {"type": "string", "defaultValue": "ptt-powerbi-exports-prod", "metadata": {"description": "S3 bucket name containing PowerBI exports"}}, "sharepoint_site_url": {"type": "string", "metadata": {"description": "SharePoint site URL for file storage"}}, "sharepoint_library": {"type": "string", "defaultValue": "PowerBI Data", "metadata": {"description": "SharePoint document library name"}}}, "triggers": {"Recurrence": {"recurrence": {"frequency": "Day", "interval": 1, "schedule": {"hours": [7], "minutes": [0]}, "timeZone": "UTC"}, "type": "Recurrence"}}, "actions": {"List_S3_Objects": {"runAfter": {}, "type": "Http", "inputs": {"method": "GET", "uri": "https://@{parameters('s3_bucket_name')}.s3.amazonaws.com/?list-type=2&prefix=powerbi-exports/", "headers": {"Authorization": "AWS4-HMAC-SHA256 Credential=@{parameters('aws_access_key')}/********/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=@{variables('aws_signature')}"}}}, "Parse_S3_Response": {"runAfter": {"List_S3_Objects": ["Succeeded"]}, "type": "<PERSON><PERSON><PERSON><PERSON>", "inputs": {"content": "@body('List_S3_Objects')", "schema": {"type": "object", "properties": {"Contents": {"type": "array", "items": {"type": "object", "properties": {"Key": {"type": "string"}, "LastModified": {"type": "string"}, "Size": {"type": "integer"}}}}}}}}, "Filter_Latest_File": {"runAfter": {"Parse_S3_Response": ["Succeeded"]}, "type": "Query", "inputs": {"from": "@body('Parse_S3_Response')?['Contents']", "where": "@contains(item()?['Key'], 'ptt_banking_claims_export_')"}}, "Get_Latest_File": {"runAfter": {"Filter_Latest_File": ["Succeeded"]}, "type": "Compose", "inputs": "@first(body('Filter_Latest_File'))"}, "Download_From_S3": {"runAfter": {"Get_Latest_File": ["Succeeded"]}, "type": "Http", "inputs": {"method": "GET", "uri": "https://@{parameters('s3_bucket_name')}.s3.amazonaws.com/@{outputs('Get_Latest_File')?['Key']}", "headers": {"Authorization": "AWS4-HMAC-SHA256 Credential=@{parameters('aws_access_key')}/********/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=@{variables('aws_signature')}"}}}, "Upload_to_SharePoint": {"runAfter": {"Download_From_S3": ["Succeeded"]}, "type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['sharepointonline']['connectionId']"}}, "method": "post", "path": "/datasets/@{encodeURIComponent(parameters('sharepoint_site_url'))}/files", "queries": {"folderPath": "/@{parameters('sharepoint_library')}", "name": "ptt_banking_claims_latest.xlsx", "queryParametersSingleEncoded": true}, "body": "@body('Download_From_S3')"}}, "Send_Success_Notification": {"runAfter": {"Upload_to_SharePoint": ["Succeeded"]}, "type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['office365']['connectionId']"}}, "method": "post", "path": "/v2/Mail", "body": {"To": "<EMAIL>", "Subject": "PowerBI Data Export - Success", "Body": "<p>The daily PowerBI data export has completed successfully.</p><p>File: @{outputs('Get_Latest_File')?['Key']}</p><p>Size: @{outputs('Get_Latest_File')?['Size']} bytes</p><p>Uploaded to SharePoint at: @{utcNow()}</p>", "Importance": "Normal"}}}}, "outputs": {}}, "parameters": {"$connections": {"value": {"sharepointonline": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/sharepointonline", "connectionName": "sharepointonline", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/sharepointonline"}, "office365": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/office365", "connectionName": "office365", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/office365"}}}}}