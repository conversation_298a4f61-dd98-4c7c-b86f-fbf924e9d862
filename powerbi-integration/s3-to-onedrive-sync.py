#!/usr/bin/env python3
"""
Alternative Python script for syncing S3 files to OneDrive/SharePoint.
This can be used instead of Power Automate if you prefer a code-based solution.
Can be deployed as a separate Lambda function or run as a scheduled script.
"""

import os
import json
import logging
import tempfile
from datetime import datetime
from typing import Optional, Dict, Any

import boto3
import requests
from office365.runtime.auth.authentication_context import AuthenticationContext
from office365.sharepoint.client_context import ClientContext
from office365.sharepoint.files.file import File

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class S3ToSharePointSync:
    def __init__(self, 
                 s3_bucket: str,
                 sharepoint_site_url: str,
                 sharepoint_username: str,
                 sharepoint_password: str,
                 document_library: str = "PowerBI Data"):
        """
        Initialize the S3 to SharePoint sync utility.
        
        Args:
            s3_bucket: S3 bucket name containing exports
            sharepoint_site_url: SharePoint site URL
            sharepoint_username: SharePoint username
            sharepoint_password: SharePoint password
            document_library: SharePoint document library name
        """
        self.s3_bucket = s3_bucket
        self.sharepoint_site_url = sharepoint_site_url
        self.sharepoint_username = sharepoint_username
        self.sharepoint_password = sharepoint_password
        self.document_library = document_library
        
        # Initialize AWS S3 client
        self.s3_client = boto3.client('s3')
        
        # Initialize SharePoint context
        self.sp_context = self._get_sharepoint_context()
    
    def _get_sharepoint_context(self) -> ClientContext:
        """Authenticate and get SharePoint context."""
        try:
            auth_context = AuthenticationContext(self.sharepoint_site_url)
            auth_context.acquire_token_for_user(
                self.sharepoint_username, 
                self.sharepoint_password
            )
            
            context = ClientContext(self.sharepoint_site_url, auth_context)
            logger.info("Successfully authenticated with SharePoint")
            return context
            
        except Exception as e:
            logger.error(f"Failed to authenticate with SharePoint: {str(e)}")
            raise
    
    def get_latest_s3_file(self, prefix: str = "powerbi-exports/") -> Optional[Dict[str, Any]]:
        """
        Get the latest file from S3 bucket with the specified prefix.
        
        Args:
            prefix: S3 key prefix to filter files
            
        Returns:
            Dictionary with file information or None if no files found
        """
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.s3_bucket,
                Prefix=prefix
            )
            
            if 'Contents' not in response:
                logger.warning(f"No files found in S3 bucket {self.s3_bucket} with prefix {prefix}")
                return None
            
            # Filter for Excel files and sort by last modified
            excel_files = [
                obj for obj in response['Contents'] 
                if obj['Key'].endswith('.xlsx') and 'ptt_banking_claims_export_' in obj['Key']
            ]
            
            if not excel_files:
                logger.warning("No Excel export files found")
                return None
            
            # Get the most recent file
            latest_file = max(excel_files, key=lambda x: x['LastModified'])
            
            logger.info(f"Found latest file: {latest_file['Key']}")
            return latest_file
            
        except Exception as e:
            logger.error(f"Error listing S3 objects: {str(e)}")
            raise
    
    def download_from_s3(self, s3_key: str) -> str:
        """
        Download file from S3 to local temporary directory.
        
        Args:
            s3_key: S3 object key
            
        Returns:
            Local file path
        """
        try:
            # Create temporary file
            temp_dir = tempfile.gettempdir()
            local_filename = os.path.basename(s3_key)
            local_filepath = os.path.join(temp_dir, local_filename)
            
            # Download from S3
            logger.info(f"Downloading {s3_key} from S3 to {local_filepath}")
            self.s3_client.download_file(self.s3_bucket, s3_key, local_filepath)
            
            return local_filepath
            
        except Exception as e:
            logger.error(f"Error downloading from S3: {str(e)}")
            raise
    
    def upload_to_sharepoint(self, local_filepath: str, sharepoint_filename: str = None) -> bool:
        """
        Upload file to SharePoint document library.
        
        Args:
            local_filepath: Local file path
            sharepoint_filename: Target filename in SharePoint (optional)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not sharepoint_filename:
                sharepoint_filename = "ptt_banking_claims_latest.xlsx"
            
            # Get the document library
            target_folder = self.sp_context.web.lists.get_by_title(self.document_library).root_folder
            
            # Read file content
            with open(local_filepath, 'rb') as file_content:
                file_data = file_content.read()
            
            # Upload file
            logger.info(f"Uploading {local_filepath} to SharePoint as {sharepoint_filename}")
            target_file = target_folder.upload_file(sharepoint_filename, file_data)
            self.sp_context.execute_query()
            
            logger.info(f"Successfully uploaded to SharePoint: {target_file.serverRelativeUrl}")
            return True
            
        except Exception as e:
            logger.error(f"Error uploading to SharePoint: {str(e)}")
            return False
    
    def sync_latest_file(self) -> Dict[str, Any]:
        """
        Main sync function: get latest file from S3 and upload to SharePoint.
        
        Returns:
            Dictionary with sync results
        """
        result = {
            "success": False,
            "message": "",
            "file_info": None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            # Get latest file from S3
            latest_file = self.get_latest_s3_file()
            if not latest_file:
                result["message"] = "No files found in S3"
                return result
            
            # Download from S3
            local_filepath = self.download_from_s3(latest_file['Key'])
            
            try:
                # Upload to SharePoint
                upload_success = self.upload_to_sharepoint(local_filepath)
                
                if upload_success:
                    result["success"] = True
                    result["message"] = "File synced successfully"
                    result["file_info"] = {
                        "s3_key": latest_file['Key'],
                        "size": latest_file['Size'],
                        "last_modified": latest_file['LastModified'].isoformat()
                    }
                else:
                    result["message"] = "Failed to upload to SharePoint"
                
            finally:
                # Clean up local file
                if os.path.exists(local_filepath):
                    os.remove(local_filepath)
                    logger.info(f"Cleaned up local file: {local_filepath}")
            
        except Exception as e:
            result["message"] = f"Sync failed: {str(e)}"
            logger.error(f"Sync failed: {str(e)}")
        
        return result


def lambda_handler(event, context):
    """
    AWS Lambda handler for S3 to SharePoint sync.
    
    Expected environment variables:
    - S3_BUCKET: S3 bucket name
    - SHAREPOINT_SITE_URL: SharePoint site URL
    - SHAREPOINT_USERNAME: SharePoint username
    - SHAREPOINT_PASSWORD: SharePoint password (use AWS Secrets Manager)
    - DOCUMENT_LIBRARY: SharePoint document library name (optional)
    """
    try:
        # Get configuration from environment variables
        s3_bucket = os.environ.get('S3_BUCKET')
        sharepoint_site_url = os.environ.get('SHAREPOINT_SITE_URL')
        sharepoint_username = os.environ.get('SHAREPOINT_USERNAME')
        sharepoint_password = os.environ.get('SHAREPOINT_PASSWORD')
        document_library = os.environ.get('DOCUMENT_LIBRARY', 'PowerBI Data')
        
        # Validate required parameters
        if not all([s3_bucket, sharepoint_site_url, sharepoint_username, sharepoint_password]):
            raise ValueError("Missing required environment variables")
        
        # Initialize sync utility
        sync_util = S3ToSharePointSync(
            s3_bucket=s3_bucket,
            sharepoint_site_url=sharepoint_site_url,
            sharepoint_username=sharepoint_username,
            sharepoint_password=sharepoint_password,
            document_library=document_library
        )
        
        # Perform sync
        result = sync_util.sync_latest_file()
        
        # Return response
        return {
            'statusCode': 200 if result['success'] else 500,
            'body': json.dumps(result)
        }
        
    except Exception as e:
        logger.error(f"Lambda handler error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'message': f"Lambda execution failed: {str(e)}",
                'timestamp': datetime.utcnow().isoformat()
            })
        }


def main():
    """
    Main function for running as a standalone script.
    Configure these variables for your environment.
    """
    # Configuration - replace with your values
    config = {
        'S3_BUCKET': 'ptt-powerbi-exports-prod',
        'SHAREPOINT_SITE_URL': 'https://yourcompany.sharepoint.com/sites/powerbi',
        'SHAREPOINT_USERNAME': '<EMAIL>',
        'SHAREPOINT_PASSWORD': 'your-password',  # Use environment variable in production
        'DOCUMENT_LIBRARY': 'PowerBI Data'
    }
    
    # Set environment variables
    for key, value in config.items():
        os.environ[key] = value
    
    # Run sync
    result = lambda_handler({}, {})
    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    main()
