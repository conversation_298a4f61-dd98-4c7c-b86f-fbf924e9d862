# PowerBI Integration Setup Guide

This guide walks you through setting up the complete MongoDB to PowerBI data pipeline.

## Architecture Overview

```
MongoDB → AWS Lambda → S3 → Power Automate → SharePoint/OneDrive → Power BI
```

## Prerequisites

1. **AWS Account** with appropriate permissions
2. **Microsoft 365** subscription with Power Automate and Power BI
3. **SharePoint Online** or **OneDrive for Business**
4. **Power BI Pro** or **Power BI Premium** license

## Step 1: Deploy AWS Infrastructure

### 1.1 Deploy the Lambda Function

```bash
# Navigate to your project directory
cd /path/to/ptt-async-workflows

# Install dependencies
poetry install

# Deploy using Terraform (adjust for your environment)
cd provisioning/prod  # or nonprod
terraform plan
terraform apply
```

### 1.2 Verify Lambda Deployment

1. Check AWS Lambda console for `powerbi-data-export-lambda-{env}`
2. Verify S3 bucket `ptt-powerbi-exports-{env}` is created
3. Confirm EventBridge rule is scheduled for daily execution

### 1.3 Test Lambda Function

```bash
# Test the Lambda function manually
aws lambda invoke \
  --function-name powerbi-data-export-lambda-prod \
  --payload '{"days_back": 7, "bucket": "ptt-powerbi-exports-prod"}' \
  response.json

# Check the response
cat response.json
```

## Step 2: Set Up Power Automate Flow

### 2.1 Create SharePoint Document Library

1. Go to your SharePoint site
2. Create a new Document Library called "PowerBI Data"
3. Note the site URL for later use

### 2.2 Import Power Automate Flow

1. Go to [Power Automate](https://flow.microsoft.com)
2. Click **"My flows"** → **"Import"** → **"Import Package (Legacy)"**
3. Upload the `power-automate-flow.json` file
4. Configure connections:
   - **SharePoint Online**: Connect to your SharePoint site
   - **Office 365 Outlook**: For notifications (optional)

### 2.3 Configure Flow Parameters

Set the following parameters in your Power Automate flow:

- **aws_access_key**: Your AWS Access Key ID
- **aws_secret_key**: Your AWS Secret Access Key (store securely)
- **s3_bucket_name**: `ptt-powerbi-exports-prod` (or your environment)
- **sharepoint_site_url**: Your SharePoint site URL
- **sharepoint_library**: `PowerBI Data`

### 2.4 Alternative: Python Script for S3 to OneDrive Sync

If you prefer a Python script instead of Power Automate:

```python
# See s3-to-onedrive-sync.py for a complete implementation
import boto3
from office365.runtime.auth.authentication_context import AuthenticationContext
from office365.sharepoint.client_context import ClientContext

# This script can be scheduled as a cron job or another Lambda function
```

## Step 3: Create Power BI Report

### 3.1 Connect to SharePoint Data

1. Open **Power BI Desktop**
2. Click **"Get Data"** → **"SharePoint Online List"**
3. Enter your SharePoint site URL
4. Navigate to the **"PowerBI Data"** library
5. Select the Excel file: `ptt_banking_claims_latest.xlsx`

### 3.2 Load Data Tables

The Excel file contains multiple sheets:
- **Banking_Data**: All banking transactions
- **Claims_Data**: All claims transactions  
- **Banking_Summary**: Aggregated banking data by client/currency
- **Claims_Summary**: Aggregated claims data by client/currency
- **Export_Metadata**: File generation information

### 3.3 Create Data Model

1. **Load all sheets** into Power BI
2. **Create relationships** between tables if needed
3. **Set up calculated columns** and measures:

```dax
// Example DAX measures
Total Banking Amount = SUM(Banking_Data[amount])
Total Claims Amount = SUM(Claims_Data[amount])
Net Position = [Total Banking Amount] - [Total Claims Amount]
Transaction Count = COUNTROWS(Banking_Data) + COUNTROWS(Claims_Data)
```

### 3.4 Build Visualizations

Create dashboards with:
- **Summary cards** for key metrics
- **Time series charts** for trends
- **Client breakdown** tables
- **Currency analysis** charts
- **Balance reconciliation** reports

### 3.5 Publish to Power BI Service

1. Click **"Publish"** in Power BI Desktop
2. Choose your workspace
3. Configure **scheduled refresh**:
   - Go to Power BI Service
   - Find your dataset
   - Click **"Settings"** → **"Scheduled refresh"**
   - Set refresh frequency (daily recommended)

## Step 4: Configure Automated Refresh

### 4.1 Power BI Dataset Refresh

1. In Power BI Service, go to your dataset
2. Click **"Settings"** → **"Data source credentials"**
3. Configure SharePoint authentication
4. Set up **scheduled refresh**:
   - Frequency: Daily
   - Time: 8:00 AM (1 hour after data export)
   - Time zone: UTC

### 4.2 Refresh Schedule Coordination

Ensure proper timing:
1. **6:00 AM UTC**: Lambda exports data to S3
2. **7:00 AM UTC**: Power Automate syncs to SharePoint
3. **8:00 AM UTC**: Power BI refreshes dataset

## Step 5: Monitoring and Troubleshooting

### 5.1 Monitor Lambda Execution

```bash
# Check CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/powerbi-data-export"

# View recent logs
aws logs filter-log-events \
  --log-group-name "/aws/lambda/powerbi-data-export-lambda-prod" \
  --start-time $(date -d "1 hour ago" +%s)000
```

### 5.2 Monitor Power Automate Flow

1. Go to Power Automate → **"My flows"**
2. Click on your flow → **"Run history"**
3. Check for any failed runs and error messages

### 5.3 Monitor Power BI Refresh

1. Go to Power BI Service → Your workspace
2. Click on your dataset → **"Refresh history"**
3. Check for refresh failures and error details

### 5.4 Common Issues and Solutions

**Lambda timeout**: Increase timeout in Terraform (currently 15 minutes)
**Memory issues**: Increase Lambda memory (currently 2048 MB)
**S3 permissions**: Verify IAM roles and bucket policies
**SharePoint authentication**: Check Power Automate connections
**Power BI refresh fails**: Verify data source credentials

## Step 6: Data Customization

### 6.1 Modify Data Export

To change what data is exported, edit `powerbi-data-export-lambda.py`:

```python
# Add custom aggregations
def get_custom_summary():
    # Your custom MongoDB aggregation pipeline
    pass

# Modify date ranges
def lambda_handler(event, context):
    days_back = event.get('days_back', 90)  # Change default
```

### 6.2 Add New Data Sources

1. Create new aggregation functions in the Lambda
2. Add new sheets to the Excel export
3. Update Power BI data model
4. Create new visualizations

## Security Considerations

1. **AWS Credentials**: Use IAM roles, not hardcoded keys
2. **MongoDB Access**: Ensure proper network security groups
3. **SharePoint Permissions**: Limit access to the PowerBI Data library
4. **Power BI**: Use row-level security if needed

## Cost Optimization

1. **Lambda**: Optimize memory and timeout settings
2. **S3**: Set up lifecycle policies for old exports
3. **Power BI**: Use shared capacity efficiently
4. **Power Automate**: Monitor flow execution limits

## Support and Maintenance

- **Lambda logs**: CloudWatch for debugging
- **Data validation**: Check Export_Metadata sheet
- **Performance monitoring**: Track execution times
- **Regular updates**: Keep dependencies current

For additional support, refer to the AWS Lambda, Power Automate, and Power BI documentation.
