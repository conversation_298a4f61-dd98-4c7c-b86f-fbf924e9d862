resource "aws_cloudwatch_event_rule" "balance_finder_event_rule" {
  name                = "crons-rule-for-${local.function_name_balance_finder_lambda}"
  description         = "Trigger for Balance Finder Lambda ${var.env}"
  schedule_expression = "cron(0 0 * * ? *)"
}

resource "aws_cloudwatch_event_target" "balance_finder_event_target" {
  rule      = aws_cloudwatch_event_rule.balance_finder_event_rule.name
  target_id = "lambda"
  arn       = aws_lambda_function.balance_finder_lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_balance_finder" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.balance_finder_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.balance_finder_event_rule.arn
}

data "archive_file" "balance_finder_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_balance_finder_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "balance-finder-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_balance_finder_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_balance_finder_lambda" {
  name               = join("-", ["iam-for", local.function_name_balance_finder_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_balance_finder_lambda.json
}

resource "aws_iam_policy" "iam_policies_balance_finder_lambda" {
  name   = join("-", ["iam-policies-for-", local.function_name_balance_finder_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_balance_finder_lambda" {
  role       = aws_iam_role.iam_for_lambda_balance_finder_lambda.name
  policy_arn = aws_iam_policy.iam_policies_balance_finder_lambda.arn
}


resource "aws_lambda_function" "balance_finder_lambda" {
  filename         = "balance-finder-lambda.zip"
  source_code_hash = data.archive_file.balance_finder_lambda_zip.output_base64sha256
  function_name    = local.function_name_balance_finder_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_balance_finder_lambda.arn
  description      = format("%s Lambda", local.function_name_balance_finder_lambda)
  handler          = "balance-finder-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_balance_finder_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      SECRET_NAME = local.secret_name
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
