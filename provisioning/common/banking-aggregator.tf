data "archive_file" "banking_aggregator_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_banking_aggregator_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "banking-aggregator-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_banking_aggregator_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_banking_aggregator_lambda" {
  name               = join("-", ["iam-for", local.function_name_banking_aggregator_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_banking_aggregator_lambda.json
}

resource "aws_iam_policy" "iam_policies_banking_aggregator_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_banking_aggregator_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:PutObject",
                "s3:GetObject",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_banking_aggregator_lambda" {
  role       = aws_iam_role.iam_for_lambda_banking_aggregator_lambda.name
  policy_arn = aws_iam_policy.iam_policies_banking_aggregator_lambda.arn
}


resource "aws_lambda_function" "banking_aggregator_lambda" {
  filename         = "banking-aggregator-lambda.zip"
  source_code_hash = data.archive_file.banking_aggregator_lambda_zip.output_base64sha256
  function_name    = local.function_name_banking_aggregator_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_banking_aggregator_lambda.arn
  description      = format("%s Lambda", local.function_name_banking_aggregator_lambda)
  handler          = "banking-aggregator-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_banking_aggregator_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      BATCH_SIZE                   = local.batch_size
      SECRET_NAME                  = local.secret_name
      STATE_MACHINE_PAYLOAD_BUCKET = local.state_machine_payload_bucket
      REDIS_URL                    = local.redis_url
      REDIS_PASSWORD               = local.redis_password
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
