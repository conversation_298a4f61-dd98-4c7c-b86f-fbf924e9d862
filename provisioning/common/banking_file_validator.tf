data "archive_file" "banking_file_validator_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_banking_file_validator_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "banking-file-validator-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_banking_file_validator_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_banking_file_validator_lambda" {
  name               = join("-", ["iam-for", local.function_name_banking_file_validator_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_banking_file_validator_lambda.json
}

resource "aws_iam_policy" "iam_policies_banking_file_validator_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_banking_file_validator_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:GetObject",
                "s3:PutObject",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "policy_attach_banking_file_validator_lambda" {
  role       = aws_iam_role.iam_for_lambda_banking_file_validator_lambda.name
  policy_arn = aws_iam_policy.iam_policies_banking_file_validator_lambda.arn
}

resource "aws_lambda_function" "banking_file_validator_lambda" {
  filename         = "banking-file-validator-lambda.zip"
  source_code_hash = data.archive_file.banking_file_validator_lambda_zip.output_base64sha256
  function_name    = local.function_name_banking_file_validator_lambda
  timeout          = 900
  role             = aws_iam_role.iam_for_lambda_banking_file_validator_lambda.arn
  description      = format("%s Lambda", local.function_name_banking_file_validator_lambda)
  handler          = "banking-file-validator-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 10240
  tags = merge(local.tags_banking_file_validator_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      BATCH_SIZE                   = local.batch_size
      SECRET_NAME                  = local.secret_name
      BANKING_FILE_BUCKET          = local.banking_files_bucket
      STATE_MACHINE_PAYLOAD_BUCKET = local.state_machine_payload_bucket
      GTL                          = local.gtl
      PENNYWOOD                    = local.pennywood
      WLH_NEW                      = local.wlh_new
      ESKY                         = local.esky
      BALK                         = local.balk
      BLUESTYLE                    = local.bluestyle
      GVH                          = local.gvh
      WINTER_GREEN                 = local.winter_green
      SHEARINGS_ABTOT              = local.shearings_abtot
      CAMPINGS                     = local.campings
      MIGHTY_HOOPLA                = local.mighty_hoopla
      REDIS_URL                    = local.redis_url
      REDIS_PASSWORD               = local.redis_password
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
