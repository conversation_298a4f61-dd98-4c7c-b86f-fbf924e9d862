data "aws_iam_policy_document" "AWSStepFunctionTrustPolicyBanking" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["states.amazonaws.com"]
    }
  }
}
data "aws_iam_policy_document" "banking_orchestrator_policy_doc" {
  statement {
    sid       = "1"
    actions   = ["lambda:InvokeFunction"]
    resources = ["arn:aws:lambda:${var.aws_region}:*"]
  }
  statement {
    sid       = "2"
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:*"]
  }
  statement {
    sid       = "3"
    actions   = ["states:StartExecution"]
    resources = ["arn:aws:states:${var.aws_region}:${data.aws_caller_identity.current.account_id}:stateMachine:${local.banking_step_function_name}"]
  }
  statement {
    sid       = "4"
    actions   = ["s3:ListBucket"]
    resources = ["arn:aws:s3:::${local.state_machine_payload_bucket}"]
  }
}

resource "aws_iam_policy" "banking_orchestrator_policy" {
  name        = join("-", ["banking-workflow-orchestrator-policy", var.env])
  path        = "/"
  description = local.banking_step_function_name
  policy      = data.aws_iam_policy_document.banking_orchestrator_policy_doc.json
}

resource "aws_iam_role" "banking_orchestrator_role" {
  name               = join("-", [local.banking_step_function_name, "role"])
  tags               = merge(local.banking_step_function_tags, { role = "iam_role" })
  assume_role_policy = data.aws_iam_policy_document.AWSStepFunctionTrustPolicyBanking.json
}

resource "aws_iam_role_policy_attachment" "banking_orchestrator_policy_attachement" {
  role       = aws_iam_role.banking_orchestrator_role.name
  policy_arn = aws_iam_policy.banking_orchestrator_policy.arn
}

# define the aws step function
data "template_file" "banking_step_function_definition" {
  template = file(var.banking_step_function_definition_file)
  vars = {
    banking_file_fetch_lambda       = aws_lambda_function.banking_file_fetch_lambda.arn
    banking_invalidator_lambda      = aws_lambda_function.banking_invalidator_lambda.arn
    banking_file_validator_lambda   = aws_lambda_function.banking_file_validator_lambda.arn
    banking_persistor_lambda        = aws_lambda_function.banking_persistor_lambda.arn
    banking_aggregator_lambda       = aws_lambda_function.banking_aggregator_lambda.arn
    banking_anomaly_detector_lambda = aws_lambda_function.banking_anomaly_detector_lambda.arn
    banking_status_updator_lambda   = aws_lambda_function.banking_status_updator_lambda.arn
  }
}

resource "aws_sfn_state_machine" "banking_orchestrator_state_machine" {
  definition = data.template_file.banking_step_function_definition.rendered
  name       = local.banking_step_function_name
  role_arn   = aws_iam_role.banking_orchestrator_role.arn
  tags       = merge(local.banking_step_function_tags, { role = "step_function" })
}
