#!/bin/bash

export BASE_DIR=$(dirname "$0")
export PROJECT_DIR="${BASE_DIR}/../.."
export DIST_DIR="${BASE_DIR}/dist"
export LAYER_DEPENDENCIES_ZIP_NAME="layer_dependencies.zip"
export CWD_DIR=`pwd`
export EXCLUDE_PACKAGES="boto botocore boto3 pytest _pytest coverage docutils moto"

# copy out all required packages into a dir and zip it
cd "${PROJECT_DIR}"
source ".venv/bin/activate"
export PYTHON_ROOT=`echo $(which python) | rev | cut -d'/' -f3- | rev`
export SITE_PACKAGES="${PYTHON_ROOT}/lib/python3.8/site-packages/"
echo "Using site_packages ${SITE_PACKAGES} to create dependencies layer"
rm -rf "${DIST_DIR}"
mkdir -p "${DIST_DIR}/layer_dependencies/python"
cp -r ${SITE_PACKAGES}/* "${DIST_DIR}/layer_dependencies/python/"
# Copy helpers module from repo
cp -r "./helpers" "${DIST_DIR}/layer_dependencies/python/"
for PACKAGE in ${EXCLUDE_PACKAGES}
  do
    rm -rf "${DIST_DIR}/layer_dependencies/python/${PACKAGE}"
  done
cd "${DIST_DIR}/layer_dependencies"
zip -r "../${LAYER_DEPENDENCIES_ZIP_NAME}" .
echo "Created ${DIST_DIR}/${LAYER_DEPENDENCIES_ZIP_NAME} from ${DIST_DIR}/layer_dependencies/python"

cd ${CWD_DIR}