resource "aws_cloudwatch_event_rule" "caledonian_tbr_dropping_lambda_event_rule" {
  name                = "crons-rule-for-${local.function_name_caledonian_tbr_dropping_lambda}"
  description         = "Trigger for CALEDONIAN TBR dropping Lambda ${var.env}"
  schedule_expression = "cron(0 11 * * ? *)"
}

resource "aws_cloudwatch_event_rule" "caledonian_tbr_dropping_lambda_event_rule_1pm" {
  name                = "crons-rule-for-${local.function_name_caledonian_tbr_dropping_lambda}-1pm"
  description         = "Monday 1 PM trigger for CALEDONIAN TBR dropping Lambda ${var.env}"
  schedule_expression = "cron(0 13 ? * 2 *)"
}

resource "aws_cloudwatch_event_target" "caledonian_tbr_dropping_lambda_event_target" {
  rule      = aws_cloudwatch_event_rule.caledonian_tbr_dropping_lambda_event_rule.name
  target_id = "lambda"
  arn       = aws_lambda_function.caledonian_tbr_dropping_lambda.arn
}

resource "aws_cloudwatch_event_target" "caledonian_tbr_dropping_lambda_event_target_1pm" {
  rule      = aws_cloudwatch_event_rule.caledonian_tbr_dropping_lambda_event_rule_1pm.name
  target_id = "lambda-monday-1pm"
  arn       = aws_lambda_function.caledonian_tbr_dropping_lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_caledonian_tbr_dropping_lambda" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.caledonian_tbr_dropping_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.caledonian_tbr_dropping_lambda_event_rule.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_caledonian_tbr_dropping_lambda_1pm" {
  statement_id  = "AllowExecutionFromCloudWatchMonday1PM"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.caledonian_tbr_dropping_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.caledonian_tbr_dropping_lambda_event_rule_1pm.arn
}

data "archive_file" "caledonian_tbr_dropping_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_caledonian_tbr_dropping_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "caledonian-tbr-dropping-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_caledonian_tbr_dropping_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_caledonian_tbr_dropping_lambda" {
  name               = join("-", ["iam-for", local.function_name_caledonian_tbr_dropping_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_caledonian_tbr_dropping_lambda.json
}

resource "aws_iam_policy" "iam_policies_caledonian_tbr_dropping_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_caledonian_tbr_dropping_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:GetObject",
                "s3:PutObject"
              ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup"
              ],
            "Resource": [
                "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:*"
            ]
        },
        {
		      "Effect": "Allow",
          "Action": [
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
         "Resource": "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${local.function_name_caledonian_tbr_dropping_lambda}:*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "arn:aws:secretsmanager:eu-west-2:${data.aws_caller_identity.current.account_id}:secret:ptt-${var.env}/secrets*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_caledonian_tbr_dropping_lambda" {
  role       = aws_iam_role.iam_for_lambda_caledonian_tbr_dropping_lambda.name
  policy_arn = aws_iam_policy.iam_policies_caledonian_tbr_dropping_lambda.arn
}


resource "aws_lambda_function" "caledonian_tbr_dropping_lambda" {
  filename         = "caledonian-tbr-dropping-lambda.zip"
  source_code_hash = data.archive_file.caledonian_tbr_dropping_lambda_zip.output_base64sha256
  function_name    = local.function_name_caledonian_tbr_dropping_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_caledonian_tbr_dropping_lambda.arn
  description      = format("%s Lambda", local.function_name_caledonian_tbr_dropping_lambda)
  handler          = "caledonian-tbr-dropping-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_caledonian_tbr_dropping_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT    = var.env
      SECRET_NAME    = local.secret_name
      URL            = local.url
      CALEDONIAN     = local.caledonian
      REPORTS_BUCKET = local.reports_bucket
      PTT_BUCKET     = local.sftp_to_ptt_s3_bucket
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}

resource "aws_iam_role" "caledonian_tbr_role" {
  name = "${local.caledonian_name}-Role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "caledonian_tbr_policy" {
  name = "${local.caledonian_name}-Policy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:PutObject",
          "s3:GetObject",
        ],
        Effect = "Allow",
        Resource = [
          "${aws_s3_bucket.sftp-to-ptt-s3-bucket[0].arn}",
          "${aws_s3_bucket.sftp-to-ptt-s3-bucket[0].arn}/*",
        ],
      },
    ],
  })
}

resource "aws_iam_policy_attachment" "caledonian_tbr_role_attachment" {
  name       = "${local.caledonian_name}_role_attachment"
  roles      = [aws_iam_role.caledonian_tbr_role.name]
  policy_arn = aws_iam_policy.caledonian_tbr_policy.arn
}
