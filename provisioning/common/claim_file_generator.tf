data "archive_file" "claim_file_generator_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_claim_file_generator_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "claim-file-generator-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_claim_file_generator_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_claim_file_generator_lambda" {
  name               = join("-", ["iam-for", local.function_name_claim_file_generator_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_claim_file_generator_lambda.json
}

resource "aws_iam_policy" "iam_policies_claim_file_generator_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_claim_file_generator_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:GetObject",
                "s3:PutObject",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "policy_attach_claim_file_generator_lambda" {
  role       = aws_iam_role.iam_for_lambda_claim_file_generator_lambda.name
  policy_arn = aws_iam_policy.iam_policies_claim_file_generator_lambda.arn
}

resource "aws_lambda_function" "claim_file_generator_lambda" {
  filename         = "claim-file-generator-lambda.zip"
  source_code_hash = data.archive_file.claim_file_generator_lambda_zip.output_base64sha256
  function_name    = local.function_name_claim_file_generator_lambda
  timeout          = 900
  role             = aws_iam_role.iam_for_lambda_claim_file_generator_lambda.arn
  description      = format("%s Lambda", local.function_name_claim_file_generator_lambda)
  handler          = "claim-file-generator-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 4096
  tags = merge(local.tags_claim_file_generator_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      SECRET_NAME       = local.secret_name
      CLAIM_FILE_BUCKET = local.claim_files_bucket
      NAS               = local.nas
      FLYPOP            = local.flypop
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
