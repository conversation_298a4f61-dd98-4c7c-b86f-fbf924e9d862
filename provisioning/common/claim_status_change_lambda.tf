data "archive_file" "claim_status_change_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_claim_status_change_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "claim-status-change-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_claim_status_change_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_claim_status_change_lambda" {
  name               = join("-", ["iam-for", local.function_name_claim_status_change_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_claim_status_change_lambda.json
}

resource "aws_iam_policy" "iam_policies_claim_status_change_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_claim_status_change_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:GetObject",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_claim_status_change_lambda" {
  role       = aws_iam_role.iam_for_lambda_claim_status_change_lambda.name
  policy_arn = aws_iam_policy.iam_policies_claim_status_change_lambda.arn
}


resource "aws_lambda_function" "claim_status_change_lambda" {
  filename         = "claim-status-change-lambda.zip"
  source_code_hash = data.archive_file.claim_status_change_lambda_zip.output_base64sha256
  function_name    = local.function_name_claim_status_change_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_claim_status_change_lambda.arn
  description      = format("%s Lambda", local.function_name_claim_status_change_lambda)
  handler          = "claim-status-change-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_claim_status_change_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      SECRET_NAME                  = local.secret_name
      STATE_MACHINE_PAYLOAD_BUCKET = local.state_machine_payload_bucket
      NAS                          = local.nas
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
