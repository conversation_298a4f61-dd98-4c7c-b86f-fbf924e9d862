data "aws_iam_policy_document" "AWSStepFunctionTrustPolicyClaim" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["states.amazonaws.com"]
    }
  }
}
data "aws_iam_policy_document" "claim_orchestrator_policy_doc" {
  statement {
    sid       = "1"
    actions   = ["lambda:InvokeFunction"]
    resources = ["arn:aws:lambda:${var.aws_region}:*"]
  }
  statement {
    sid       = "2"
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:*"]
  }
  statement {
    sid       = "3"
    actions   = ["states:StartExecution"]
    resources = ["arn:aws:states:${var.aws_region}:${data.aws_caller_identity.current.account_id}:stateMachine:${local.claim_step_function_name}"]
  }
  statement {
    sid       = "4"
    actions   = ["s3:ListBucket"]
    resources = ["arn:aws:s3:::${local.state_machine_payload_bucket}"]
  }
}

resource "aws_iam_policy" "claim_orchestrator_policy" {
  name        = join("-", ["claim-workflow-orchestrator-policy", var.env])
  path        = "/"
  description = local.claim_step_function_name
  policy      = data.aws_iam_policy_document.claim_orchestrator_policy_doc.json
}

resource "aws_iam_role" "claim_orchestrator_role" {
  name               = join("-", [local.claim_step_function_name, "role"])
  tags               = merge(local.claim_step_function_tags, { role = "iam_role" })
  assume_role_policy = data.aws_iam_policy_document.AWSStepFunctionTrustPolicyClaim.json
}

resource "aws_iam_role_policy_attachment" "claim_orchestrator_policy_attachement" {
  role       = aws_iam_role.claim_orchestrator_role.name
  policy_arn = aws_iam_policy.claim_orchestrator_policy.arn
}

# define the aws step function
data "template_file" "claim_step_function_definition" {
  template = file(var.claim_step_function_definition_file)
  vars = {
    claim_file_fetch_lambda       = aws_lambda_function.claim_file_fetch_lambda.arn
    claim_invalidator_lambda      = aws_lambda_function.claim_invalidator_lambda.arn
    claim_file_generator_lambda   = aws_lambda_function.claim_file_generator_lambda.arn
    claim_file_validator_lambda   = aws_lambda_function.claim_file_validator_lambda.arn
    claim_persistor_lambda        = aws_lambda_function.claim_persistor_lambda.arn
    claim_aggregator_lambda       = aws_lambda_function.claim_aggregator_lambda.arn
    claim_anomaly_detector_lambda = aws_lambda_function.claim_anomaly_detector_lambda.arn
    claim_status_updator_lambda   = aws_lambda_function.claim_status_updator_lambda.arn
  }
}

resource "aws_sfn_state_machine" "claim_orchestrator_state_machine" {
  definition = data.template_file.claim_step_function_definition.rendered
  name       = local.claim_step_function_name
  role_arn   = aws_iam_role.claim_orchestrator_role.arn
  tags       = merge(local.claim_step_function_tags, { role = "step_function" })
}
