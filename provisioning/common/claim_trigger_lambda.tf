data "archive_file" "claim_trigger_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_claim_trigger_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "claim-trigger-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_claim_trigger_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_claim_trigger_lambda" {
  name               = join("-", ["iam-for", local.function_name_claim_trigger_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_claim_trigger_lambda.json
}

resource "aws_iam_policy" "iam_policies_claim_trigger_lambda" {
  name   = join("-", ["iam-policies-for-", local.function_name_claim_trigger_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "states:ListStateMachines",
                "states:StartExecution"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_claim_trigger_lambda" {
  role       = aws_iam_role.iam_for_lambda_claim_trigger_lambda.name
  policy_arn = aws_iam_policy.iam_policies_claim_trigger_lambda.arn
}


resource "aws_lambda_function" "claim-trigger-lambda" {
  filename         = "claim-trigger-lambda.zip"
  source_code_hash = data.archive_file.claim_trigger_lambda_zip.output_base64sha256
  function_name    = local.function_name_claim_trigger_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_claim_trigger_lambda.arn
  description      = format("%s Lambda", local.function_name_claim_trigger_lambda)
  handler          = "claim-trigger-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_claim_trigger_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT = var.env
    }
  }
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
