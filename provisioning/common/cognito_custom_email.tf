data "archive_file" "cognito_custom_email_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_cognito_custom_email_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "cognito-custom-email-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_cognito_custom_email_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_cognito_custom_email_lambda" {
  name               = join("-", ["iam-for", local.function_name_cognito_custom_email_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_cognito_custom_email_lambda.json
}

resource "aws_iam_policy" "iam_policies_cognito_custom_email_lambda" {
  name   = join("-", ["iam-policies-for-", local.function_name_cognito_custom_email_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:GetObject"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_cognito_custom_email_lambda" {
  role       = aws_iam_role.iam_for_lambda_cognito_custom_email_lambda.name
  policy_arn = aws_iam_policy.iam_policies_cognito_custom_email_lambda.arn
}


resource "aws_lambda_function" "cognito-custom-email-lambda" {
  filename         = "cognito-custom-email-lambda.zip"
  source_code_hash = data.archive_file.cognito_custom_email_lambda_zip.output_base64sha256
  function_name    = local.function_name_cognito_custom_email_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_cognito_custom_email_lambda.arn
  description      = format("%s Lambda", local.function_name_cognito_custom_email_lambda)
  handler          = "cognito-custom-email-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_cognito_custom_email_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT = var.env
      LOGO_BUCKET = local.logo_bucket
    }
  }
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
