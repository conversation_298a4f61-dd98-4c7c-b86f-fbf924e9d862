data "archive_file" "data_transform_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_data_transform_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "data-transform-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_data_transform_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_data_transform_lambda" {
  name               = join("-", ["iam-for", local.function_name_data_transform_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_data_transform_lambda.json
}

resource "aws_iam_policy" "iam_policies_data_transform_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_data_transform_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:PutObject",
                "s3:GetObject",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_data_transform_lambda" {
  role       = aws_iam_role.iam_for_lambda_data_transform_lambda.name
  policy_arn = aws_iam_policy.iam_policies_data_transform_lambda.arn
}


resource "aws_lambda_function" "data_transform_lambda" {
  filename         = "data-transform-lambda.zip"
  source_code_hash = data.archive_file.data_transform_lambda_zip.output_base64sha256
  function_name    = local.function_name_data_transform_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_data_transform_lambda.arn
  description      = format("%s Lambda", local.function_name_data_transform_lambda)
  handler          = "data-transform-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_data_transform_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      SECRET_NAME                  = local.secret_name
      STATE_MACHINE_PAYLOAD_BUCKET = local.state_machine_payload_bucket
      REPORTS_BUCKET               = local.reports_bucket
      MAJOR_TRAVEL                 = local.major_travel
      WLH                          = local.wlh
      CLIENT_IDS                   = local.nas
      IGLU_ESCROW                  = local.iglu_escrow
      LIVESCORE                    = local.livescore
      BANKING_FILE_BUCKET          = local.banking_files_bucket
      STATE_MACHINE_PAYLOAD_BUCKET = local.state_machine_payload_bucket
      PENNYWOOD                    = local.pennywood
      BLUESTYLE                    = local.bluestyle
      ANGLIA_TOURS                 = local.anglia_tours
      WST_TRAVEL                   = local.wst_travel
      NAS                          = local.nas
      FLYPOP                       = local.flypop
      GTL                          = local.gtl
      BARRHEAD                     = local.barrhead
      HAYS                         = local.hays
      SUNSHINE                     = local.sunshine
      BROADWAY                     = local.broadway
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
