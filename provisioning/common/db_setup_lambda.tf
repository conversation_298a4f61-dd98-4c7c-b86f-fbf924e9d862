data "archive_file" "db_setup_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_db_setup_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "db-setup-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_db_setup_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_db_setup_lambda" {
  name               = join("-", ["iam-for", local.function_name_db_setup_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_db_setup_lambda.json
}

resource "aws_iam_policy" "iam_policies_db_setup_lambda" {
  name   = join("-", ["iam-policies-for-", local.function_name_db_setup_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "secretsmanager:GetSecretValue",
                "cognito-idp:ListUsersInGroup"
              ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_db_setup_lambda" {
  role       = aws_iam_role.iam_for_lambda_db_setup_lambda.name
  policy_arn = aws_iam_policy.iam_policies_db_setup_lambda.arn
}


resource "aws_lambda_function" "db-setup-lambda" {
  filename         = "db-setup-lambda.zip"
  source_code_hash = data.archive_file.db_setup_lambda_zip.output_base64sha256
  function_name    = local.function_name_db_setup_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_db_setup_lambda.arn
  description      = format("%s Lambda", local.function_name_db_setup_lambda)
  handler          = "db-setup-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_db_setup_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT  = var.env
      SECRET_NAME  = local.secret_name
      USER_POOL_ID = tolist(data.aws_cognito_user_pools.ptt_cognito_user_pool.ids)[0]
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
