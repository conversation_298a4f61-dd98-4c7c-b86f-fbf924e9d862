resource "aws_cloudwatch_event_rule" "iglu_escrow_scheduler_lambda_event_rule" {
  name                = "crons-rule-for-${local.function_name_iglu_escrow_scheduler_lambda}"
  description         = "Trigger for Iglu Escrow Scheduler Lambda ${var.env}"
  schedule_expression = "cron(50 23 L * ? *)"
}

resource "aws_cloudwatch_event_target" "iglu_escrow_scheduler_lambda_event_target" {
  rule      = aws_cloudwatch_event_rule.iglu_escrow_scheduler_lambda_event_rule.name
  target_id = "lambda"
  arn       = aws_lambda_function.iglu_escrow_scheduler_lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_iglu_escrow_scheduler_lambda" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.iglu_escrow_scheduler_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.iglu_escrow_scheduler_lambda_event_rule.arn
}

data "archive_file" "iglu_escrow_scheduler_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_iglu_escrow_scheduler_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "iglu-escrow-scheduler-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_iglu_escrow_scheduler_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_iglu_escrow_scheduler_lambda" {
  name               = join("-", ["iam-for", local.function_name_iglu_escrow_scheduler_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_iglu_escrow_scheduler_lambda.json
}

resource "aws_iam_policy" "iam_policies_iglu_escrow_scheduler_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_iglu_escrow_scheduler_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:GetObject",
                "s3:PutObject"
              ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup"
              ],
            "Resource": [
                "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:*"
            ]
        },
        {
		      "Effect": "Allow",
          "Action": [
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
         "Resource": "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${local.function_name_iglu_escrow_scheduler_lambda}:*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "arn:aws:secretsmanager:eu-west-2:${data.aws_caller_identity.current.account_id}:secret:ptt-${var.env}/secrets*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_iglu_escrow_scheduler_lambda" {
  role       = aws_iam_role.iam_for_lambda_iglu_escrow_scheduler_lambda.name
  policy_arn = aws_iam_policy.iam_policies_iglu_escrow_scheduler_lambda.arn
}


resource "aws_lambda_function" "iglu_escrow_scheduler_lambda" {
  filename         = "iglu-escrow-scheduler-lambda.zip"
  source_code_hash = data.archive_file.iglu_escrow_scheduler_lambda_zip.output_base64sha256
  function_name    = local.function_name_iglu_escrow_scheduler_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_iglu_escrow_scheduler_lambda.arn
  description      = format("%s Lambda", local.function_name_iglu_escrow_scheduler_lambda)
  handler          = "iglu-escrow-scheduler-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_iglu_escrow_scheduler_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT     = var.env
      SECRET_NAME     = local.secret_name
      URL             = local.url
      IGLU_ESCROW     = local.iglu_escrow
      REPORTS_BUCKET  = local.reports_bucket
      IGLU_TBR_BUCKET = local.iglu_tbr_bucket
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}

resource "aws_iam_role" "iglu_tbr_role" {
  name = "${local.iglu_name}-Role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "iglu_tbr_policy" {
  name = "${local.iglu_name}-Policy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:PutObject",
          "s3:GetObject",
        ],
        Effect = "Allow",
        Resource = [
          "${aws_s3_bucket.iglu_tbr_bucket.arn}",
          "${aws_s3_bucket.iglu_tbr_bucket.arn}/*",
        ],
      },
    ],
  })
}

resource "aws_iam_policy_attachment" "iglu_tbr_role_attachment" {
  name       = "${local.iglu_name}_role_attachment"
  roles      = [aws_iam_role.iglu_tbr_role.name]
  policy_arn = aws_iam_policy.iglu_tbr_policy.arn
}
