
locals {
  banking_step_function_name = "${var.banking_step_function_name}-${var.env}"
  banking_step_function_tags = {
    name = var.banking_step_function_name
  }

  claim_step_function_name = "${var.claim_step_function_name}-${var.env}"
  claim_step_function_tags = {
    name = var.claim_step_function_name
  }

  banking_status_change_step_function_name = "${var.banking_status_change_step_function_name}-${var.env}"
  banking_status_change_step_function_tags = {
    name = var.banking_status_change_step_function_name
  }

  claim_status_change_step_function_name = "${var.claim_status_change_step_function_name}-${var.env}"
  claim_status_change_step_function_tags = {
    name = var.claim_status_change_step_function_name
  }

  function_name_banking_trigger_lambda = "${var.function_name_banking_trigger_lambda}-${var.env}"
  tags_banking_trigger_lambda = {
    name = local.function_name_banking_trigger_lambda
    env  = var.env
  }
  function_name_db_setup_lambda = "${var.function_name_db_setup_lambda}-${var.env}"
  tags_db_setup_lambda = {
    name = local.function_name_db_setup_lambda
    env  = var.env
  }

  function_name_banking_file_fetch_lambda = "${var.function_name_banking_file_fetch_lambda}-${var.env}"
  tags_banking_file_fetch_lambda = {
    name = local.function_name_banking_file_fetch_lambda
    env  = var.env
  }

  function_name_banking_invalidator_lambda = "${var.function_name_banking_invalidator_lambda}-${var.env}"
  tags_banking_invalidator_lambda = {
    name = local.function_name_banking_invalidator_lambda
    env  = var.env
  }

  function_name_banking_file_validator_lambda = "${var.function_name_banking_file_validator_lambda}-${var.env}"
  tags_banking_file_validator_lambda = {
    name = local.function_name_banking_file_validator_lambda
    env  = var.env
  }

  function_name_banking_persistor_lambda = "${var.function_name_banking_persistor_lambda}-${var.env}"
  tags_banking_persistor_lambda = {
    name = local.function_name_banking_persistor_lambda
    env  = var.env
  }

  function_name_banking_aggregator_lambda = "${var.function_name_banking_aggregator_lambda}-${var.env}"

  function_name_data_transform_lambda = "${var.function_name_data_transform_lambda}-${var.env}"

  tags_banking_aggregator_lambda = {
    name = local.function_name_banking_aggregator_lambda
    env  = var.env
  }

   tags_data_transform_lambda = {
    name = local.function_name_data_transform_lambda
    env  = var.env
  }

  function_name_banking_anomaly_detector_lambda = "${var.function_name_banking_anomaly_detector_lambda}-${var.env}"
  tags_banking_anomaly_detector_lambda = {
    name = local.function_name_banking_anomaly_detector_lambda
    env  = var.env
  }

  function_name_banking_status_updator_lambda = "${var.function_name_banking_status_updator_lambda}-${var.env}"
  tags_banking_status_updator_lambda = {
    name = local.function_name_banking_status_updator_lambda
    env  = var.env
  }

  function_name_claim_trigger_lambda = "${var.function_name_claim_trigger_lambda}-${var.env}"
  tags_claim_trigger_lambda = {
    name = local.function_name_claim_trigger_lambda
    env  = var.env
  }

  function_name_claim_file_fetch_lambda = "${var.function_name_claim_file_fetch_lambda}-${var.env}"
  tags_claim_file_fetch_lambda = {
    name = local.function_name_claim_file_fetch_lambda
    env  = var.env
  }

  function_name_claim_invalidator_lambda = "${var.function_name_claim_invalidator_lambda}-${var.env}"
  tags_claim_invalidator_lambda = {
    name = local.function_name_claim_invalidator_lambda
    env  = var.env
  }

  function_name_claim_file_generator_lambda = "${var.function_name_claim_file_generator_lambda}-${var.env}"
  tags_claim_file_generator_lambda = {
    name = local.function_name_claim_file_generator_lambda
    env  = var.env
  }

  function_name_claim_file_validator_lambda = "${var.function_name_claim_file_validator_lambda}-${var.env}"
  tags_claim_file_validator_lambda = {
    name = local.function_name_claim_file_validator_lambda
    env  = var.env
  }

  function_name_claim_persistor_lambda = "${var.function_name_claim_persistor_lambda}-${var.env}"
  tags_claim_persistor_lambda = {
    name = local.function_name_claim_persistor_lambda
    env  = var.env
  }

  function_name_claim_aggregator_lambda = "${var.function_name_claim_aggregator_lambda}-${var.env}"
  tags_claim_aggregator_lambda = {
    name = local.function_name_claim_aggregator_lambda
    env  = var.env
  }

  function_name_claim_anomaly_detector_lambda = "${var.function_name_claim_anomaly_detector_lambda}-${var.env}"
  tags_claim_anomaly_detector_lambda = {
    name = local.function_name_claim_anomaly_detector_lambda
    env  = var.env
  }

  function_name_claim_status_updator_lambda = "${var.function_name_claim_status_updator_lambda}-${var.env}"
  tags_claim_status_updator_lambda = {
    name = local.function_name_claim_status_updator_lambda
    env  = var.env
  }

  function_name_balance_finder_lambda = "${var.function_name_balance_finder_lambda}-${var.env}"
  tags_balance_finder_lambda = {
    name = local.function_name_balance_finder_lambda
    env  = var.env
  }

  function_name_banking_status_change_trigger_lambda = "${var.function_name_banking_status_change_trigger_lambda}-${var.env}"
  tags_banking_status_change_trigger_lambda = {
    name = local.function_name_banking_status_change_trigger_lambda
    env  = var.env
  }

  function_name_claim_status_change_trigger_lambda = "${var.function_name_claim_status_change_trigger_lambda}-${var.env}"
  tags_claim_status_change_trigger_lambda = {
    name = local.function_name_claim_status_change_trigger_lambda
    env  = var.env
  }

  function_name_banking_status_change_lambda = "${var.function_name_banking_status_change_lambda}-${var.env}"
  tags_banking_status_change_lambda = {
    name = local.function_name_banking_status_change_lambda
    env  = var.env
  }

  function_name_claim_status_change_lambda = "${var.function_name_claim_status_change_lambda}-${var.env}"
  tags_claim_status_change_lambda = {
    name = local.function_name_claim_status_change_lambda
    env  = var.env
  }

  function_name_booking_ref_modifier_lambda = "${var.function_name_booking_ref_modifier_lambda}-${var.env}"
  tags_booking_ref_modifier_lambda = {
    name = local.function_name_booking_ref_modifier_lambda
    env  = var.env
  }
  function_name_cognito_custom_email_lambda = "${var.function_name_cognito_custom_email_lambda}-${var.env}"
  tags_cognito_custom_email_lambda = {
    name = local.function_name_cognito_custom_email_lambda
    env  = var.env
  }

  function_name_sftp_lambda = "${var.function_name_sftp_lambda}-${var.env}"
  tags_sftp_lambda = {
    name = local.function_name_sftp_lambda
    env  = var.env
  }

  function_name_sftp_auth_lambda = "${var.function_name_sftp_auth_lambda}-${var.env}"
  tags_sftp_auth_lambda = {
    name = local.function_name_sftp_auth_lambda
    env  = var.env
  }

  function_name_exposure_status_lambda = "${var.function_name_exposure_status_lambda}-${var.env}"
  tags_exposure_status_lambda = {
    name = local.function_name_exposure_status_lambda
    env  = var.env
  }

  function_name_risk_exposure_insight_lambda = "${var.function_name_risk_exposure_insight_lambda}-${var.env}"
  tags_risk_exposure_insight_lambda = {
    name = local.function_name_risk_exposure_insight_lambda
    env  = var.env
  }

  function_name_banking_claim_summary_file_deletion_lambda = "${var.function_name_banking_claim_summary_file_deletion_lambda}-${var.env}"
  tags_banking_claim_summary_file_deletion_lambda = {
    name = local.function_name_banking_claim_summary_file_deletion_lambda
    env  = var.env
  }

  function_name_iglu_escrow_scheduler_lambda = "${var.function_name_iglu_escrow_scheduler_lambda}-${var.env}"
  tags_iglu_escrow_scheduler_lambda = {
    name = local.function_name_iglu_escrow_scheduler_lambda
    env  = var.env
  }

  function_name_sftp_to_ptt_lambda = "${var.function_name_sftp_to_ptt_lambda}-${var.env}"
  tags_sftp_to_ptt_lambda = {
    name = local.function_name_sftp_to_ptt_lambda
    env  = var.env
  }

  function_name_gtl_tbr_dropping_lambda = "${var.function_name_gtl_tbr_dropping_lambda}-${var.env}"
  tags_gtl_tbr_dropping_lambda = {
    name = local.function_name_gtl_tbr_dropping_lambda
    env  = var.env
  }

  function_name_caledonian_tbr_dropping_lambda = "${var.function_name_caledonian_tbr_dropping_lambda}-${var.env}"
  tags_caledonian_tbr_dropping_lambda = {
    name = local.function_name_caledonian_tbr_dropping_lambda
    env  = var.env
  }

  function_name_wlh_net_amount_calculator_lambda = "${var.function_name_wlh_net_amount_calculator_lambda}-${var.env}"
  tags_wlh_net_amount_calculator_lambda = {
    name = local.function_name_wlh_net_amount_calculator_lambda
    env  = var.env
  }

  banking_files_bucket = "ptt-banking-files-${var.env}"
  claim_files_bucket   = "ptt-claim-files-${var.env}"

  // note that ${path.module} resolves to the dir containing this file, eg 'provisioning/common/'
  layer_dependencies_build = "${path.module}/build_layer_dependencies.sh"
  layer_dependencies_zip   = "${path.module}/../../dist/layer_dependencies.zip"

  lambda_sg_name = "ptt-lambda-sg-${var.env}"

  state_machine_payload_bucket = "ptt-state-machine-payload-${var.env}"

  banking_and_claim_summary_bucket = "ptt-banking-and-claim-summary-bucket-${var.env}"

  sftp_name = "sftp-${var.env}"

  iglu_name = "iglu-${var.env}"

  gtl_name = "gtl-${var.env}"

  caledonian_name = "caledonian-${var.env}"

  wlh_name = "wlh-${var.env}"

  tags = {
    env         = var.env,
    Application = "ptt-backend-service"
  }

  secret_name                   = var.secret_name
  batch_size                    = 500
  major_travel                  = var.major_travel
  anglia_tours                  = var.anglia_tours
  wst_travel                    = var.wst_travel
  nas                           = var.nas
  flypop                        = var.flypop
  broadway                      = var.broadway
  broadway_ej                   = var.broadway_ej
  wlh                           = var.wlh
  iglu                          = var.iglu
  iglu_escrow                   = var.iglu_escrow
  gtl                           = var.gtl
  caledonian                    = var.caledonian
  livescore                     = var.livescore
  sftp_s3_bucket                = "ptt-sftp-${var.env}"
  url                           = "https://${var.env}-api.pttapp.com/api"
  logo_bucket                   = "ptt-logo-${var.env}"
  logo_path                     = "../../../assets/ptt-logo.png"
  reports_bucket                = "ptt-reports-${var.env}"
  iglu_tbr_bucket               = "ptt-iglu-tbr-${var.env}"
  sftp_to_ptt_s3_bucket         = "sftp-to-ptt-${var.env}"
  pennywood                     = var.pennywood
  claim_exceeds_cost_anomaly_id = var.claim_exceeds_cost_anomaly_id
  wlh_new                       = var.wlh_new
  esky                          = var.esky
  balk                          = var.balk
  bluestyle                     = var.bluestyle
  gvh                           = var.gvh
  winter_green                  = var.winter_green
  campings                      = var.campings
  shearings_abtot               = var.shearings_abtot
  mighty_hoopla                 = var.mighty_hoopla
  redis_url                     = var.redis_url
  redis_password                = var.redis_password
  barrhead                      = var.barrhead
  hays                          = var.hays
  sunshine                      = var.sunshine
}

