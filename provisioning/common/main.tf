data "aws_caller_identity" "current" {}

resource "null_resource" "lambda_dependencies" {
  triggers = {
    build_number = timestamp()
  }
  provisioner "local-exec" {
    command     = "./${basename(local.layer_dependencies_build)}"
    working_dir = dirname(local.layer_dependencies_build)
  }
}

data "null_data_source" "lamda_dependencies_zip" {
  depends_on = [null_resource.lambda_dependencies]
  inputs = {
    file_name = local.layer_dependencies_zip
  }
}

resource "aws_lambda_layer_version" "lambda_layer" {
  layer_name          = join("-", ["lambda-${var.env}", "deps-layer"])
  filename            = data.null_data_source.lamda_dependencies_zip.outputs["file_name"]
  source_code_hash    = filemd5(data.null_data_source.lamda_dependencies_zip.outputs["file_name"])
  compatible_runtimes = ["python3.8", "python3.9"]
  depends_on          = [null_resource.lambda_dependencies]
}

data "aws_vpc" "ptt_vpc" {
  cidr_block = var.vpc_cidr
}

data "aws_subnets" "ptt_private_subnets" {
  filter {
    name   = "tag:Name"
    values = ["ptt-private-subnet-${var.env}-1", "ptt-private-subnet-${var.env}-2"]
  }
}

data "aws_subnets" "ptt_public_subnets" {
  filter {
    name   = "tag:Name"
    values = ["ptt-public-subnet-${var.env}-1", "ptt-public-subnet-${var.env}-2"]
  }
}

resource "aws_security_group" "ptt_lambda_sg" {
  name   = local.lambda_sg_name
  vpc_id = data.aws_vpc.ptt_vpc.id

  egress {
    from_port = 0
    to_port   = 0
    protocol  = "-1"
    cidr_blocks = [
    "0.0.0.0/0"]
  }

  tags = {
    Name = local.lambda_sg_name
  }
}

data "aws_cognito_user_pools" "ptt_cognito_user_pool" {
  name = "ptt-user-pool-${var.env}"
}
