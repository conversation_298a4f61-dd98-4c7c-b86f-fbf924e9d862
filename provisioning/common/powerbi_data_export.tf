# PowerBI Data Export Lambda Function and Infrastructure

# S3 bucket for PowerBI exports
resource "aws_s3_bucket" "powerbi_export_bucket" {
  bucket = local.powerbi_export_bucket

  tags = merge(local.tags, {
    Name = local.powerbi_export_bucket
  })
}

resource "aws_s3_bucket_acl" "powerbi_export_bucket_acl" {
  bucket = aws_s3_bucket.powerbi_export_bucket.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "powerbi_export_bucket_block_public" {
  bucket = aws_s3_bucket.powerbi_export_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# IAM role for PowerBI export Lambda
resource "aws_iam_role" "iam_for_lambda_powerbi_export" {
  name = "iam_for_lambda_powerbi_export_${var.env}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = local.tags_powerbi_export_lambda
}

# IAM policy for PowerBI export Lambda
resource "aws_iam_policy" "powerbi_export_lambda_policy" {
  name        = "powerbi_export_lambda_policy_${var.env}"
  description = "IAM policy for PowerBI export lambda"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "${aws_s3_bucket.powerbi_export_bucket.arn}",
          "${aws_s3_bucket.powerbi_export_bucket.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "powerbi_export_lambda_policy_attachment" {
  role       = aws_iam_role.iam_for_lambda_powerbi_export.name
  policy_arn = aws_iam_policy.powerbi_export_lambda_policy.arn
}

# Lambda function zip file
data "archive_file" "powerbi_export_lambda_zip" {
  type        = "zip"
  source_dir  = var.source_dir_powerbi_export_lambda
  output_path = "powerbi-data-export-lambda.zip"
}

# PowerBI export Lambda function
resource "aws_lambda_function" "powerbi_export_lambda" {
  filename         = "powerbi-data-export-lambda.zip"
  source_code_hash = data.archive_file.powerbi_export_lambda_zip.output_base64sha256
  function_name    = local.function_name_powerbi_export_lambda
  timeout          = 900  # 15 minutes for large data exports
  role             = aws_iam_role.iam_for_lambda_powerbi_export.arn
  description      = "PowerBI Data Export Lambda - Exports banking and claims data to Excel"
  handler          = "powerbi-data-export-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 2048  # Increased memory for pandas operations

  layers = [aws_lambda_layer_version.layer_dependencies.arn]

  vpc_config {
    subnet_ids         = data.aws_subnets.private.ids
    security_group_ids = [data.aws_security_group.lambda_sg.id]
  }

  environment {
    variables = {
      ENVIRONMENT           = var.env
      POWERBI_EXPORT_BUCKET = local.powerbi_export_bucket
      DATABASE_URI          = var.database_uri
    }
  }

  tags = merge(local.tags_powerbi_export_lambda, {
    role = "lambda_function"
  })

  depends_on = [
    aws_iam_role_policy_attachment.powerbi_export_lambda_policy_attachment,
    aws_cloudwatch_log_group.powerbi_export_lambda_log_group
  ]
}

# CloudWatch log group for PowerBI export Lambda
resource "aws_cloudwatch_log_group" "powerbi_export_lambda_log_group" {
  name              = "/aws/lambda/${local.function_name_powerbi_export_lambda}"
  retention_in_days = 14

  tags = local.tags_powerbi_export_lambda
}

# EventBridge rule for daily scheduling
resource "aws_cloudwatch_event_rule" "powerbi_export_schedule" {
  name                = "powerbi-export-daily-schedule-${var.env}"
  description         = "Trigger PowerBI export Lambda daily at 6 AM UTC"
  schedule_expression = "cron(0 6 * * ? *)"  # Daily at 6 AM UTC

  tags = local.tags_powerbi_export_lambda
}

# EventBridge target for the Lambda function
resource "aws_cloudwatch_event_target" "powerbi_export_lambda_target" {
  rule      = aws_cloudwatch_event_rule.powerbi_export_schedule.name
  target_id = "PowerBIExportLambdaTarget"
  arn       = aws_lambda_function.powerbi_export_lambda.arn

  input = jsonencode({
    days_back = 30
    bucket    = local.powerbi_export_bucket
  })
}

# Permission for EventBridge to invoke Lambda
resource "aws_lambda_permission" "allow_eventbridge_powerbi_export" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.powerbi_export_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.powerbi_export_schedule.arn
}

# Output the S3 bucket name for reference
output "powerbi_export_bucket_name" {
  description = "Name of the S3 bucket for PowerBI exports"
  value       = aws_s3_bucket.powerbi_export_bucket.bucket
}

output "powerbi_export_lambda_function_name" {
  description = "Name of the PowerBI export Lambda function"
  value       = aws_lambda_function.powerbi_export_lambda.function_name
}
