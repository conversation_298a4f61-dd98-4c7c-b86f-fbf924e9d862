resource "aws_cloudwatch_event_rule" "risk_exposure_insight_event_rule" {
  name                = "crons-rule-for-${local.function_name_risk_exposure_insight_lambda}"
  description         = "Trigger for Risk Exposure Insight Lambda ${var.env}"
  schedule_expression = "cron(0 0 * * ? *)"
}

resource "aws_cloudwatch_event_target" "risk_exposure_insight_event_target" {
  rule      = aws_cloudwatch_event_rule.risk_exposure_insight_event_rule.name
  target_id = "lambda"
  arn       = aws_lambda_function.risk_exposure_insight_lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_risk_exposure_insight" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.risk_exposure_insight_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.risk_exposure_insight_event_rule.arn
}

data "archive_file" "risk_exposure_insight_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_risk_exposure_insight_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "risk-exposure-insight-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_risk_exposure_insight_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_risk_exposure_insight_lambda" {
  name               = join("-", ["iam-for", local.function_name_risk_exposure_insight_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_risk_exposure_insight_lambda.json
}

resource "aws_iam_policy" "iam_policies_risk_exposure_insight_lambda" {
  name   = join("-", ["iam-policies-for-", local.function_name_risk_exposure_insight_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface"
              ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup"
              ],
            "Resource": [
                "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:*"
            ]
        },
        {
		      "Effect": "Allow",
          "Action": [
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
         "Resource": "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${local.function_name_risk_exposure_insight_lambda}:*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "arn:aws:secretsmanager:eu-west-2:${data.aws_caller_identity.current.account_id}:secret:ptt-${var.env}/secrets*"
        }
    ]
}
EOF
}


resource "aws_iam_role_policy_attachment" "iam_policies_attach_risk_exposure_insight_lambda" {
  role       = aws_iam_role.iam_for_lambda_risk_exposure_insight_lambda.name
  policy_arn = aws_iam_policy.iam_policies_risk_exposure_insight_lambda.arn
}


resource "aws_lambda_function" "risk_exposure_insight_lambda" {
  filename         = "risk-exposure-insight-lambda.zip"
  source_code_hash = data.archive_file.risk_exposure_insight_lambda_zip.output_base64sha256
  function_name    = local.function_name_risk_exposure_insight_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_risk_exposure_insight_lambda.arn
  description      = format("%s Lambda", local.function_name_risk_exposure_insight_lambda)
  handler          = "risk-exposure-insight-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_risk_exposure_insight_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      SECRET_NAME = local.secret_name
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
