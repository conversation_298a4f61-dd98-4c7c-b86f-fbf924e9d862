data "aws_route53_zone" "ptt_public_zone" {
  name         = "pttapp.com"
  private_zone = false
}

resource "aws_route53_record" "sftp_domain_r53" {
  count   = var.sftp_enabled ? 1 : 0
  zone_id = data.aws_route53_zone.ptt_public_zone.id
  name    = var.env == "prod" ? "sftp.pttapp.com" : "${var.env}-sftp.pttapp.com"
  type    = "CNAME"
  ttl     = "5"
  records = [aws_transfer_server.sftp_server[0].endpoint]
}
