resource "aws_s3_bucket" "state_machine_payload_bucket" {
  bucket = local.state_machine_payload_bucket

  tags = merge(local.tags, {
    Name = local.state_machine_payload_bucket
  })
}
resource "aws_s3_bucket_acl" "state_machine_payload_bucket_acl" {
  bucket = aws_s3_bucket.state_machine_payload_bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block_public_access_for_state_machine_payload_bucket" {
  bucket = aws_s3_bucket.state_machine_payload_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "sftp-s3-bucket" {
  count  = var.sftp_enabled ? 1 : 0
  bucket = local.sftp_s3_bucket

  tags = merge(local.tags, {
    Name = local.sftp_s3_bucket
  })
}
resource "aws_s3_bucket_policy" "sftp-s3-bucket-policy" {
  count  = var.sftp_enabled ? 1 : 0
  bucket = aws_s3_bucket.sftp-s3-bucket[0].id

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "AllowSftpAccess",
        "Effect" : "Allow",
        "Principal" : {
          "AWS" : aws_iam_role.sftp_role[0].arn
        },
        "Action" : [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject"
        ],
        "Resource" : [
          "${aws_s3_bucket.sftp-s3-bucket[0].arn}",
          "${aws_s3_bucket.sftp-s3-bucket[0].arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_public_access_block" "block-public-access-for-sftp-s3-bucket" {
  count  = var.sftp_enabled ? 1 : 0
  bucket = aws_s3_bucket.sftp-s3-bucket[0].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "aws-lambda-trigger" {
  count  = var.sftp_enabled ? 1 : 0
  bucket = aws_s3_bucket.sftp-s3-bucket[0].id
  lambda_function {
    lambda_function_arn = aws_lambda_function.sftp_lambda[0].arn
    events              = ["s3:ObjectCreated:Put", "s3:ObjectCreated:CompleteMultipartUpload", "s3:ObjectCreated:Post"]

  }
}
resource "aws_lambda_permission" "sftp-lambda-test" {
  count         = var.sftp_enabled ? 1 : 0
  statement_id  = "AllowS3Invoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.sftp_lambda[0].function_name
  principal     = "s3.amazonaws.com"
  source_arn    = "arn:aws:s3:::${aws_s3_bucket.sftp-s3-bucket[0].id}"
}

resource "aws_s3_bucket" "logo-bucket" {
  bucket = local.logo_bucket

  tags = merge(local.tags, {
    Name = local.logo_bucket
  })
}
resource "aws_s3_bucket_acl" "logo-bucket_acl" {
  bucket = aws_s3_bucket.logo-bucket.id
  acl    = "private"
}
resource "aws_s3_bucket_public_access_block" "block-public-access-for-logo-bucket" {
  bucket = aws_s3_bucket.logo-bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_object" "ptt_logo" {
  bucket       = aws_s3_bucket.logo-bucket.id
  key          = "ptt-logo.png"
  acl          = "private"
  source       = local.logo_path
  content_type = "image/png"
  etag         = md5(local.logo_path)

}

resource "aws_s3_bucket" "iglu_tbr_bucket" {
  bucket = local.iglu_tbr_bucket

  tags = merge(local.tags, {
    Name = local.iglu_tbr_bucket
  })
}
resource "aws_s3_bucket_policy" "iglu_tbr_bucket_policy" {
  bucket = aws_s3_bucket.iglu_tbr_bucket.id

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "AllowAccess",
        "Effect" : "Allow",
        "Principal" : {
          "AWS" : aws_iam_role.iglu_tbr_role.arn
        },
        "Action" : [
          "s3:PutObject",
          "s3:GetObject"
        ],
        "Resource" : [
          "${aws_s3_bucket.iglu_tbr_bucket.arn}",
          "${aws_s3_bucket.iglu_tbr_bucket.arn}/*"
        ]
      }
    ]
  })
}
resource "aws_s3_bucket_public_access_block" "block_public_access_for_iglu_tbr_bucket" {
  bucket = aws_s3_bucket.iglu_tbr_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket" "sftp-to-ptt-s3-bucket" {
  count  = var.sftp_to_ptt_enabled ? 1 : 0
  bucket = local.sftp_to_ptt_s3_bucket

  tags = merge(local.tags, {
    Name = local.sftp_to_ptt_s3_bucket
  })
}
resource "aws_s3_bucket_policy" "sftp-to-ptt-s3-bucket-policy" {
  count  = var.sftp_to_ptt_enabled ? 1 : 0
  bucket = aws_s3_bucket.sftp-to-ptt-s3-bucket[0].id

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "AllowSftpAccess",
        "Effect" : "Allow",
        "Principal" : {
          "AWS" : aws_iam_role.iam_for_lambda_sftp_to_ptt_lambda[0].arn
        },
        "Action" : [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject"
        ],
        "Resource" : [
          "${aws_s3_bucket.sftp-to-ptt-s3-bucket[0].arn}",
          "${aws_s3_bucket.sftp-to-ptt-s3-bucket[0].arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_public_access_block" "block-public-access-for-sftp-to-ptt-s3-bucket" {
  count  = var.sftp_to_ptt_enabled ? 1 : 0
  bucket = aws_s3_bucket.sftp-to-ptt-s3-bucket[0].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "aws-lambda-trigger-sftp-to-ptt" {
  count  = var.sftp_to_ptt_enabled ? 1 : 0
  bucket = aws_s3_bucket.sftp-to-ptt-s3-bucket[0].id
  lambda_function {
    lambda_function_arn = aws_lambda_function.sftp_to_ptt_lambda[0].arn
    events              = ["s3:ObjectCreated:Put", "s3:ObjectCreated:CompleteMultipartUpload", "s3:ObjectCreated:Post"]

  }
}
resource "aws_lambda_permission" "sftp-to-ptt-lambda-test" {
  count         = var.sftp_to_ptt_enabled ? 1 : 0
  statement_id  = "AllowS3Invoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.sftp_to_ptt_lambda[0].function_name
  principal     = "s3.amazonaws.com"
  source_arn    = "arn:aws:s3:::${aws_s3_bucket.sftp-to-ptt-s3-bucket[0].id}"
}
