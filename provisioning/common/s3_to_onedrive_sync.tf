resource "aws_cloudwatch_event_rule" "s3_to_onedrive_sync_event_rule" {
  name                = "crons-rule-for-s3-to-onedrive-sync"
  description         = "Trigger for S3 to OneDrive Sync Lambda"
  schedule_expression = "cron(0 3 * * ? *)" # every day at 3am UTC
}

resource "aws_cloudwatch_event_target" "s3_to_onedrive_sync_event_target" {
  rule      = aws_cloudwatch_event_rule.s3_to_onedrive_sync_event_rule.name
  target_id = "lambda"
  arn       = aws_lambda_function.s3_to_onedrive_sync_lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_s3_to_onedrive_sync_lambda" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.s3_to_onedrive_sync_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.s3_to_onedrive_sync_event_rule.arn
} 