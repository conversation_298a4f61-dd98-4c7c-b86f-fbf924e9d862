resource "aws_iam_role" "sftp_role" {
  count = var.sftp_enabled ? 1 : 0
  name  = "${local.sftp_name}-Role"

  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
        "Effect": "Allow",
        "Principal": {
            "Service": "transfer.amazonaws.com"
        },
        "Action": "sts:AssumeRole"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy" "sftp_policy" {
  count = var.sftp_enabled ? 1 : 0
  name  = "${local.sftp_name}-Policy"
  role  = aws_iam_role.sftp_role[0].id

  policy = <<POLICY
{
    "Version": "2012-10-17",
    "Statement": [
        {
        "Sid": "AllowFullAccesstoCloudWatchLogs",
        "Effect": "Allow",
        "Action": [
            "logs:*"
        ],
        "Resource": "*"
        },
        {
			    "Effect": "Allow",
          "Action": [
            "s3:ListBucket",
            "s3:GetBucketLocation"
          ],
         "Resource": [
            "arn:aws:s3:::${local.sftp_s3_bucket}"
          ]
        },
        {
			    "Effect": "Allow",
          "Action": [
            "s3:PutObject",
            "s3:GetObject",
            "s3:DeleteObject",
            "s3:DeleteObjectVersion",
            "s3:GetObjectVersion",
            "s3:GetObjectACL",
            "s3:PutObjectACL"
          ],
         "Resource": [
            "arn:aws:s3:::${local.sftp_s3_bucket}/*"
          ]
        }
    ]
}
POLICY
}

resource "aws_eip" "ptt_eip" {
  count = var.sftp_enabled ? 2 : 0
  vpc   = true
}

resource "aws_security_group" "ptt_sftp_sg" {
  count  = var.sftp_enabled ? 1 : 0
  name   = "ptt-sftp-sg-${var.env}"
  vpc_id = data.aws_vpc.ptt_vpc.id

  ingress {
    from_port = 22
    to_port   = 22
    protocol  = "tcp"
    cidr_blocks = [
    "0.0.0.0/0"]
  }

  tags = local.tags

  lifecycle {
    ignore_changes = [
      # Ignore changes to ingress since it will be controlled manually
      ingress,
    ]
  }
}

resource "aws_transfer_server" "sftp_server" {
  count = var.sftp_enabled ? 1 : 0

  endpoint_type = "VPC"
  endpoint_details {
    address_allocation_ids = aws_eip.ptt_eip[*].id
    subnet_ids             = data.aws_subnets.ptt_public_subnets.ids
    vpc_id                 = data.aws_vpc.ptt_vpc.id
    security_group_ids     = [aws_security_group.ptt_sftp_sg[0].id]
  }

  identity_provider_type = "AWS_LAMBDA"
  function               = aws_lambda_function.sftp_auth_lambda[0].arn
  logging_role           = aws_iam_role.sftp_role[0].arn


  tags = merge(local.tags, {
    Name = local.sftp_name
  })

  lifecycle {
    # Ignore changes to endpoint details since more security groups are added manually
    ignore_changes = [endpoint_details]
  }
}

resource "aws_transfer_tag" "zone_id" {
  count        = var.sftp_enabled ? 1 : 0
  resource_arn = aws_transfer_server.sftp_server[0].arn
  key          = "aws:transfer:route53HostedZoneId"
  value        = "/hostedzone/${data.aws_route53_zone.ptt_public_zone.id}"
}

resource "aws_transfer_tag" "hostname" {
  count        = var.sftp_enabled ? 1 : 0
  resource_arn = aws_transfer_server.sftp_server[0].arn
  key          = "aws:transfer:customHostname"
  value        = aws_route53_record.sftp_domain_r53[0].name
}

resource "aws_lambda_permission" "sftp_lambda_invocation" {
  count         = var.sftp_enabled ? 1 : 0
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.sftp_auth_lambda[0].function_name
  principal     = "transfer.amazonaws.com"
  source_arn    = aws_transfer_server.sftp_server[0].arn
}
