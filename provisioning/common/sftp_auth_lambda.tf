data "archive_file" "sftp_auth_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_sftp_auth_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "sftp-auth-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_sftp_auth_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_sftp_auth_lambda" {
  count              = var.sftp_enabled ? 1 : 0
  name               = join("-", ["iam-for", local.function_name_sftp_auth_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_sftp_auth_lambda.json
}

resource "aws_iam_policy" "basic_iam_policiey_sftp_auth_lambda" {
  count  = var.sftp_enabled ? 1 : 0
  name   = join("-", ["basic-iam-policy-for", local.function_name_sftp_auth_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface"
              ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup"
              ],
            "Resource": [
                "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:*"
            ]
        },
        {
		  "Effect": "Allow",
          "Action": [
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
         "Resource": "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${local.function_name_sftp_auth_lambda}:*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "basic_iam_policy_attachment" {
  count      = var.sftp_enabled ? 1 : 0
  role       = aws_iam_role.iam_for_lambda_sftp_auth_lambda[0].name
  policy_arn = aws_iam_policy.basic_iam_policiey_sftp_auth_lambda[0].arn
}

resource "aws_iam_policy" "secrets_manager_iam_policiy_sftp_auth_lambda" {
  count  = var.sftp_enabled ? 1 : 0
  name   = join("-", ["secrets-manager-iam-policy-for", local.function_name_sftp_auth_lambda, ])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
              ],
            "Resource": [
                "arn:aws:secretsmanager:eu-west-2:${data.aws_caller_identity.current.account_id}:secret:${aws_transfer_server.sftp_server[0].id}/*"
            ]
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "secrets_manager_iam_policy_attachment" {
  count      = var.sftp_enabled ? 1 : 0
  role       = aws_iam_role.iam_for_lambda_sftp_auth_lambda[0].name
  policy_arn = aws_iam_policy.secrets_manager_iam_policiy_sftp_auth_lambda[0].arn
}

resource "aws_lambda_function" "sftp_auth_lambda" {
  count            = var.sftp_enabled ? 1 : 0
  filename         = "sftp-auth-lambda.zip"
  source_code_hash = data.archive_file.sftp_auth_lambda_zip.output_base64sha256
  function_name    = local.function_name_sftp_auth_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_sftp_auth_lambda[0].arn
  description      = format("%s Lambda", local.function_name_sftp_auth_lambda)
  handler          = "sftp-auth-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_sftp_auth_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT          = var.env
      SecretsManagerRegion = "eu-west-2"
    }
  }
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
