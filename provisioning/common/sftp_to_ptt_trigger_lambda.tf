data "archive_file" "sftp_to_ptt_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_sftp_to_ptt_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "sftp-to-ptt-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_sftp_to_ptt_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_sftp_to_ptt_lambda" {
  count              = var.sftp_to_ptt_enabled ? 1 : 0
  name               = join("-", ["iam-for", local.function_name_sftp_to_ptt_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_sftp_to_ptt_lambda.json
}

resource "aws_iam_policy" "iam_policies_sftp_to_ptt_lambda" {
  count  = var.sftp_to_ptt_enabled ? 1 : 0
  name   = join("-", ["iam-policy-for", local.function_name_sftp_to_ptt_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        },
        {
		  "Effect": "Allow",
          "Action": [
            "s3:GetObject",
            "s3:PutObject",
            "s3:ListBucket",
            "s3:GetBucketLocation",
            "s3:DeleteObject",
            "s3:DeleteObjectVersion",
            "s3:GetObjectVersion",
            "s3:GetObjectACL",
            "s3:PutObjectACL"
          ],
         "Resource": [
            "arn:aws:s3:::${local.sftp_to_ptt_s3_bucket}/*"
          ]
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_sftp_to_ptt_lambda" {
  count      = var.sftp_to_ptt_enabled ? 1 : 0
  role       = aws_iam_role.iam_for_lambda_sftp_to_ptt_lambda[0].name
  policy_arn = aws_iam_policy.iam_policies_sftp_to_ptt_lambda[0].arn
}


resource "aws_lambda_function" "sftp_to_ptt_lambda" {
  count            = var.sftp_to_ptt_enabled ? 1 : 0
  filename         = "sftp-to-ptt-lambda.zip"
  source_code_hash = data.archive_file.sftp_to_ptt_lambda_zip.output_base64sha256
  function_name    = local.function_name_sftp_to_ptt_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_sftp_to_ptt_lambda[0].arn
  description      = format("%s Lambda", local.function_name_sftp_to_ptt_lambda)
  handler          = "sftp-to-ptt-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_sftp_to_ptt_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT = var.env
      URL         = local.url
      SECRET_NAME = local.secret_name
      PTT_BUCKET  = local.sftp_to_ptt_s3_bucket
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
