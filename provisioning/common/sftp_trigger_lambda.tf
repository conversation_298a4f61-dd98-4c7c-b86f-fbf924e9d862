data "archive_file" "sftp_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_sftp_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "sftp-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_sftp_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_sftp_lambda" {
  count              = var.sftp_enabled ? 1 : 0
  name               = join("-", ["iam-for", local.function_name_sftp_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_sftp_lambda.json
}

resource "aws_iam_policy" "iam_policies_sftp_lambda" {
  count  = var.sftp_enabled ? 1 : 0
  name   = join("-", ["iam-policy-for", local.function_name_sftp_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream",
                "logs:CreateLogGroup",
                "logs:PutLogEvents",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "*"
        },
        {
		  "Effect": "Allow",
          "Action": [
            "s3:GetObject",
            "s3:PutObject"
          ],
         "Resource": [
            "arn:aws:s3:::${local.sftp_s3_bucket}/*"
          ]
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_sftp_lambda" {
  count      = var.sftp_enabled ? 1 : 0
  role       = aws_iam_role.iam_for_lambda_sftp_lambda[0].name
  policy_arn = aws_iam_policy.iam_policies_sftp_lambda[0].arn
}


resource "aws_lambda_function" "sftp_lambda" {
  count            = var.sftp_enabled ? 1 : 0
  filename         = "sftp-lambda.zip"
  source_code_hash = data.archive_file.sftp_lambda_zip.output_base64sha256
  function_name    = local.function_name_sftp_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_sftp_lambda[0].arn
  description      = format("%s Lambda", local.function_name_sftp_lambda)
  handler          = "sftp-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_sftp_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT = var.env
      URL         = local.url
      SECRET_NAME = local.secret_name
      SFTP_BUCKET = local.sftp_s3_bucket
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}
