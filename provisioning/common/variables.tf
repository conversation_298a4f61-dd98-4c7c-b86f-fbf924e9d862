variable "env" {
}

variable "aws_region" {
  default = "eu-west-2"
}

variable "banking_step_function_name" {
  default = "banking-orchestration"
}

variable "banking_step_function_definition_file" {
  default = "../../../banking-orchestration/orchestrator.tmpl.json"
}

variable "claim_step_function_name" {
  default = "claim-orchestration"
}

variable "claim_step_function_definition_file" {
  default = "../../../claim-orchestration/orchestrator.tmpl.json"
}

variable "banking_status_change_step_function_name" {
  default = "banking-status-change-orchestration"
}

variable "banking_status_change_step_function_definition_file" {
  default = "../../../banking-status-change-orchestration/orchestrator.tmpl.json"
}

variable "claim_status_change_step_function_name" {
  default = "claim-status-change-orchestration"
}

variable "claim_status_change_step_function_definition_file" {
  default = "../../../claim-status-change-orchestration/orchestrator.tmpl.json"
}

variable "function_name_banking_trigger_lambda" {
  default = "banking-trigger-lambda"
}

variable "source_dir_banking_trigger_lambda" {
  default = "../../../banking-trigger-lambda/"
}
variable "function_name_db_setup_lambda" {
  default = "db-setup-lambda"
}

variable "source_dir_db_setup_lambda" {
  default = "../../../db-setup-lambda/"
}

variable "function_name_banking_file_fetch_lambda" {
  default = "banking-file-fetch-lambda"
}

variable "source_dir_banking_file_fetch_lambda" {
  default = "../../../banking-file-fetch-lambda/"
}

variable "function_name_banking_invalidator_lambda" {
  default = "banking-invalidator-lambda"
}

variable "source_dir_banking_invalidator_lambda" {
  default = "../../../banking-invalidator-lambda/"
}

variable "function_name_banking_file_validator_lambda" {
  default = "banking-file-validator-lambda"
}

variable "source_dir_banking_file_validator_lambda" {
  default = "../../../banking-file-validator-lambda/"
}

variable "function_name_banking_persistor_lambda" {
  default = "banking-persistor-lambda"
}

variable "source_dir_banking_persistor_lambda" {
  default = "../../../banking-persistor-lambda/"
}

variable "function_name_banking_aggregator_lambda" {
  default = "banking-aggregator-lambda"
}

variable "function_name_data_transform_lambda" {
  default = "data-transform-lambda"
}

variable "source_dir_banking_aggregator_lambda" {
  default = "../../../banking-aggregator-lambda/"
}

variable "source_dir_data_transform_lambda" {
  default = "../../../data-transform-lambda/"
}

variable "function_name_banking_anomaly_detector_lambda" {
  default = "banking-anomaly-detector-lambda"
}

variable "source_dir_banking_anomaly_detector_lambda" {
  default = "../../../banking-anomaly-detector-lambda/"
}

variable "function_name_banking_status_updator_lambda" {
  default = "banking-status-updator-lambda"
}

variable "source_dir_banking_status_updator_lambda" {
  default = "../../../banking-status-updator-lambda/"
}

variable "function_name_claim_trigger_lambda" {
  default = "claim-trigger-lambda"
}

variable "source_dir_claim_trigger_lambda" {
  default = "../../../claim-trigger-lambda/"
}

variable "function_name_claim_file_fetch_lambda" {
  default = "claim-file-fetch-lambda"
}

variable "source_dir_claim_file_fetch_lambda" {
  default = "../../../claim-file-fetch-lambda/"
}

variable "function_name_claim_invalidator_lambda" {
  default = "claim-invalidator-lambda"
}

variable "source_dir_claim_invalidator_lambda" {
  default = "../../../claim-invalidator-lambda/"
}

variable "function_name_claim_file_generator_lambda" {
  default = "claim-file-generator-lambda"
}

variable "source_dir_claim_file_generator_lambda" {
  default = "../../../claim-file-generator-lambda/"
}

variable "function_name_claim_file_validator_lambda" {
  default = "claim-file-validator-lambda"
}

variable "source_dir_claim_file_validator_lambda" {
  default = "../../../claim-file-validator-lambda/"
}

variable "function_name_claim_persistor_lambda" {
  default = "claim-persistor-lambda"
}

variable "source_dir_claim_persistor_lambda" {
  default = "../../../claim-persistor-lambda/"
}

variable "function_name_claim_aggregator_lambda" {
  default = "claim-aggregator-lambda"
}

variable "source_dir_claim_aggregator_lambda" {
  default = "../../../claim-aggregator-lambda/"
}

variable "function_name_claim_anomaly_detector_lambda" {
  default = "claim-anomaly-detector-lambda"
}

variable "source_dir_claim_anomaly_detector_lambda" {
  default = "../../../claim-anomaly-detector-lambda/"
}

variable "function_name_claim_status_updator_lambda" {
  default = "claim-status-updator-lambda"
}

variable "source_dir_claim_status_updator_lambda" {
  default = "../../../claim-status-updator-lambda/"
}

variable "function_name_balance_finder_lambda" {
  default = "balance-finder-lambda"
}

variable "source_dir_balance_finder_lambda" {
  default = "../../../balance-finder-lambda/"
}

variable "function_name_banking_status_change_trigger_lambda" {
  default = "banking-status-change-trigger-lambda"
}

variable "source_dir_banking_status_change_trigger_lambda" {
  default = "../../../banking-status-change-trigger-lambda/"
}

variable "function_name_claim_status_change_trigger_lambda" {
  default = "claim-status-change-trigger-lambda"
}

variable "source_dir_claim_status_change_trigger_lambda" {
  default = "../../../claim-status-change-trigger-lambda/"
}

variable "function_name_banking_status_change_lambda" {
  default = "banking-status-change-lambda"
}

variable "source_dir_banking_status_change_lambda" {
  default = "../../../banking-status-change-lambda/"
}

variable "function_name_claim_status_change_lambda" {
  default = "claim-status-change-lambda"
}

variable "source_dir_claim_status_change_lambda" {
  default = "../../../claim-status-change-lambda/"
}

variable "function_name_booking_ref_modifier_lambda" {
  default = "booking-ref-modifier-lambda"
}

variable "source_dir_booking_ref_modifier_lambda" {
  default = "../../../booking-ref-modifier-lambda/"
}

variable "function_name_cognito_custom_email_lambda" {
  default = "cognito-custom-email-lambda"
}

variable "source_dir_cognito_custom_email_lambda" {
  default = "../../../cognito-custom-email-lambda/"
}

variable "function_name_sftp_lambda" {
  default = "sftp-lambda"
}

variable "source_dir_sftp_lambda" {
  default = "../../../sftp-lambda/"
}

variable "function_name_sftp_auth_lambda" {
  default = "sftp-auth-lambda"
}

variable "source_dir_sftp_auth_lambda" {
  default = "../../../sftp-auth-lambda/"
}

variable "function_name_exposure_status_lambda" {
  default = "exposure-status-lambda"
}

variable "source_dir_exposure_status_lambda" {
  default = "../../../dashboard/"
}


variable "function_name_risk_exposure_insight_lambda" {
  default = "risk-exposure-insight-lambda"
}

variable "source_dir_risk_exposure_insight_lambda" {
  default = "../../../risk-exposure-insight-lambda/"
}

variable "function_name_banking_claim_summary_file_deletion_lambda" {
  default = "banking-claim-file-deletion-lambda"
}

variable "source_dir_banking_claim_summary_file_deletion_lambda" {
  default = "../../../banking-claim-file-deletion-lambda/"
}

variable "function_name_iglu_escrow_scheduler_lambda" {
  default = "iglu-escrow-scheduler-lambda"
}

variable "source_dir_iglu_escrow_scheduler_lambda" {
  default = "../../../iglu-escrow-scheduler-lambda/"
}

variable "function_name_sftp_to_ptt_lambda" {
  default = "sftp-to-ptt-lambda"
}

variable "source_dir_sftp_to_ptt_lambda" {
  default = "../../../sftp-to-ptt-lambda/"
}

variable "function_name_gtl_tbr_dropping_lambda" {
  default = "gtl-tbr-dropping-lambda"
}

variable "source_dir_gtl_tbr_dropping_lambda" {
  default = "../../../gtl-tbr-dropping-lambda/"
}

variable "function_name_caledonian_tbr_dropping_lambda" {
  default = "caledonian-tbr-dropping-lambda"
}

variable "source_dir_caledonian_tbr_dropping_lambda" {
  default = "../../../caledonian-tbr-dropping-lambda/"
}


variable "function_name_wlh_net_amount_calculator_lambda" {
  default = "wlh-net-amount-calculator-lambda"
}

variable "source_dir_wlh_net_amount_calculator_lambda" {
  default = "../../../wlh-net-amount-calculator-lambda/"
}

variable "subnet_ids" {
  default = ""
}
variable "vpc_cidr" {
}

variable "secret_name" {}

variable "timeout" {
  default = 300
}

variable "anglia_tours" {}
variable "wst_travel" {}
variable "major_travel" {}
variable "nas" {}
variable "flypop" {}
variable "broadway" {}
variable "broadway_ej" {}
variable "wlh" {}
variable "iglu" {}
variable "gtl" {}
variable "caledonian" {}
variable "livescore" {}
variable "iglu_escrow" {}
variable "pennywood" {}
variable "claim_exceeds_cost_anomaly_id" {}
variable "wlh_new" {}
variable "esky" {}
variable "balk" {}
variable "bluestyle" {}
variable "gvh" {}
variable "winter_green" {}
variable "campings" {}
variable "shearings_abtot" {}
variable "mighty_hoopla" {}
variable "sftp_enabled" {
  default = false
}

variable "sftp_to_ptt_enabled" {
  default = false
}

variable "redis_url" {}
variable "redis_password" {}
variable "barrhead" {}
variable "hays" {}
variable "sunshine" {}
