resource "aws_cloudwatch_event_rule" "wlh_net_amount_calculator_evet_rule" {
  name                = "crons-rule-for-${local.function_name_wlh_net_amount_calculator_lambda}"
  description         = "Trigger for WLH net amount calculator lambda ${var.env}"
  schedule_expression = "cron(50 23 L * ? *)"
}

resource "aws_cloudwatch_event_target" "wlh_net_amount_calculator_lambda_event_target" {
  rule      = aws_cloudwatch_event_rule.wlh_net_amount_calculator_evet_rule.name
  target_id = "lambda"
  arn       = aws_lambda_function.wlh_net_amount_calculator_lambda.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_wlh_net_amount_calculator_lambda" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.wlh_net_amount_calculator_lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.wlh_net_amount_calculator_evet_rule.arn
}

data "archive_file" "wlh_net_amount_calculator_lambda_zip" {
  type       = "zip"
  source_dir = dirname(var.source_dir_wlh_net_amount_calculator_lambda)
  excludes = [
    "dist",
    "provisioning",
    ".git",
    "idea",
  ".pytest_cache"]
  output_path = "wlh-net-amount-calculator-lambda.zip"
}

data "aws_iam_policy_document" "aws_lambda_trust_policy_wlh_net_amount_calculator_lambda" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "iam_for_lambda_wlh_net_amount_calculator_lambda" {
  name               = join("-", ["iam-for", local.function_name_wlh_net_amount_calculator_lambda])
  assume_role_policy = data.aws_iam_policy_document.aws_lambda_trust_policy_wlh_net_amount_calculator_lambda.json
}

resource "aws_iam_policy" "iam_policies_wlh_net_amount_calculator_lambda" {
  name   = join("-", ["iam-policy-for", local.function_name_wlh_net_amount_calculator_lambda])
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterface",
                "ec2:DeleteNetworkInterface",
                "ec2:DescribeInstances",
                "ec2:AttachNetworkInterface",
                "s3:GetObject",
                "s3:PutObject"
              ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup"
              ],
            "Resource": [
                "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:*"
            ]
        },
        {
		      "Effect": "Allow",
          "Action": [
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
         "Resource": "arn:aws:logs:eu-west-2:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${local.function_name_wlh_net_amount_calculator_lambda}:*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
              ],
            "Resource": "arn:aws:secretsmanager:eu-west-2:${data.aws_caller_identity.current.account_id}:secret:ptt-${var.env}/secrets*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "iam_policies_attach_wlh_net_amount_calculator_lambda" {
  role       = aws_iam_role.iam_for_lambda_wlh_net_amount_calculator_lambda.name
  policy_arn = aws_iam_policy.iam_policies_wlh_net_amount_calculator_lambda.arn
}


resource "aws_lambda_function" "wlh_net_amount_calculator_lambda" {
  filename         = "wlh-net-amount-calculator-lambda.zip"
  source_code_hash = data.archive_file.wlh_net_amount_calculator_lambda_zip.output_base64sha256
  function_name    = local.function_name_wlh_net_amount_calculator_lambda
  timeout          = var.timeout
  role             = aws_iam_role.iam_for_lambda_wlh_net_amount_calculator_lambda.arn
  description      = format("%s Lambda", local.function_name_wlh_net_amount_calculator_lambda)
  handler          = "wlh-net-amount-calculator-lambda.lambda_handler"
  runtime          = "python3.9"
  memory_size      = 1024
  tags = merge(local.tags_wlh_net_amount_calculator_lambda, {
    role = "lambda_function"
  })
  environment {
    variables = {
      ENVIRONMENT = var.env
      SECRET_NAME = local.secret_name
      URL         = local.url
      WLH_NEW     = local.wlh_new
    }
  }
  layers = [
    aws_lambda_layer_version.lambda_layer.arn
  ]
  vpc_config {
    subnet_ids         = data.aws_subnets.ptt_private_subnets.ids
    security_group_ids = [aws_security_group.ptt_lambda_sg.id]
  }
}

resource "aws_iam_role" "wlh_net_role" {
  name = "${local.wlh_name}-Role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "wlh_net_policy" {
  name = "${local.wlh_name}-Policy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:PutObject",
          "s3:GetObject",
        ],
        Effect = "Allow",
        Resource = [
          "${aws_s3_bucket.iglu_tbr_bucket.arn}",
          "${aws_s3_bucket.iglu_tbr_bucket.arn}/*",
        ],
      },
    ],
  })
}

resource "aws_iam_policy_attachment" "wlh_net_role_attachment" {
  name       = "${local.wlh_name}_role_attachment"
  roles      = [aws_iam_role.wlh_net_role.name]
  policy_arn = aws_iam_policy.wlh_net_policy.arn
}
