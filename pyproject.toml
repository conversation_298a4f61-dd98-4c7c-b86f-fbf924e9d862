[tool.poetry]
name = "ptt-async-workflows"
version = "0.1.0"
description = ""
authors = ["Nithin <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.8"
boto3 = "^1.20.46"
sheet2dict = "^0.1.1"
pymongo = "^4.0.1"
dnspython = "^2.2.1"
XlsxWriter = "^3.0.3"
redis = "^5.2.1"
setuptools = "^65.5.1"
fakeredis = "^2.28.1"

[tool.poetry.dev-dependencies]
black = "^22.1.0"
flake8 = "^4.0.1"
pre-commit = "^2.17.0"
mongomock = "^4.0.0"
moto = "^3.0.5"
pytest = "^7.0.1"
freezegun = "^1.2.0"
PyHamcrest = "^2.0.3"
pytest-coverage = "^0.0"
pytest-env = "^0.6.2"
requests-mock = "^1.10.0"

[tool.coverage.run]
omit = [
    "tests/*",
    ]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
