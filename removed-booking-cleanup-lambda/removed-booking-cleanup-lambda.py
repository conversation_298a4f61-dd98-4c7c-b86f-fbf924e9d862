import os
import boto3
from datetime import datetime, date
from pymongo import MongoClient


def lambda_handler(event, context):
    """
    Clean up expired removed booking references where return_date has passed.
    This function should be scheduled to run daily.
    """
    
    # MongoDB connection
    client = MongoClient(os.environ["MONGODB_URL"])
    db = client[os.environ["DATABASE_NAME"]]
    
    today = date.today().strftime("%Y-%m-%d")
    
    print(f"Starting cleanup of expired removed booking references. Today: {today}")
    
    try:
        # Find expired removed booking references
        expired_bookings = list(
            db.removed_booking_refs.find(
                {
                    "expired": False,
                    "return_date": {"$lt": today, "$ne": None, "$exists": True}
                },
                projection={
                    "_id": 1,
                    "client_id": 1,
                    "booking_ref": 1,
                    "return_date": 1,
                    "file_id": 1
                }
            )
        )
        
        if not expired_bookings:
            print("No expired removed booking references found.")
            return {"statusCode": 200, "cleanedCount": 0}
        
        print(f"Found {len(expired_bookings)} expired removed booking references to clean up.")
        
        # Mark as expired instead of deleting for audit purposes
        result = db.removed_booking_refs.update_many(
            {
                "expired": False,
                "return_date": {"$lt": today, "$ne": None, "$exists": True}
            },
            {
                "$set": {
                    "expired": True,
                    "expired_at": datetime.utcnow()
                }
            }
        )
        
        print(f"Successfully marked {result.modified_count} removed booking references as expired.")
        
        # Log details of cleaned bookings
        for booking in expired_bookings:
            print(f"Marked as expired: Client {booking['client_id']}, "
                  f"Booking {booking['booking_ref']}, "
                  f"Return Date: {booking['return_date']}, "
                  f"File: {booking['file_id']}")
        
        # Optional: Actually delete expired entries older than X months (for cleanup)
        # This keeps audit trail for recent deletions but cleans up old data
        cutoff_date = datetime.now().replace(month=datetime.now().month - 6).strftime("%Y-%m-%d")
        
        deletion_result = db.removed_booking_refs.delete_many(
            {
                "expired": True,
                "return_date": {"$lt": cutoff_date, "$ne": None, "$exists": True}
            }
        )
        
        print(f"Deleted {deletion_result.deleted_count} old expired records (older than 6 months).")
        
        return {
            "statusCode": 200,
            "markedExpired": result.modified_count,
            "deletedOldRecords": deletion_result.deleted_count,
            "totalProcessed": len(expired_bookings)
        }
        
    except Exception as e:
        print(f"Error during cleanup: {str(e)}")
        return {
            "statusCode": 500,
            "error": str(e)
        }
    
    finally:
        client.close() 