import os

from pymongo import MongoClient, ReadPreference, read_concern
from datetime import datetime, timedelta

from helpers.secret_manager_handler import get_secret

client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()


def lambda_handler(event, context):
    date_today = datetime.today().strftime("%Y-%m-%d")
    date_yesterday = datetime.today() - timedelta(days=1)
    date = datetime.combine(date_yesterday, datetime.min.time())
    match_condition = {
        "deleted": False,
        "return_date": {
            "$gte": date_today,
        },
    }
    if event.get("userId"):
        date = datetime.combine(datetime.today(), datetime.min.time())
        users = db.user.find_one({"user_id": event["userId"]}, projection={"clients": 1, "_id": 0})
        match_condition.update({"client_id": {"$in": users["clients"]}})

    data = list(
        db.claims_file_details.aggregate(
            [
                {"$match": match_condition},
                {
                    "$group": {
                        "_id": {"client_id": "$client_id", "currency_code": "$currency_code"},
                        "claim_amount": {"$sum": "$amount"},
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "client_id": "$_id.client_id",
                        "currency_code": "$_id.currency_code",
                        "claim_amount": "$claim_amount",
                        "date": date,
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                    }
                },
            ],
        )
    )
    with client.start_session() as session:
        with session.start_transaction(
            read_preference=ReadPreference.PRIMARY, read_concern=read_concern.ReadConcern(level="snapshot")
        ):
            db.dashboard_risk_exposure.delete_many(
                {"date": date, "client_id": {"$in": users["clients"]}} if event.get("userId") else {"date": date},
                session=session,
            )
            if data:
                db.dashboard_risk_exposure.insert_many(
                    data,
                    session=session,
                )
