# S3 to OneDrive Sync Script

This script reliably syncs large Excel/CSV files from AWS S3 to OneDrive using the Microsoft Graph API with chunked upload and atomic replacement. It is designed for use cases where Power BI needs to consume large, daily-updated files from S3 via OneDrive/SharePoint.

## Features
- Handles very large files (hundreds of MBs to many GBs)
- Chunked upload with retry logic
- Atomic replacement: Power BI only sees a fully uploaded file
- Can be scheduled (cron, Task Scheduler, Lambda, etc.)
- Python-based, easy to maintain

## How It Works
1. Downloads the latest file from S3
2. Uploads to OneDrive using a temporary filename via chunked upload
3. Renames/moves the file to the final name only after a successful upload

## Configuration
Set the following variables in the script or as environment variables:
- `S3_BUCKET`: Name of your S3 bucket
- `S3_KEY`: Key (path) to the file in S3
- `CLIENT_ID`, `CLIENT_SECRET`, `TENANT_ID`, `REFRESH_TOKEN`: Azure app registration credentials
- `ONEDRIVE_FOLDER`: Path in OneDrive (e.g., `/Documents/PowerBI/`)
- `ONEDRIVE_FILENAME`: Final filename in OneDrive
- `TEMP_FILENAME`: Temporary filename for atomic replacement

## Prerequisites
- Python 3.7+
- Install dependencies:
  ```sh
  pip install boto3 requests
  ```
- Azure app registration with Microsoft Graph API permissions (`Files.ReadWrite.All`)
- AWS credentials with access to the S3 bucket

## Usage
1. Update configuration variables as needed.
2. Run the script:
   ```sh
   python s3_to_onedrive.py
   ```
3. Schedule the script as needed (e.g., cron, Task Scheduler, Lambda, etc.)

## Scheduling Example (cron)
To run every day at 3am:
```
0 3 * * * /usr/bin/python3 /path/to/s3_to_onedrive.py
```

## Error Handling
- The script retries failed chunk uploads up to 5 times.
- If the upload fails, the previous file remains in OneDrive (no partial/corrupt files).
- All progress and errors are printed to the console for monitoring.

## Security
- Store credentials securely (use environment variables or a secrets manager).
- Do not hardcode secrets in production.

## For Help
- See Microsoft Graph API docs: https://learn.microsoft.com/en-us/graph/api/resources/onedrive?view=graph-rest-1.0
- See AWS boto3 docs: https://boto3.amazonaws.com/v1/documentation/api/latest/index.html

## Deploying as an AWS Lambda Function

1. **Package the Code:**
   - Include `s3_to_onedrive.py`, `lambda_handler.py`, and dependencies (use a Lambda Layer for `boto3`, `requests` if needed).
2. **Create the Lambda Function:**
   - Set the handler to `lambda_handler.lambda_handler`.
   - Set environment variables for all configuration (see above).
3. **Automate with EventBridge:**
   - Use the provided Terraform (`provisioning/common/s3_to_onedrive_sync.tf`) to schedule the Lambda (e.g., daily at 3am UTC).
   - Deploy with your standard Terraform workflow.
4. **Monitor:**
   - Check CloudWatch Logs for success/failure.

## Environment Variables
Set these in the Lambda configuration:
- `S3_BUCKET`, `S3_KEY`
- `CLIENT_ID`, `CLIENT_SECRET`, `TENANT_ID`, `REFRESH_TOKEN`
- `ONEDRIVE_FOLDER`, `ONEDRIVE_FILENAME`, `TEMP_FILENAME`

## Local Testing
You can run the script locally for testing before deploying to Lambda:
```sh
python s3_to_onedrive.py
```

---

For questions or improvements, contact the data engineering team. 