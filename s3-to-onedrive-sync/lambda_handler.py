import s3_to_onedrive

def lambda_handler(event, context):
    s3_to_onedrive.download_from_s3()
    token = s3_to_onedrive.get_access_token()
    upload_url = s3_to_onedrive.create_upload_session(token, s3_to_onedrive.TEMP_FILENAME)
    s3_to_onedrive.upload_large_file(upload_url)
    s3_to_onedrive.move_file(token, s3_to_onedrive.TEMP_FILENAME, s3_to_onedrive.ONEDRIVE_FILENAME)
    return {"statusCode": 200, "body": "S3 to OneDrive sync completed."} 