import os
import boto3
import requests
import time

# --- CONFIGURATION ---
S3_BUCKET = os.environ.get('S3_BUCKET', 'your-s3-bucket')
S3_KEY = os.environ.get('S3_KEY', 'banking_claims_latest.xlsx')
LOCAL_FILE = '/tmp/banking_claims_latest.xlsx'

CLIENT_ID = os.environ.get('CLIENT_ID', 'your-azure-client-id')
CLIENT_SECRET = os.environ.get('CLIENT_SECRET', 'your-azure-client-secret')
TENANT_ID = os.environ.get('TENANT_ID', 'your-tenant-id')
REFRESH_TOKEN = os.environ.get('REFRESH_TOKEN', 'your-refresh-token')
ONEDRIVE_FOLDER = os.environ.get('ONEDRIVE_FOLDER', '/Documents/PowerBI/')
ONEDRIVE_FILENAME = os.environ.get('ONEDRIVE_FILENAME', 'banking_claims_latest.xlsx')
TEMP_FILENAME = os.environ.get('TEMP_FILENAME', 'banking_claims_latest_temp.xlsx')
CHUNK_SIZE = 3276800  # 3.2 MB per chunk
MAX_RETRIES = 5

def download_from_s3():
    s3 = boto3.client('s3')
    s3.download_file(S3_BUCKET, S3_KEY, LOCAL_FILE)
    print(f"Downloaded {S3_KEY} from S3 to {LOCAL_FILE}")

def get_access_token():
    url = f"https://login.microsoftonline.com/{TENANT_ID}/oauth2/v2.0/token"
    data = {
        'client_id': CLIENT_ID,
        'scope': 'https://graph.microsoft.com/.default offline_access',
        'client_secret': CLIENT_SECRET,
        'grant_type': 'refresh_token',
        'refresh_token': REFRESH_TOKEN,
    }
    response = requests.post(url, data=data)
    response.raise_for_status()
    return response.json()['access_token']

def create_upload_session(access_token, filename):
    url = f"https://graph.microsoft.com/v1.0/me/drive/root:{ONEDRIVE_FOLDER}{filename}:/createUploadSession"
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    resp = requests.post(url, headers=headers, json={})
    resp.raise_for_status()
    return resp.json()['uploadUrl']

def upload_large_file(upload_url):
    file_size = os.path.getsize(LOCAL_FILE)
    with open(LOCAL_FILE, 'rb') as f:
        chunk_number = 0
        for chunk_start in range(0, file_size, CHUNK_SIZE):
            chunk_end = min(chunk_start + CHUNK_SIZE, file_size) - 1
            chunk_length = chunk_end - chunk_start + 1
            f.seek(chunk_start)
            chunk_data = f.read(chunk_length)
            headers = {
                'Content-Range': f'bytes {chunk_start}-{chunk_end}/{file_size}',
                'Content-Length': str(chunk_length)
            }
            for attempt in range(MAX_RETRIES):
                resp = requests.put(upload_url, headers=headers, data=chunk_data)
                if resp.status_code in (200, 201, 202):
                    print(f"Uploaded chunk {chunk_number} ({chunk_length} bytes)")
                    break
                else:
                    print(f"Chunk {chunk_number} failed (attempt {attempt+1}): {resp.text}")
                    time.sleep(2 ** attempt)
            else:
                raise Exception(f"Failed to upload chunk {chunk_number} after {MAX_RETRIES} attempts")
            chunk_number += 1
    print("Upload complete.")

def move_file(access_token, temp_filename, final_filename):
    # Move/rename the temp file to the final filename (atomic replacement)
    url = f"https://graph.microsoft.com/v1.0/me/drive/root:{ONEDRIVE_FOLDER}{temp_filename}"
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    data = {"name": final_filename, "parentReference": {"path": f"/drive/root:{ONEDRIVE_FOLDER}"}}
    resp = requests.patch(url, headers=headers, json=data)
    resp.raise_for_status()
    print(f"Renamed {temp_filename} to {final_filename}")

if __name__ == "__main__":
    download_from_s3()
    token = get_access_token()
    upload_url = create_upload_session(token, TEMP_FILENAME)
    upload_large_file(upload_url)
    move_file(token, TEMP_FILENAME, ONEDRIVE_FILENAME) 