import os
import boto3
import urllib
import re
import requests
import tempfile
from helpers.secret_manager_handler import get_secret
import logging
from pymongo import MongoClient
from datetime import datetime

url = os.environ["URL"]
TEMP_DIR = tempfile.gettempdir()
if logging.getLogger().hasHandlers():
    logging.getLogger().setLevel(logging.INFO)
else:
    logging.basicConfig(level=logging.INFO)
details = get_secret("eu-west-2", True)
user_name, password, mongo_db = details
client = MongoClient(os.environ.get("DATABASE_URI") or mongo_db)
db = client.get_database()


def error_handler(s3_client, folder_location, file_name, err_msg):
    time_stamp = datetime.now()
    error_file = open(f"{TEMP_DIR}/{file_name}.txt", "a")
    error_file.write(f"{file_name} {err_msg} {time_stamp}" "\n")
    error_file.close()
    file_id = f"{folder_location[1:]}errors/{file_name}.txt"
    s3_client.upload_file(f"{TEMP_DIR}/{file_name}.txt", os.environ["PTT_BUCKET"], file_id)
    logging.info("Error message file uploaded to s3")


def lambda_handler(event, context):
    logging.info("triggered ptt ftps lambda")
    s3 = boto3.client("s3")
    event["Records"][0]["s3"]["bucket"]["name"]
    key = event["Records"][0]["s3"]["object"]["key"]
    key = urllib.parse.unquote_plus(key, encoding="utf-8")
    logging.info("user logging in")
    user_auth_token = requests.post(f"{url}/login", json={"username": user_name, "password": password}).json()
    token = user_auth_token["authenticationResult"]["AccessToken"]
    logging.info("returned auth_token")

    if "/" not in key:
        file_name = key
        folder_location = "/"
    else:
        file_name_split_list = key.rsplit("/", 1)
        file_name = file_name_split_list[-1]
        folder_location = f"/{file_name_split_list[0]}/"
    token_headers = {"Authorization": f"Bearer {token}"}
    logging.info("started downloading file from s3 and saving to temp directory")
    s3.download_file(os.environ["PTT_BUCKET"], key, f"{TEMP_DIR}/{file_name}")
    logging.info("file downloaded and saved")

    logging.info("checking for the file format")
    file_format = file_name.rsplit(".", 1)[-1]
    if file_format == "xls":
        file_type = "application/vnd.ms-excel"
    elif file_format == "xlsx":
        file_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    elif file_format == "csv":
        file_type = "text/csv"
    else:
        requests.get(f"{url}/logout", headers=token_headers)
        raise Exception("File format not supported, use xls, xlsx or csv files")

    logging.info("checking for the client with the given folder location")
    basic_info = db.client_basic_info.find_one({"sftp_location": folder_location})
    if basic_info:
        client_id = str(basic_info["_id"])
    else:
        requests.get(f"{url}/logout", headers=token_headers)
        raise Exception("client not found for the given folder location")
    logging.info("checking for the type of file - banking/Claim")
    if re.match(r"\d{8}[-].*Banking", file_name, re.IGNORECASE):
        logging.info("started to upload banking file")
        upload_presigned_url = f"{url}/banking/upload/presigned-url"
        params = {"clientId": client_id, "fileName": file_name}
        upload_url = f"{url}/banking/upload"
        payload = {"clientId": client_id, "fileName": file_name, "sftp": True, "sftpKey": key}

    elif re.match(r"\d{8}[-].*Claim", file_name, re.IGNORECASE):
        logging.info("started to upload claim file")
        upload_presigned_url = f"{url}/claim/upload/presigned-url"
        params = {"clientId": client_id, "fileName": file_name, "claimFromTBR": False}
        upload_url = f"{url}/claim/upload"
        payload = {"clientId": client_id, "fileName": file_name, "claimFromTBR": False, "sftp": True, "sftpKey": key}

    elif re.match(r"\d{8}.*TrustBalanceReport", file_name, re.IGNORECASE):
        upload_presigned_url = f"{url}/claim/upload/presigned-url"
        params = {"clientId": client_id, "fileName": file_name, "claimFromTBR": True}
        upload_url = f"{url}/claim/upload"
        payload = {"clientId": client_id, "fileName": file_name, "claimFromTBR": True, "sftp": True, "sftpKey": key}
    else:
        requests.get(f"{url}/logout", headers=token_headers)
        raise Exception("Invalid file name")

    presigned_details = requests.get(
        upload_presigned_url,
        headers=token_headers,
        params={**params, "claimFromTBR": "true" if params["claimFromTBR"] else "false"}
        if "claimFromTBR" in params
        else params,
    )
    if presigned_details.status_code == 200:
        presigned_json = presigned_details.json()
        presigned_url = presigned_json["presignedUrl"].strip('"')
        payload.update({"fileId": presigned_json["fileId"]})
        with open(f"{TEMP_DIR}/{file_name}", "rb") as file_obj:
            data = file_obj.read()
        requests.put(presigned_url, data=data, headers={"Content-Type": file_type})
        response = requests.post(
            upload_url,
            headers=token_headers,
            json=payload,
        )
        if response.status_code == 201:
            logging.info("file uploaded successfully")
        else:
            error_handler(s3, folder_location, file_name, response.json())
            requests.get(f"{url}/logout", headers=token_headers)
            raise Exception("file upload failed")
    else:
        error_handler(s3, folder_location, file_name, presigned_details.json())
        requests.get(f"{url}/logout", headers=token_headers)
        raise Exception("failed to get presigned url")

    requests.get(f"{url}/logout", headers=token_headers)
    logging.info("user logged out")
