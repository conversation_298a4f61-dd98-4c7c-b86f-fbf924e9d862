import os
import pytest
from moto import mock_s3
import boto3


def pytest_generate_tests():
    os.environ["ENVIRONMENT"] = "dev"
    os.environ["STATE_MACHINE_PAYLOAD_BUCKET"] = f"ptt-state-machine-payload-{os.environ['ENVIRONMENT']}"
    os.environ["MAJOR_TRAVEL"] = "621c4699b29069e5c622ca88"
    os.environ["CLAIM_FILE_BUCKET"] = f"ptt-claim-files-{os.environ['ENVIRONMENT']}"
    os.environ["BANKING_FILE_BUCKET"] = f"ptt-banking-files-{os.environ['ENVIRONMENT']}"
    os.environ["WLH"] = "62cc1b5116133e88d93dba5c"
    os.environ["IGLU"] = "32cc1b5116144e88d93dba50"
    os.environ["LIVESCORE"] = "648197a398f2eb3f34e1553a"
    os.environ["GTL"] = "12cc1b5016143e88d93dya59"
    os.environ["CALEDONIAN"] = "62cc1b5116133e88d93dba4c"
    os.environ["URL"] = "https://test-api.pttapp.com/api"
    os.environ["SFTP_BUCKET"] = "test_bucket"
    os.environ["SECRET_NAME"] = "test/secrets"
    os.environ["PRESIGNED_URL"] = "https://test-banking-files-dev.s3.amazonaws.com/123"
    os.environ["CLIENT_IDS"] = "62ce5983bff9a1862ca13424"
    os.environ["WLH_NEW"] = "34cc1b5116133e88d93dba90"
    os.environ["ESKY"] = "34cc1b5116133e88d93dba91"
    os.environ["CLAIM_EXCEEDS_COST_ANOMALY_ID"] = "634f7b525318711ce6ce8eb3"
    os.environ["BLUE_STYLE"] = "667ce73400f9ff77b2549041"
    os.environ["GVH"] = "67165592ff6ea4450b8a1d24"


@pytest.fixture
def s3_client():
    with mock_s3():
        conn = boto3.client("s3")
        yield conn
