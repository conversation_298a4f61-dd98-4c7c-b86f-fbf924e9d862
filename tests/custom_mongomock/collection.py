# flake8: noqa
import math
from mongomock.collection import (
    Collection,
    validate_is_mapping,
    validate_is_mutable_mapping,
    validate_write_concern_params,
    _updaters,
    _set_updater,
    _current_date_updater,
)

try:
    from collections.abc import Mapping
except ImportError:
    from collections import Mapping
import copy
import itertools
from packaging import version
import warnings

try:
    from bson import BSON
except ImportError:
    json_utils = SON = BSON = None

try:
    from pymongo import ReturnDocument
except ImportError:

    class IndexModel(object):
        pass

    class ReturnDocument(object):
        BEFORE = False
        AFTER = True


from sentinels import NOTHING
from six import iteritems
from six import raise_from
from six import string_types
from six import text_type

from mongomock import DuplicateKeyError, BulkWriteError
from mongomock import filtering
from mongomock.filtering import filter_applies
from mongomock import helpers
from mongomock.not_implemented import raise_for_feature as raise_not_implemented
from mongomock import ObjectId
from mongomock import OperationFailure
from mongomock import WriteError


class CustomCollection(Collection):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _insert(self, data, session=None, ordered=True):
        if not isinstance(data, Mapping):
            results = []
            write_errors = []
            num_inserted = 0
            for index, item in enumerate(data):
                try:
                    results.append(self._insert(item))
                except WriteError as error:
                    write_errors.append(
                        {
                            "index": index,
                            "code": error.code,
                            "errmsg": str(error),
                            "op": item,
                        }
                    )
                    if ordered:
                        break
                    else:
                        continue
                num_inserted += 1
            if write_errors:
                raise BulkWriteError(
                    {
                        "writeErrors": write_errors,
                        "nInserted": num_inserted,
                    }
                )
            return results

        if not all(isinstance(k, string_types) for k in data):
            raise ValueError("Document keys must be strings")

        if BSON:
            # bson validation
            BSON.encode(data, check_keys=True)

        # Like pymongo, we should fill the _id in the inserted dict (odd behavior,
        # but we need to stick to it), so we must patch in-place the data dict
        if "_id" not in data:
            data["_id"] = ObjectId()

        object_id = data["_id"]
        if isinstance(object_id, dict):
            object_id = helpers.hashdict(object_id)
        if object_id in self._store:
            raise DuplicateKeyError("E11000 Duplicate Key Error", 11000)

        data = helpers.patch_datetime_awareness_in_document(data)

        self._store[object_id] = data
        try:
            self._ensure_uniques(data)
        except DuplicateKeyError:
            # Rollback
            del self._store[object_id]
            raise
        return data["_id"]

    def _update(
        self,
        spec,
        document,
        upsert=False,
        manipulate=False,
        multi=False,
        check_keys=False,
        hint=None,
        session=None,
        **kwargs
    ):
        if hint:
            raise NotImplementedError(
                "The hint argument of update is valid but has not been implemented in " "mongomock yet"
            )

        spec = helpers.patch_datetime_awareness_in_document(spec)
        document = helpers.patch_datetime_awareness_in_document(document)
        validate_is_mapping("spec", spec)
        validate_is_mapping("document", document)
        for operator in _updaters:
            if not document.get(operator, True):
                raise WriteError(
                    "'%s' is empty. You must specify a field like so: {%s: {<field>: ...}}" % (operator, operator),
                )

        updated_existing = False
        upserted_id = None
        num_updated = 0
        num_matched = 0
        for existing_document in itertools.chain(self._iter_documents(spec), [None]):
            # we need was_insert for the setOnInsert update operation
            was_insert = False
            # the sentinel document means we should do an upsert
            if existing_document is None:
                if not upsert or num_matched:
                    continue
                # For upsert operation we have first to create a fake existing_document,
                # update it like a regular one, then finally insert it
                if spec.get("_id") is not None:
                    _id = spec["_id"]
                elif document.get("_id") is not None:
                    _id = document["_id"]
                else:
                    _id = ObjectId()
                to_insert = dict(spec, _id=_id)
                to_insert = self._expand_dots(to_insert)
                to_insert, _ = self._discard_operators(to_insert)
                existing_document = to_insert
                was_insert = True
            else:
                original_document_snapshot = copy.deepcopy(existing_document)
                updated_existing = True
            num_matched += 1
            first = True
            subdocument = None
            for k, v in iteritems(document):
                if k in _updaters:
                    updater = _updaters[k]
                    subdocument = self._update_document_fields_with_positional_awareness(
                        existing_document, v, spec, updater, subdocument
                    )

                elif k == "$rename":
                    for src, dst in iteritems(v):
                        if "." in src or "." in dst:
                            raise NotImplementedError(
                                "Using the $rename operator with dots is a valid MongoDB "
                                "operation, but it is not yet supported by mongomock"
                            )
                        if self._has_key(existing_document, src):
                            existing_document[dst] = existing_document.pop(src)

                elif k == "$setOnInsert":
                    if not was_insert:
                        continue
                    subdocument = self._update_document_fields_with_positional_awareness(
                        existing_document, v, spec, _set_updater, subdocument
                    )

                elif k == "$currentDate":
                    subdocument = self._update_document_fields_with_positional_awareness(
                        existing_document, v, spec, _current_date_updater, subdocument
                    )

                elif k == "$addToSet":
                    for field, value in iteritems(v):
                        nested_field_list = field.rsplit(".")
                        if len(nested_field_list) == 1:
                            if field not in existing_document:
                                existing_document[field] = []
                            # document should be a list append to it
                            if isinstance(value, dict):
                                if "$each" in value:
                                    # append the list to the field
                                    existing_document[field] += [
                                        obj for obj in list(value["$each"]) if obj not in existing_document[field]
                                    ]
                                    continue
                            if value not in existing_document[field]:
                                existing_document[field].append(value)
                            continue
                        # push to array in a nested attribute
                        else:
                            # create nested attributes if they do not exist
                            subdocument = existing_document
                            for field_part in nested_field_list[:-1]:
                                if field_part not in subdocument:
                                    subdocument[field_part] = {}

                                subdocument = subdocument[field_part]

                            # we're pushing a list
                            push_results = []
                            if nested_field_list[-1] in subdocument:
                                # if the list exists, then use that list
                                push_results = subdocument[nested_field_list[-1]]

                            if isinstance(value, dict) and "$each" in value:
                                push_results += [obj for obj in list(value["$each"]) if obj not in push_results]
                            elif value not in push_results:
                                push_results.append(value)

                            subdocument[nested_field_list[-1]] = push_results
                elif k == "$pull":
                    for field, value in iteritems(v):
                        nested_field_list = field.rsplit(".")
                        # nested fields includes a positional element
                        # need to find that element
                        if "$" in nested_field_list:
                            if not subdocument:
                                subdocument, _ = self._get_subdocument(existing_document, spec, nested_field_list)

                            # value should be a dictionary since we're pulling
                            pull_results = []
                            # and the last subdoc should be an array
                            for obj in subdocument[nested_field_list[-1]]:
                                if isinstance(obj, dict):
                                    for pull_key, pull_value in iteritems(value):
                                        if obj[pull_key] != pull_value:
                                            pull_results.append(obj)
                                    continue
                                if obj != value:
                                    pull_results.append(obj)

                            # cannot write to doc directly as it doesn't save to
                            # existing_document
                            subdocument[nested_field_list[-1]] = pull_results
                        else:
                            arr = existing_document
                            for field_part in nested_field_list:
                                if field_part not in arr:
                                    break
                                arr = arr[field_part]
                            if not isinstance(arr, list):
                                continue

                            arr_copy = copy.deepcopy(arr)
                            if isinstance(value, dict):
                                for obj in arr_copy:
                                    try:
                                        is_matching = filter_applies(value, obj)
                                    except OperationFailure:
                                        is_matching = False
                                    if is_matching:
                                        arr.remove(obj)
                                        continue

                                    if filter_applies({"field": value}, {"field": obj}):
                                        arr.remove(obj)
                            else:
                                for obj in arr_copy:
                                    if value == obj:
                                        arr.remove(obj)
                elif k == "$pullAll":
                    for field, value in iteritems(v):
                        nested_field_list = field.rsplit(".")
                        if len(nested_field_list) == 1:
                            if field in existing_document:
                                arr = existing_document[field]
                                existing_document[field] = [obj for obj in arr if obj not in value]
                            continue
                        else:
                            subdocument = existing_document
                            for nested_field in nested_field_list[:-1]:
                                if nested_field not in subdocument:
                                    break
                                subdocument = subdocument[nested_field]

                            if nested_field_list[-1] in subdocument:
                                arr = subdocument[nested_field_list[-1]]
                                subdocument[nested_field_list[-1]] = [obj for obj in arr if obj not in value]
                elif k == "$push":
                    for field, value in iteritems(v):
                        # Find the place where to push.
                        nested_field_list = field.rsplit(".")
                        subdocument, field = self._get_subdocument(existing_document, spec, nested_field_list)

                        # Push the new element or elements.
                        if isinstance(subdocument, dict) and field not in subdocument:
                            subdocument[field] = []
                        push_results = subdocument[field]
                        if isinstance(value, dict) and "$each" in value:
                            if "$position" in value:
                                push_results = (
                                    push_results[0 : value["$position"]]
                                    + list(value["$each"])
                                    + push_results[value["$position"] :]
                                )
                            else:
                                push_results += list(value["$each"])

                            if "$sort" in value:
                                sort_spec = value["$sort"]
                                if isinstance(sort_spec, dict):
                                    sort_key = set(sort_spec.keys()).pop()
                                    push_results = sorted(
                                        push_results,
                                        key=lambda d: helpers.get_value_by_dot(d, sort_key),
                                        reverse=set(sort_spec.values()).pop() < 0,
                                    )
                                else:
                                    push_results = sorted(push_results, reverse=sort_spec < 0)

                            if "$slice" in value:
                                slice_value = value["$slice"]
                                if slice_value < 0:
                                    push_results = push_results[slice_value:]
                                elif slice_value == 0:
                                    push_results = []
                                else:
                                    push_results = push_results[:slice_value]

                            unused_modifiers = set(value.keys()) - {"$each", "$slice", "$position", "$sort"}
                            if unused_modifiers:
                                raise WriteError("Unrecognized clause in $push: " + unused_modifiers.pop())
                        else:
                            push_results.append(value)
                        subdocument[field] = push_results
                else:
                    if first:
                        # replace entire document
                        for key in document.keys():
                            if key.startswith("$"):
                                # can't mix modifiers with non-modifiers in
                                # update
                                raise ValueError("field names cannot start with $ [{}]".format(k))
                        _id = spec.get("_id", existing_document.get("_id"))
                        existing_document.clear()
                        if _id is not None:
                            existing_document["_id"] = _id
                        if BSON:
                            # bson validation
                            BSON.encode(document, check_keys=True)
                        existing_document.update(self._internalize_dict(document))
                        if existing_document["_id"] != _id:
                            raise OperationFailure(
                                "The _id field cannot be changed from {0} to {1}".format(existing_document["_id"], _id)
                            )
                        break
                    else:
                        # can't mix modifiers with non-modifiers in update
                        raise ValueError("Invalid modifier specified: {}".format(k))
                first = False
            # if empty document comes
            if not document:
                _id = spec.get("_id", existing_document.get("_id"))
                existing_document.clear()
                if _id:
                    existing_document["_id"] = _id

            if was_insert:
                upserted_id = self._insert(existing_document)
                num_updated += 1
            elif existing_document != original_document_snapshot:
                # Document has been modified in-place.

                # Make sure the ID was not change.
                if original_document_snapshot.get("_id") != existing_document.get("_id"):
                    # Rollback.
                    self._store[original_document_snapshot["_id"]] = original_document_snapshot
                    raise WriteError(
                        "After applying the update, the (immutable) field '_id' was found to have "
                        "been altered to _id: {}".format(existing_document.get("_id"))
                    )

                # Make sure it still respect the unique indexes and, if not, to
                # revert modifications
                try:
                    self._ensure_uniques(existing_document)
                    num_updated += 1
                except DuplicateKeyError:
                    # Rollback.
                    self._store[original_document_snapshot["_id"]] = original_document_snapshot
                    raise

            if not multi:
                break

        return {
            text_type("connectionId"): self.database.client._id,
            text_type("err"): None,
            text_type("n"): num_matched,
            text_type("nModified"): num_updated if updated_existing else 0,
            text_type("ok"): 1,
            text_type("upserted"): upserted_id,
            text_type("updatedExisting"): updated_existing,
        }

    def _find_and_modify(
        self,
        query,
        projection=None,
        update=None,
        upsert=False,
        sort=None,
        return_document=ReturnDocument.BEFORE,
        session=None,
        **kwargs
    ):
        remove = kwargs.get("remove", False)
        if kwargs.get("new", False) and remove:
            # message from mongodb
            raise OperationFailure("remove and returnNew can't co-exist")

        if not (remove or update):
            raise ValueError("Must either update or remove")

        if remove and update:
            raise ValueError("Can't do both update and remove")

        old = self.find_one(query, projection=projection, sort=sort)
        if not old and not upsert:
            return

        if old and "_id" in old:
            query = {"_id": old["_id"]}

        if remove:
            self.delete_one(query)
        else:
            updated = self._update(query, update, upsert)
            if updated["upserted"]:
                query = {"_id": updated["upserted"]}

        if return_document is ReturnDocument.AFTER or kwargs.get("new"):
            return self.find_one(query, projection)
        return old

    if helpers.PYMONGO_VERSION < version.parse("4.0"):

        def save(self, to_save, manipulate=True, check_keys=True, **kwargs):
            warnings.warn(
                "save is deprecated. Use insert_one or replace_one " "instead", DeprecationWarning, stacklevel=2
            )
            validate_is_mutable_mapping("to_save", to_save)
            validate_write_concern_params(**kwargs)

            if "_id" not in to_save:
                return self.insert(to_save)
            self._update({"_id": to_save["_id"]}, to_save, True, manipulate, check_keys=True, **kwargs)
            return to_save.get("_id", None)

    def _delete(self, filter, collation=None, hint=None, multi=False, session=None):
        if hint:
            raise NotImplementedError(
                "The hint argument of delete is valid but has not been implemented in " "mongomock yet"
            )
        if collation:
            raise_not_implemented(
                "collation",
                "The collation argument of delete is valid but has not been " "implemented in mongomock yet",
            )
        filter = helpers.patch_datetime_awareness_in_document(filter)
        if filter is None:
            filter = {}
        if not isinstance(filter, Mapping):
            filter = {"_id": filter}
        to_delete = list(self.find(filter))
        deleted_count = 0
        for doc in to_delete:
            doc_id = doc["_id"]
            if isinstance(doc_id, dict):
                doc_id = helpers.hashdict(doc_id)
            del self._store[doc_id]
            deleted_count += 1
            if not multi:
                break

        return {
            "connectionId": self.database.client._id,
            "n": deleted_count,
            "ok": 1.0,
            "err": None,
        }

    def drop(self, session=None):
        self.database.drop_collection(self.name)

    def create_index(self, key_or_list, cache_for=300, session=None, **kwargs):
        index_list = helpers.create_index_list(key_or_list)
        is_unique = kwargs.pop("unique", False)
        is_sparse = kwargs.pop("sparse", False)

        index_name = kwargs.pop("name", helpers.gen_index_name(index_list))
        index_dict = {"key": index_list}
        if is_sparse:
            index_dict["sparse"] = True
        if is_unique:
            index_dict["unique"] = True
        if "expireAfterSeconds" in kwargs and kwargs["expireAfterSeconds"] is not None:
            index_dict["expireAfterSeconds"] = kwargs.pop("expireAfterSeconds")

        existing_index = self._store.indexes.get(index_name)
        if existing_index and index_dict != existing_index:
            raise OperationFailure("Index with name: %s already exists with different options" % index_name)

        # Check that documents already verify the uniquess of this new index.
        if is_unique:
            indexed = set()
            indexed_list = []
            for doc in self._store.documents:
                index = []
                for key, unused_order in index_list:
                    try:
                        index.append(helpers.get_value_by_dot(doc, key))
                    except KeyError:
                        if is_sparse:
                            continue
                        index.append(None)
                if is_sparse and not index:
                    continue
                index = tuple(index)
                try:
                    if index in indexed:
                        raise DuplicateKeyError("E11000 Duplicate Key Error", 11000)
                    indexed.add(index)
                except TypeError as err:
                    # index is not hashable.
                    if index in indexed_list:
                        raise_from(DuplicateKeyError("E11000 Duplicate Key Error", 11000), err)
                    indexed_list.append(index)

        self._store.create_index(index_name, index_dict)

        return index_name

    def drop_index(self, index_or_name, session=None):
        if isinstance(index_or_name, list):
            name = helpers.gen_index_name(index_or_name)
        else:
            name = index_or_name
        try:
            self._store.drop_index(name)
        except KeyError as err:
            raise_from(OperationFailure("index not found with name [%s]" % name), err)

    def drop_indexes(self, session=None):
        self._store.indexes = {}

    def list_indexes(self, session=None):
        for name, information in self._list_all_indexes():
            yield dict(information, key=dict(information["key"]), name=name, v=2)

    def index_information(self, session=None):
        return {name: dict(index, v=2) for name, index in self._list_all_indexes()}

    def distinct(self, key, filter=None, session=None):
        return self.find(filter).distinct(key)

    def rename(self, new_name, session=None, **kwargs):
        return self.database.rename_collection(self.name, new_name, **kwargs)

    def distinct(self, key, session=None):
        if not isinstance(key, string_types):
            raise TypeError("cursor.distinct key must be a string")
        unique = set()
        unique_dict_vals = []
        for x in self._compute_results():
            for value in filtering.iter_key_candidates(key, x):
                if value == NOTHING:
                    continue
                if isinstance(value, dict):
                    if any(dict_val == value for dict_val in unique_dict_vals):
                        continue
                    unique_dict_vals.append(value)
                else:
                    unique.update(value if isinstance(value, (tuple, list)) else [value])
        return list(unique) + unique_dict_vals

    def count_documents(self, filter, **kwargs):
        kwargs.pop("session", 0)
        kwargs.pop("collation", 0)
        skip = kwargs.pop("skip", 0)
        if "limit" in kwargs:
            limit = kwargs.pop("limit")
            if not isinstance(limit, (int, float)):
                raise OperationFailure("the limit must be specified as a number")
            if limit <= 0:
                raise OperationFailure("the limit must be positive")
            limit = math.floor(limit)
        else:
            limit = None
        unknown_kwargs = set(kwargs) - {"maxTimeMS", "hint"}
        if unknown_kwargs:
            raise OperationFailure("unrecognized field '%s'" % unknown_kwargs.pop())

        spec = helpers.patch_datetime_awareness_in_document(filter)
        doc_num = len(list(self._iter_documents(spec)))
        count = max(doc_num - skip, 0)
        return count if limit is None else min(count, limit)
