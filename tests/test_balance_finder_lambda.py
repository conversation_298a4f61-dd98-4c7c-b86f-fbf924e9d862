import importlib
from unittest import mock

from bson import ObjectId
from freezegun import freeze_time
import pytest
from datetime import datetime
from custom_mongomock.mongo_client import CustomMongoClient

with mock.patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    balance_finder_lambda = importlib.import_module("balance-finder-lambda.balance-finder-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(balance_finder_lambda, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield balance_finder_lambda.db
    balance_finder_lambda.client.drop_database("db")


@freeze_time("May 18 2022")
def test_modify_balance_case_1(patch_db):
    # Given
    now = datetime.utcnow()
    item = {"client_id": "1", "currency": "GBP", "amount": 4000}
    date = datetime(2022, 5, 15)
    date_yesterday = now
    session = None
    patch_db.opening_closing_balance.insert_many(
        [
            {
                "client_id": "1",
                "currency": "GBP",
                "opening_balance": 0,
                "closing_balance": 500,
                "date": datetime(2022, 5, 17),
                "created_at": now,
                "updated_at": now,
            },
            {
                "client_id": "1",
                "currency": "GBP",
                "opening_balance": 500,
                "closing_balance": 4500,
                "date": datetime(2022, 5, 18),
                "created_at": now,
                "updated_at": now,
            },
        ]
    )

    # When
    balance_finder_lambda.modify_balance(item, date, date_yesterday, session)

    # Then
    opening_closing = list(
        patch_db.opening_closing_balance.find({"client_id": "1"}, projection={"_id": 0}).sort("date")
    )
    assert opening_closing == [
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 0,
            "closing_balance": 4000,
            "date": datetime(2022, 5, 15),
            "created_at": now,
            "updated_at": now,
        },
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 4000,
            "closing_balance": 4000,
            "date": datetime(2022, 5, 16),
            "created_at": now,
            "updated_at": now,
        },
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 4000,
            "closing_balance": 4500,
            "date": datetime(2022, 5, 17),
            "created_at": now,
            "updated_at": now,
        },
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 4500,
            "closing_balance": 8500,
            "date": datetime(2022, 5, 18),
            "created_at": now,
            "updated_at": now,
        },
    ]


@freeze_time("May 18 2022")
def test_modify_balance_case_2(patch_db):
    # Given
    now = datetime.utcnow()
    item = {"client_id": "1", "currency": "GBP", "amount": 4000}
    date = datetime(2022, 5, 16)
    date_yesterday = now
    session = None
    patch_db.opening_closing_balance.insert_many(
        [
            {
                "client_id": "1",
                "currency": "GBP",
                "opening_balance": 0,
                "closing_balance": 500,
                "date": datetime(2022, 5, 15),
                "created_at": now,
                "updated_at": now,
            },
            {
                "client_id": "1",
                "currency": "GBP",
                "opening_balance": 500,
                "closing_balance": 4500,
                "date": datetime(2022, 5, 16),
                "created_at": now,
                "updated_at": now,
            },
        ]
    )

    # When
    balance_finder_lambda.modify_balance(item, date, date_yesterday, session)

    # Then
    opening_closing = list(
        patch_db.opening_closing_balance.find({"client_id": "1"}, projection={"_id": 0}).sort("date")
    )
    assert opening_closing == [
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 0,
            "closing_balance": 500,
            "date": datetime(2022, 5, 15),
            "created_at": now,
            "updated_at": now,
        },
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 500,
            "closing_balance": 8500,
            "date": datetime(2022, 5, 16),
            "created_at": now,
            "updated_at": now,
        },
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 8500,
            "closing_balance": 8500,
            "date": datetime(2022, 5, 17),
            "created_at": now,
            "updated_at": now,
        },
        {
            "client_id": "1",
            "currency": "GBP",
            "opening_balance": 8500,
            "closing_balance": 8500,
            "date": datetime(2022, 5, 18),
            "created_at": now,
            "updated_at": now,
        },
    ]


@freeze_time("July 22 2022")
def test_lambda_handler_balance_1(patch_db, monkeypatch):
    # Given
    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": ObjectId("62d631d064d58d1db33d2349"),
                "client_id": "218",
                "deposit": {"GBP": 4},
                "file_date": datetime(2022, 7, 21, 0, 0),
            },
        ],
    )
    monkeypatch.setattr(patch_db.claims_metadata, "aggregate", lambda *args, **kwargs: [])

    # When
    balance_finder_lambda.lambda_handler(None, None)

    # Then
    opening_closing_balance = list(
        patch_db.opening_closing_balance.find({}, projection={"_id": 0, "created_at": 0, "updated_at": 0}).sort(
            "date", 1
        )
    )
    assert opening_closing_balance == [
        {
            "client_id": "218",
            "currency": "GBP",
            "opening_balance": 0,
            "closing_balance": 4,
            "date": datetime(2022, 7, 21, 0, 0),
        },
        {
            "client_id": "218",
            "currency": "GBP",
            "opening_balance": 4,
            "closing_balance": 4,
            "date": datetime(2022, 7, 22, 0, 0),
        },
    ]


@freeze_time("July 23 2022")
def test_lambda_handler_balance_finder_future_date(patch_db, monkeypatch):
    # Given
    patch_db.opening_closing_balance.insert_many(
        [
            {
                "client_id": "218",
                "currency": "GBP",
                "opening_balance": 0,
                "closing_balance": 4,
                "date": datetime(2022, 7, 23),
                "created_at": datetime(2022, 7, 21),
                "updated_at": datetime(2022, 7, 21),
            }
        ]
    )

    monkeypatch.setattr(
        patch_db.banking_metadata,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": ObjectId("62d631d064d58d1db33d2349"),
                "client_id": "218",
                "deposit": {"GBP": 10},
                "file_date": datetime(2022, 7, 22, 0, 0),
            },
        ],
    )
    monkeypatch.setattr(patch_db.claims_metadata, "aggregate", lambda *args, **kwargs: [])

    # When
    balance_finder_lambda.lambda_handler(None, None)

    # Then
    opening_closing_balance = list(
        patch_db.opening_closing_balance.find({}, projection={"_id": 0, "created_at": 0, "updated_at": 0}).sort(
            "date", 1
        )
    )
    assert opening_closing_balance == [
        {
            "client_id": "218",
            "currency": "GBP",
            "opening_balance": 0,
            "closing_balance": 10,
            "date": datetime(2022, 7, 22, 0, 0),
        },
        {
            "client_id": "218",
            "currency": "GBP",
            "opening_balance": 10,
            "closing_balance": 14,
            "date": datetime(2022, 7, 23, 0, 0),
        },
    ]
