from datetime import datetime
import importlib
import json
import os
import fakeredis
from unittest.mock import patch
from freezegun import freeze_time
from mongomock import ObjectId
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    banking_module = importlib.import_module("banking-aggregator-lambda.banking-aggregator-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(banking_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield banking_module.db
    banking_module.client.drop_database("db")


@freeze_time("Mar 1st, 2022")
def test_lambda_handler(patch_db, s3_client, monkeypatch):

    fake_redis = fakeredis.FakeStrictRedis()
    monkeypatch.setattr(banking_module, "redis_client", fake_redis)

    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    file_id = "12346"

    data = {
        "fileId": file_id,
        "clientId": str(client_id),
        "dataKey": "banking/persistor/output/12346.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
    }
    metadata = patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "deposit": {"GBP": 2000},
                },
                {
                    "status": "Scanning",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": file_id,
                    "file_date": "2022-02-24",
                },
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    patch_db.banking_file_details.insert_many(
        [
            {"_id": ObjectId("621c3b23e22a9303b9b091ba"), "file_id": file_id},
            {"_id": ObjectId("621c3b23e22a9303b9b091bb"), "file_id": file_id},
            {"_id": ObjectId("621c3b23e22a9303b9b091bc"), "file_id": file_id},
            {"_id": ObjectId("621c3b23e22a9303b9b091bd"), "file_id": file_id},
        ]
    )
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )

    data_items = [
        {
            "clientId": str(client_id),
            "fileId": file_id,
            "itemCount": {"GBP": 1},
            "deposit": {"GBP": 100},
            "sftp": False,
            "sftpKey": "",
        },
        {
            "clientId": str(client_id),
            "fileId": file_id,
            "itemCount": {"GBP": 1, "USD": 2},
            "deposit": {"GBP": 100, "USD": 500},
            "sftp": False,
            "sftpKey": "",
        },
        {
            "clientId": str(client_id),
            "fileId": file_id,
            "itemCount": {"GBP": 1, "USD": 2, "NOK": 1},
            "deposit": {"GBP": 100, "USD": 500, "NOK": 400},
            "sftp": False,
            "sftpKey": "",
        },
    ]

    s3_client.put_object(
        Body=json.dumps(data_items), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    response = banking_module.lambda_handler(data, None)

    # Then
    banking_metadata = patch_db.banking_metadata.find_one({"_id": metadata.inserted_id}, projection={"_id": 0})
    assert banking_metadata == {
        "client_id": client_id,
        "banking_files": [
            {
                "status": "Cancelled by System",
                "file_name": "********-All -Banking -All anomalies.xlsx",
                "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                "file_id": "12345",
                "file_date": "2022-02-24",
                "item_count": {"GBP": 4},
                "deposit": {"GBP": 2000},
            },
            {
                "status": "Scanning",
                "file_name": "********-All -Banking -All anomalies.xlsx",
                "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                "file_id": file_id,
                "file_date": "2022-02-24",
                "item_count": {"GBP": 3, "USD": 4, "NOK": 1},
                "deposit": {"GBP": 300, "USD": 1000, "NOK": 400},
            },
        ],
        "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        "updated_at": datetime(2022, 3, 1),
    }
    assert response == [
        {"batchNo": 0, "dataKey": "banking/aggregator/12346.json"},
        {"batchNo": 1, "dataKey": "banking/aggregator/12346.json"},
    ]
