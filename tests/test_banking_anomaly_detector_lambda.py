import importlib
import json
import os
from mongomock import ObjectId
import pytest
from datetime import datetime
from freezegun import freeze_time
from unittest.mock import Mock, patch
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    banking_anomaly_detector_lambda = importlib.import_module(
        "banking-anomaly-detector-lambda.banking-anomaly-detector-lambda"
    )


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(banking_anomaly_detector_lambda, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield banking_anomaly_detector_lambda.db
    banking_anomaly_detector_lambda.client.drop_database("db")


@freeze_time("Jan 14th, 2012")
def test_negative_funds_trust_sucess(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"balance": -100, "client_id": "1", "booking_ref": "1"})
    banking_details = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "1",
            "booking_date": "2022-02-01",
            "departure_date": "2022-01-01",
            "return_date": "2022-01-01",
            "amount": 1,
            "client_id": "1",
            "banking_id": ObjectId("621c3b23e22a9303b9b091ba"),
            "currency_code": "EUR",
            "customer_type": "Agent",
            "file_id": "67555",
            "element": "ddd",
            "total_booking_value": 100,
            "transfer_of_funds": "2",
        }
    )
    banking_lookup = patch_db.lookup_anomaly.insert_one(
        {"description": "Negative cumulative Funds in Trust", "name": "Negative Funds in Trust"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "1"})
    banking_lookup_anomaly = patch_db.lookup_anomaly.find_one({"name": "Negative Funds in Trust"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.negative_funds_trust(
        transaction, trust, "67555", banking_lookup_anomaly, "Negative Funds in Trust", None
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "1",
            "transaction_id": banking_details.inserted_id,
            "client_id": "1",
            "banking_id": ObjectId("621c3b23e22a9303b9b091ba"),
            "file_id": "67555",
            "anomaly_type": "Negative Funds in Trust",
            "anomaly_id": banking_lookup_anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_negative_funds_trust_failure(patch_db):

    # Given
    patch_db.anomaly_banking.insert_one(
        {
            "client_id": "1",
            "booking_ref": "1",
            "anomaly_type": "Negative Funds in Trust",
            "deleted": False,
            "status": "Unresolved",
        }
    )
    patch_db.anomaly_claims.insert_one(
        {
            "client_id": "1",
            "booking_ref": "1",
            "anomaly_type": "Negative Funds in Trust",
            "deleted": False,
            "status": "Unresolved",
        }
    )
    patch_db.trust_fund_v2.insert_one({"balance": 100, "client_id": "1", "booking_ref": "1"})
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "1",
            "booking_date": "2022-02-01",
            "departure_date": "2022-01-01",
            "return_date": "2022-01-01",
            "amount": 1,
            "client_id": "1",
            "banking_id": ObjectId("621c3b23e22a9303b9b091ba"),
            "currency_code": "EUR",
            "customer_type": "Agent",
            "file_id": "67555",
            "element": "ddd",
            "total_booking_value": 100,
            "transfer_of_funds": "2",
        }
    )
    banking_lookup = patch_db.lookup_anomaly.insert_one(
        {"description": "Negative cumulative Funds in Trust", "name": "Negative Funds in Trust"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "1"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Negative Funds in Trust"})

    # when
    output = banking_anomaly_detector_lambda.negative_funds_trust(
        transaction, trust, "67555", anomaly, "Negative Funds in Trust", None
    )
    banking_anomaly = patch_db.anomaly_banking.find_one(
        {
            "client_id": "1",
            "booking_ref": "1",
            "anomaly_type": "Negative Funds in Trust",
            "deleted": False,
        },
        projection={"_id": 0},
    )
    claim_anomaly = patch_db.anomaly_claims.find_one(
        {
            "client_id": "1",
            "booking_ref": "1",
            "anomaly_type": "Negative Funds in Trust",
            "deleted": False,
        },
        projection={"_id": 0},
    )

    # Then
    assert output is None
    assert banking_anomaly == (
        {
            "booking_ref": "1",
            "client_id": "1",
            "anomaly_type": "Negative Funds in Trust",
            "status": "Resolved",
            "deleted": False,
            "updated_at": datetime(2012, 1, 14),
        }
    )
    assert claim_anomaly == (
        {
            "booking_ref": "1",
            "client_id": "1",
            "anomaly_type": "Negative Funds in Trust",
            "status": "Resolved",
            "deleted": False,
            "updated_at": datetime(2012, 1, 14),
        }
    )


@pytest.mark.parametrize("customer_type", [("Agent")])
@freeze_time("Jan 14th, 2012")
def test_agent_balance_sucess(patch_db, customer_type):
    # Given
    patch_db.trust_fund_v2.insert_one({"balance": 100, "client_id": "1", "booking_ref": "1"})
    banking_metadata = patch_db.banking_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2022-01-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )
    banking_details = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "1",
            "booking_date": "2022-02-01",
            "departure_date": "2022-01-01",
            "return_date": "2022-01-01",
            "amount": 1,
            "client_id": "1",
            "banking_id": banking_metadata.inserted_id,
            "currency_code": "EUR",
            "file_id": "67555",
            "element": "Agent",
            "total_booking_value": 500,
            "transfer_of_funds": "2",
        }
    )
    banking_lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Agent Balance Funds to be sent into Trust no less than 30 days before Departure Date.",
            "name": "Agent Balance",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup.inserted_id,
            "client_id": "1",
        }
    )
    patch_db.banking_metadata.find_one({"client_id": "1"})
    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    transaction["customer_type"] = customer_type
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "1"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Agent Balance"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.agent_balance(
        transaction, trust, "67555", anomaly, "Agent Balance", None
    )

    # Then
    assert banking_anomaly == {
        "booking_ref": "1",
        "transaction_id": banking_details.inserted_id,
        "client_id": "1",
        "banking_id": banking_metadata.inserted_id,
        "file_id": "67555",
        "anomaly_type": "Agent Balance",
        "anomaly_id": anomaly["_id"],
        "status": "Unresolved",
        "created_at": datetime(2012, 1, 14),
        "updated_at": datetime(2012, 1, 14),
        "deleted": False,
    }
    assert (transaction["total_booking_value"] - trust["balance"]) != 0


@pytest.mark.parametrize("customer_type", [("client")])
@freeze_time("Jan 14th, 2012")
def test_agent_balance_failure(patch_db, customer_type):
    # Given
    patch_db.trust_fund_v2.insert_one({"balance": 500, "client_id": "1", "booking_ref": "1"})
    banking_metadata = patch_db.banking_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2022-02-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "1",
            "booking_date": "2022-02-01",
            "departure_date": "2022-01-01",
            "return_date": "2022-01-01",
            "amount": 1,
            "client_id": "1",
            "banking_id": banking_metadata.inserted_id,
            "currency_code": "EUR",
            "file_id": "67555",
            "element": "Agent",
            "total_booking_value": 500,
            "transfer_of_funds": "2",
        }
    )
    banking_lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Agent Balance Funds to be sent into Trust no less than 30 days before Departure Date.",
            "name": "Agent Balance",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup.inserted_id,
            "client_id": "1",
        }
    )
    patch_db.banking_metadata.find_one({"client_id": "1"})
    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    transaction["customer_type"] = customer_type
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "1"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Agent Balance"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.agent_balance(
        transaction, trust, "67555", anomaly, "Agent Balance", None
    )

    # Then
    assert banking_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_movement_of_funds_sucess(patch_db):

    # Given
    patch_db.trust_fund_v2.insert_many(
        [
            {"balance": 500, "client_id": "1", "booking_ref": "1"},
            {"balance": 1000, "client_id": "1", "booking_ref": "2"},
        ]
    )
    banking_details = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "1",
            "booking_date": "2022-02-01",
            "departure_date": "2022-01-01",
            "return_date": "2022-01-01",
            "amount": 500,
            "client_id": "1",
            "banking_id": ObjectId("621c3b23e22a9303b9b091ba"),
            "currency_code": "EUR",
            "customer_type": "Agent",
            "file_id": "67555",
            "total_booking_value": 100,
            "transfer_of_funds": "2",
        }
    )
    banking_lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds transferred from old booking ref to new booking ref should be exactly equal to balance in Trust on old booking.",
            "name": "Movement of funds between bookings",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "1"})
    patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "2"})
    banking_lookup_anomaly = patch_db.lookup_anomaly.find_one({"name": "Movement of funds between bookings"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.movement_of_funds(
        transaction, trust, "67555", banking_lookup_anomaly, "Movement of funds between bookings", None
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "1",
            "transaction_id": banking_details.inserted_id,
            "client_id": "1",
            "banking_id": ObjectId("621c3b23e22a9303b9b091ba"),
            "file_id": "67555",
            "anomaly_type": "Movement of funds between bookings",
            "anomaly_id": banking_lookup_anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_movement_of_funds_failure(patch_db):

    # Given
    patch_db.trust_fund_v2.insert_many(
        [
            {"balance": 500, "client_id": "1", "booking_ref": "1"},
            {"balance": 100, "client_id": "1", "booking_ref": "2"},
        ]
    )
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "1",
            "booking_date": "2022-02-01",
            "departure_date": "2022-01-01",
            "return_date": "2022-01-01",
            "amount": 400,
            "client_id": "1",
            "banking_id": ObjectId("621c3b23e22a9303b9b091ba"),
            "currency_code": "EUR",
            "customer_type": "Agent",
            "file_id": "67555",
            "total_booking_value": 100,
            "transfer_of_funds": "2",
        }
    )
    banking_lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds transferred from old booking ref to new booking ref should be exactly equal to balance in Trust on old booking.",
            "name": "Movement of funds between bookings",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "1"})
    patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "2"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Movement of funds between bookings"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.movement_of_funds(
        transaction, trust, "67555", anomaly, "Movement of funds between bookings", None
    )

    # Then
    assert banking_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_banking_supplier_list(patch_db):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    banking = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Flight",
            "type": "Direct",
            "client_id": "1",
            "banking_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Banking SupplierList Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": "1",
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": ["RCCL (Royal Caribbean, Celebrity, Azamara)", "Aegean Air"],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Banking SupplierList Anomaly"})
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    patch_db.client_files.find_one({"client_id": "1"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.banking_supplier_list(
        transaction, trust, "67555", anomaly, "Banking SupplierList Anomaly", None
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": banking.inserted_id,
            "client_id": "1",
            "banking_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
            "anomaly_type": "Banking SupplierList Anomaly",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_banking_supplier_list_insurance_empty(patch_db):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Flight",
            "type": "Direct",
            "client_id": "1",
            "banking_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Banking SupplierList Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Agent Balance"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.banking_supplier_list(
        transaction, trust, "67555", anomaly, "Banking SupplierList Anomaly", None
    )

    # Then
    assert banking_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_banking_supplier_list_failure(patch_db):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "Aegean Air",
            "element": "Flight",
            "type": "Direct",
            "client_id": "1",
            "banking_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Banking SupplierList Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": "1",
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": ["RCCL (Royal Caribbean, Celebrity, Azamara)", "Aegean Air"],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    patch_db.lookup_anomaly.find_one({"name": "Banking SupplierList Anomaly"})
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    patch_db.client_files.find_one({"client_id": "1"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Agent Balance"})

    # when
    banking_anomaly = banking_anomaly_detector_lambda.banking_supplier_list(
        transaction, trust, "67555", anomaly, "Banking SupplierList Anomaly", None
    )

    # Then
    assert banking_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_livescore_claim_exceed_cost_success(patch_db):
    # Given
    banking = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 5900000,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "deleted": False,
        }
    )

    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim Exceeds Cost %",
            "description": "Amount exceeds configured percentage of Total Booking Value.",
            "custom_field_name": "Max % Of Total booking value",
            "guarantee_value_name": "Bank Guarantee Value",
        }
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 90, "bank_guarantee_value": 600000}
    )

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds Cost %"})
    anomaly["custom_field_value"] = 90
    anomaly["bank_guarantee_value"] = 600000

    # When
    banking_anomaly = banking_anomaly_detector_lambda.claim_exceed_cost(
        transaction,
        None,
        "67555",
        anomaly,
        "Claim Exceeds Cost %",
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": banking.inserted_id,
            "client_id": "1",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Claim Exceeds Cost %",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_livescore_claim_exceed_cost_failure(patch_db):
    # Given
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 1500,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "deleted": "false",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim Exceeds Cost %",
            "description": "Amount exceeds configured percentage of Total Booking Value.",
            "custom_field_name": "Max % Of Total booking value",
            "guarantee_value_name": "Bank Guarantee Value",
        }
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 10, "bank_guarantee_value": 6000}
    )

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds Cost %"})
    anomaly["custom_field_value"] = 10
    anomaly["bank_guarantee_value"] = 6000

    # When
    banking_anomaly = banking_anomaly_detector_lambda.claim_exceed_cost(
        transaction,
        None,
        "67555",
        anomaly,
        "Claim Exceeds Cost %",
    )

    # Then
    assert banking_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_trust_bookings_check(patch_db):
    # Given
    banking = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 1500,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-07-11",
            "file_id": "67555",
            "deleted": "false",
            "trust_type": "Trust",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Trust Bookings Check",
            "description": "This anomaly is to be enabled if the difference between the return date and departure date for any booking is greater than the Number of Days Given",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 2})

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Trust Bookings Check"})
    anomaly["custom_field_value"] = 2
    # When
    banking_anomaly = banking_anomaly_detector_lambda.trust_bookings_check(
        transaction,
        None,
        "67555",
        anomaly,
        "Trust Bookings Check",
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": banking.inserted_id,
            "client_id": "1",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Trust Bookings Check",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_trust_bookings_check_failure(patch_db):
    # Given
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 1500,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-07-14",
            "file_id": "67555",
            "deleted": "false",
            "trust_type": "Trust",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Trust Bookings Check",
            "description": "This anomaly is to be enabled if the difference between the return date and departure date for any booking is greater than the Number of Days Given",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 1})

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Trust Bookings Check"})
    anomaly["custom_field_value"] = 1
    # When
    banking_anomaly = banking_anomaly_detector_lambda.trust_bookings_check(
        transaction,
        None,
        "67555",
        anomaly,
        "Trust Bookings Check",
    )

    # Then
    assert banking_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_non_trust_bookings_check(patch_db):
    # Given
    banking = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 1500,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-07-14",
            "file_id": "67555",
            "deleted": "false",
            "trust_type": "Non-Trust",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Non-Trust Bookings Check",
            "description": "This anomaly is to be enabled if the difference between the return date and departure date for any booking is less than the Number of Days Given or if the booking has multiple elements",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 2})

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Non-Trust Bookings Check"})
    anomaly["custom_field_value"] = 2
    # When
    banking_anomaly = banking_anomaly_detector_lambda.non_trust_bookings_check(
        transaction,
        None,
        "67555",
        anomaly,
        "Non-Trust Bookings Check",
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": banking.inserted_id,
            "client_id": "1",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Non-Trust Bookings Check",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_non_trust_bookings_check_multiple_elements(patch_db):
    # Given
    patch_db.banking_file_details.insert_many(
        [
            {
                "booking_ref": "123",
                "status": "string",
                "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
                "currency_code": "GBP",
                "amount": 450,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-07-12",
                "file_id": "67555",
                "deleted": False,
                "trust_type": "Non-Trust",
                "element": "abc",
            },
            {
                "booking_ref": "123",
                "status": "string",
                "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
                "currency_code": "GBP",
                "amount": 890,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-07-12",
                "file_id": "67555",
                "deleted": False,
                "trust_type": "Non-Trust",
                "element": "performance",
            },
        ]
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Non-Trust Bookings Check",
            "description": "This anomaly is to be enabled if the difference between the return date and departure date for any booking is less than the Number of Days Given or if the booking has multiple elements",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 2})

    patch_db.banking_file_details.find({"file_id": "67555"})
    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Non-Trust Bookings Check"})
    anomaly["custom_field_value"] = 2
    # When
    banking_anomaly = banking_anomaly_detector_lambda.non_trust_bookings_check(
        transaction,
        None,
        "67555",
        anomaly,
        "Non-Trust Bookings Check",
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": transaction["_id"],
            "client_id": "1",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Non-Trust Bookings Check",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_non_trust_bookings_check_failure(patch_db):
    # Given
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 1500,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-07-14",
            "file_id": "67555",
            "deleted": "false",
            "trust_type": "Trust",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Non-Trust Bookings Check",
            "description": "This anomaly is to be enabled if the difference between the return date and departure date for any booking is less than the Number of Days Given or if the booking has multiple elements",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 1})

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Non-Trust Bookings Check"})
    anomaly["custom_field_value"] = 1
    # When
    banking_anomaly = banking_anomaly_detector_lambda.non_trust_bookings_check(
        transaction,
        None,
        "67555",
        anomaly,
        "Non-Trust Bookings Check",
    )

    # Then
    assert banking_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_bookings_before_live(patch_db):
    # Given
    banking = patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 1500,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-07-14",
            "booking_date": "2020-07-14",
            "file_id": "67555",
            "deleted": "false",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Bookings Before Live",
            "description": "To check if the booking date is lower than the live date 2024-03-18",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1"})

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Bookings Before Live"})

    # When
    banking_anomaly = banking_anomaly_detector_lambda.bookings_before_live(
        transaction,
        None,
        "67555",
        anomaly,
        "Bookings Before Live",
    )

    # Then
    assert banking_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": banking.inserted_id,
            "client_id": "1",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Bookings Before Live",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_bookings_before_live_failure(patch_db):
    # Given
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "123",
            "status": "string",
            "banking_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "GBP",
            "amount": 1500,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-07-11",
            "booking_date": "2024-03-19",
            "file_id": "67555",
            "deleted": "false",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Bookings Before Live",
            "description": "To check if the booking date is lower than the live date 2024-03-18",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1"})

    transaction = patch_db.banking_file_details.find_one({"file_id": "67555"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Bookings Before Live"})

    # When
    banking_anomaly = banking_anomaly_detector_lambda.bookings_before_live(
        transaction,
        None,
        "67555",
        anomaly,
        "Bookings Before Live",
    )

    # Then
    assert banking_anomaly is None


mock = Mock(return_value=None)


@patch(
    "banking-anomaly-detector-lambda.banking-anomaly-detector-lambda.negative_funds_trust",
    mock,
)
@patch(
    "banking-anomaly-detector-lambda.banking-anomaly-detector-lambda.agent_balance",
    mock,
)
@patch(
    "banking-anomaly-detector-lambda.banking-anomaly-detector-lambda.movement_of_funds",
    mock,
)
@patch(
    "banking-anomaly-detector-lambda.banking-anomaly-detector-lambda.banking_supplier_list",
    mock,
)
def test_lambda_handler(patch_db, s3_client):

    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = {"dataKey": "banking/aggregator/12345.json", "batchNo": 0}

    patch_db.trust_fund_v2.insert_one({"client_id": client_id, "booking_ref": "1"})

    banking_metadata = patch_db.banking_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": client_id,
            "updated_at": datetime(2012, 1, 14),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2022-02-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )
    patch_db.banking_file_details.insert_many(
        [
            {
                "_id": ObjectId("621c3b23e22a9303b9b091ba"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 1,
                "client_id": client_id,
                "banking_id": banking_metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "67555",
                "element": "Agent",
                "total_booking_value": 500,
                "transfer_of_funds": "2",
            },
            {
                "_id": ObjectId("621c3b23e22a9303b9b091bb"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 1,
                "client_id": client_id,
                "banking_id": banking_metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "67555",
                "element": "Agent",
                "total_booking_value": 500,
                "transfer_of_funds": "2",
            },
        ]
    )
    banking_lookup1 = patch_db.lookup_anomaly.insert_one(
        {"description": "Negative cumulative Funds in Trust", "name": "Negative Funds in Trust"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup1.inserted_id,
            "client_id": client_id,
        }
    )
    banking_lookup2 = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Agent Balance Funds to be sent into Trust no less than 30 days before Departure Date.",
            "name": "Agent Balance",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup2.inserted_id,
            "client_id": client_id,
        }
    )
    banking_lookup3 = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds transferred from old booking ref to new booking ref should be exactly equal to balance in Trust on old booking.",
            "name": "Movement of funds between bookings",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup3.inserted_id,
            "client_id": client_id,
        }
    )
    banking_lookup12 = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Banking SupplierList Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": banking_lookup12.inserted_id,
            "client_id": client_id,
        }
    )

    output_data = {
        "clientId": str(client_id),
        "fileId": "67555",
        "sftp": False,
        "sftpKey": "",
        "transactionIds": ["621c3b23e22a9303b9b091ba", "621c3b23e22a9303b9b091bb"],
    }
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(output_data), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = banking_anomaly_detector_lambda.lambda_handler(data, "context")

    # Then
    assert result == {"fileId": "67555", "clientId": str(client_id), "sftp": False, "sftpKey": ""}
    assert mock.call_count == 8
