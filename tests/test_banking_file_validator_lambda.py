import importlib
from unittest.mock import patch, MagicMock
import pytest
from datetime import datetime
from freezegun import freeze_time
from custom_mongomock.mongo_client import CustomMongoClient
from hamcrest import assert_that, calling, raises

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    banking_module = importlib.import_module("banking-file-validator-lambda.banking-file-validator-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(banking_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield banking_module.db
    banking_module.client.drop_database("db")


@freeze_time("Jan 14th, 2012")
def test_cancel_banking_file(patch_db):
    # Given
    banking_metadata = patch_db.banking_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "67555",
                    "file_date": "2022-01-21",
                    "item_count": {"GBP": 14},
                    "deposit": {"GBP": 45920},
                }
            ],
            "created_at": datetime(2012, 1, 14),
        },
    )
    # When
    banking_module.cancel_banking_file(
        "67555", "Validation Error for -1043388 of type alphanumeric in bookingref column"
    )
    metadata = patch_db.banking_metadata.find_one({"client_id": "1"})
    # Then
    assert metadata == (
        {
            "_id": banking_metadata.inserted_id,
            "status": "Cancelled by System",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "banking_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "67555",
                    "file_date": "2022-01-21",
                    "item_count": {"GBP": 14},
                    "deposit": {"GBP": 45920},
                    "notes": "Validation Error for -1043388 of type alphanumeric in bookingref column",
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )


@pytest.mark.parametrize(
    "value,datatype,file_id,output,key,row_count,client_id",
    [
        ("string", "string", "67555", "string", "bookingref", 1, "123"),
        ("1", "number", "67555", 1.0, "amount", 3, "123"),
        ("-1", "number", "67555", -1.0, "amount", 3, "123"),
        ("-3.552713678800501e+14", "number", "67555", -***************.1, "amount", 3, "123"),
        ("-3.552713678800501e-14", "number", "67555", -3.552713678800501e-14, "amount", 4, "123"),
        ("1a2b", "alphanumeric", "67555", "1a2b", "reference", 2, "123"),
        ("2021-02-12", "date", "67555", "2021-02-12", "returndate", 3, "123"),
        ("January", "month", "67555", "January", "month", 2, "123"),
        ("january", "month", "67555", "january", "month", 3, "123"),
        ("True", "boolean", "6755", True, "intrust", 4, "123"),
        ("Yes", "boolean", "6755", True, "intrust", 1, "123"),
        ("False", "boolean", "6755", False, "intrust", 2, "123"),
        ("No", "boolean", "6755", False, "intrust", 2, "123"),
        ("true", "boolean", "6755", True, "intrust", 3, "123"),
        ("yes", "boolean", "6755", True, "intrust", 4, "123"),
        ("false", "boolean", "6755", False, "intrust", 4, "123"),
        ("no", "boolean", "6755", False, "intrust", 2, "123"),
        ("y", "boolean", "6755", True, "intrust", 2, "123"),
        ("Y", "boolean", "6755", True, "intrust", 3, "123"),
        ("n", "boolean", "6755", False, "intrust", 1, "123"),
        ("N", "boolean", "6755", False, "intrust", 3, "123"),
    ],
)
def test_validate_type_success(patch_db, value, datatype, file_id, output, key, row_count, client_id):
    # When
    result = banking_module.validate_type(value, datatype, file_id, key, row_count, client_id)
    # Then
    assert result == output


@pytest.mark.parametrize(
    "value,datatype,file_id,key,row_count,client_id",
    [
        (1, "string", "67555", "bookingref", 1, "123"),
        ("a", "number", "67555", "amount", 2, "123"),
        (1, "alphanumeric", "67555", "reference", 2, "123"),
        (2021 - 2 - 12, "date", "67555", "bookingdate", 4, "123"),
        ("01", "month", "67555", "month", 2, "123"),
        (None, "string", "67555", "bookingref", 3, "123"),
        (None, "number", "67555", "amount", 2, "123"),
        (None, "alphanumeric", "67555", "reference", 3, "123"),
        (None, "date", "67555", "bookingdate", 2, "123"),
        (None, "month", "67555", "month", 3, "123"),
    ],
)
def test_validate_type_failure(patch_db, value, datatype, file_id, key, row_count, client_id):
    # When

    # Then
    assert_that(
        calling(banking_module.validate_type).with_args(value, datatype, file_id, key, row_count, client_id),
        raises(Exception),
    )


@pytest.mark.parametrize(
    "input,output",
    [
        (["2020-05-04", "date", "file_id", "date"], "2020-05-04"),
        (["2020-05-04 10:18:32", "date", "file_id", "date"], "2020-05-04"),
        (["2020-05-04 10:18:32.123", "date", "file_id", "date"], "2020-05-04"),
        (["2020/05/04", "date", "file_id", "date"], "2020-05-04"),
        (["04-05-2020", "date", "file_id", "date"], "2020-05-04"),
        (["04-05-2020 10:18:32", "date", "file_id", "date"], "2020-05-04"),
        (["04-05-2020 10:18:32.234567", "date", "file_id", "date"], "2020-05-04"),
        (["04/05/2020", "date", "file_id", "date"], "2020-05-04"),
        (["2020/05/04 12:00:00 AM", "date", "file_id", "date"], "2020-05-04"),
        (["6/15/2021 12:00:00 AM", "date", "file_id", "date"], "2021-06-15"),
    ],
)
def test_banking_try_parsing_date(input, output):
    # Given
    data = input
    # When
    result = banking_module.try_parsing_date(*data, 1, "123")
    # Then
    assert result == output


@patch(
    "banking-file-validator-lambda.banking-file-validator-lambda.cancel_banking_file",
    MagicMock(),
)
def test_banking_try_parsing_date_failure(patch_db):
    # Given
    data = ["invalid_date", "date", "file_id", "date"]
    # When

    # Then
    assert_that(
        calling(banking_module.try_parsing_date).with_args(*data, 1, "123"),
        raises(ValueError),
    )


@pytest.mark.parametrize("input,output", [("A BC", "abc"), ("A  B C", "abc"), ("A B C", "abc"), ("A / b", "a/b")])
def test_custom_filter(input, output):
    # Given
    data = input
    # When
    result = banking_module.custom_filter(data)
    # Then
    assert result == output


def test_currency_validation_sucess(patch_db):
    # Given
    currency_list = ["GBP", "USD"]
    # When
    result = banking_module.currency_validation(currency_list, "USD", "123")
    # Then
    assert result == "USD"


@pytest.mark.parametrize("input", ["usdw", "GBP"])
def test_currency_validation_failure(patch_db, input):
    # Given
    currency_list = ["NOK", "USD"]
    value = input
    # Then
    assert_that(
        calling(banking_module.currency_validation).with_args(currency_list, value, "123"),
        raises(Exception),
    )


# def test_custom_data_modifier_pennywood(patch_db):
#     # Given
#     patch_db.lookup_banking_elements.insert_many(
#         [
#             {"name": "Flight"},
#             {"name": "Flight BSP"},
#             {"name": "Flight Corp Card"},
#             {"name": "Insurance"},
#             {"name": "Cruise"},
#             {"name": "Coach"},
#             {"name": "Commission"},
#             {"name": "Accommodation"},
#             {"name": "Performance"},
#             {"name": "Car hire"},
#             {"name": "Tours"},
#             {"name": "Deposit"},
#             {"name": "Balance"},
#             {"name": "Non-trust"},
#         ]
#     )

#     data = [
#         {
#             "Booking reference": "BTW232058",
#             "Lead Pax": "Mrs P Smith",
#             "Booking Date": "2021-11-12 00:00:00",
#             "Departure Date": "2022-05-13 00:00:00",
#             "Return Date": "2022-05-27 00:00:00",
#             "Booking Type": "Package (Multi-contract)",
#             "Flight": "0",
#             "Flight BSP": "0",
#             "Flight Corp Card": "822.8",
#             "Non-trust": "0",
#             "Balance": "0",
#             "Deposit": "0",
#             "Insurance": "0",
#             "Cruise": "0",
#             "Coach": "0",
#             "Commission": "0",
#             "Accommodation": "0",
#             "Performance": "0",
#             "Car hire": "0",
#             "Tours": "0",
#         },
#         {
#             "Booking reference": "BSH360284",
#             "Lead Pax": "Mr I Gammon",
#             "Booking Date": "2022-03-19 00:00:00",
#             "Departure Date": "2022-08-25 00:00:00",
#             "Return Date": "2022-09-06 00:00:00",
#             "Booking Type": "Package (Multi-contract)",
#             "Flight": "0",
#             "Flight BSP": "0",
#             "Flight Corp Card": "0",
#             "Non-trust": "0",
#             "Balance": "0",
#             "Deposit": "678",
#             "Insurance": "0",
#             "Cruise": "0",
#             "Coach": "0",
#             "Commission": "0",
#             "Accommodation": "100",
#             "Performance": "0",
#             "Car hire": "0",
#             "Tours": "0",
#         },
#     ]

#     row_count = 5
#     # When
#     result = banking_module.custom_data_modifier_pennywood(data, row_count, "123")

#     # Then
#     assert result == [
#         {
#             "Booking reference": "BTW232058",
#             "Lead Pax": "Mrs P Smith",
#             "Booking Date": "2021-11-12 00:00:00",
#             "Departure Date": "2022-05-13 00:00:00",
#             "Return Date": "2022-05-27 00:00:00",
#             "Booking Type": "Package (Multi-contract)",
#             "element": "Flight Corp Card",
#             "Amount": "822.8",
#             "initial_row_count": 6,
#         },
#         {
#             "Booking reference": "BSH360284",
#             "Lead Pax": "Mr I Gammon",
#             "Booking Date": "2022-03-19 00:00:00",
#             "Departure Date": "2022-08-25 00:00:00",
#             "Return Date": "2022-09-06 00:00:00",
#             "Booking Type": "Package (Multi-contract)",
#             "Amount": "678",
#             "element": "Deposit",
#             "initial_row_count": 7,
#         },
#         {
#             "Booking reference": "BSH360284",
#             "Lead Pax": "Mr I Gammon",
#             "Booking Date": "2022-03-19 00:00:00",
#             "Departure Date": "2022-08-25 00:00:00",
#             "Return Date": "2022-09-06 00:00:00",
#             "Booking Type": "Package (Multi-contract)",
#             "Amount": "100",
#             "element": "Accommodation",
#             "initial_row_count": 7,
#         },
#     ]
