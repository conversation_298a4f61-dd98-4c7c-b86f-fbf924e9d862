from datetime import datetime
import importlib
import os
from mongomock import ObjectId
import pytest
import json
from unittest import mock
from custom_mongomock.mongo_client import CustomMongoClient
from freezegun import freeze_time
import tempfile

TEMP_DIR = tempfile.gettempdir()

with mock.patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    os.environ["CLIENT_IDS"] = "62ce5983bff9a1862ca13424, 62ce5983bff9a1862ca13431"
    banking_module = importlib.import_module("banking-persistor-lambda.banking-persistor-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    yield banking_module.db
    banking_module.client.drop_database("db")


@pytest.mark.skip("To be completed later")
def test_lambda_handler_banking_persistor(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow1"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id}
    )
    client_id = basic_info.inserted_id
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "banking/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
        "fileType": "text/csv",
    }
    data_list = [
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2020-11-02",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "type": "string",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2020-11-02",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "type": "string",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2020-11-02",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "type": "string",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2020-11-02",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "type": "string",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 14},
                    "deposit": {"GBP": 45920},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = banking_module.lambda_handler(data, None)

    # Then
    banking_info = list(
        patch_db.banking_file_details.find(
            {"client_id": ObjectId(data["clientId"]), "booking_ref": data_list[0]["booking_ref"]}
        )
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == 1300
    assert trust_fund_info["total_in_trust"] == 1300
    assert trust_fund_info["type"] == "string"
    assert trust_fund_info["payment_type"] == "Giro"
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "banking/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert_banking_file_details(banking_info[0], data, data_list[2])
    assert_banking_file_details(banking_info[1], data, data_list[3])


@pytest.mark.skip("To be completed later")
def test_lambda_handler_banking_persistor_atol_escrow(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("621c4699b29069e5c622ca88"),
            "client_id": "2193",
            "type_of_trust_account": lookup_trust.inserted_id,
        }
    )
    basic_info = patch_db.client_basic_info.find_one({"_id": ObjectId("621c4699b29069e5c622ca88")})
    client_id = basic_info["_id"]
    data = {
        "dataKey": "banking/12345.json",
    }
    data_list = [
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 14},
                    "deposit": {"GBP": 45920},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = banking_module.lambda_handler(data, None)

    # Then
    banking_info = list(
        patch_db.banking_file_details.find({"client_id": client_id, "booking_ref": data_list[0]["booking_ref"]})
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == 900
    assert trust_fund_info["total_in_trust"] == 900
    assert trust_fund_info["payment_type"] == "Giro"
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "banking/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert banking_info[0]["amount"] == 100
    assert banking_info[0]["original_amount"] == 200
    assert banking_info[0]["escrow_multiplier"] == 0.5


@pytest.mark.skip("To be completed later")
def test_banking_lambda_handler_without_currency_code(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow1"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id}
    )
    client_id = basic_info.inserted_id
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "banking/persistor/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "batchNo": 0,
        "batchSize": 2,
        "sftp": False,
        "sftpKey": "",
        "fileType": "text/csv",
    }
    data_list = [
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2020-11-02",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 14},
                    "deposit": {"GBP": 45920},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = banking_module.lambda_handler(data, None)

    # Then
    banking_info = patch_db.banking_file_details.find_one(
        {"client_id": ObjectId(data["clientId"]), "booking_ref": data_list[0]["booking_ref"]}
    )
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "banking/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert banking_info["currency_code"] == "GBP"


@pytest.mark.skip("To be completed later")
def test_lambda_handler_banking_persistor_we_love_holidays(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "2175", "type_of_trust_account": lookup_trust.inserted_id}
    )
    client_id = basic_info.inserted_id
    patch_db.client_escrow_multiplier.insert_one(
        {"client_id": client_id, "multiplier": 0.75, "date": "2022-08-01", "modified_by": "asd"}
    )
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "banking/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
        "fileType": "text/csv",
    }
    data_list = [
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Scanning",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-08-10",
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = banking_module.lambda_handler(data, None)

    # Then
    banking_info = list(
        patch_db.banking_file_details.find(
            {"client_id": ObjectId(data["clientId"]), "booking_ref": data_list[0]["booking_ref"]}
        )
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == 1100
    assert trust_fund_info["total_in_trust"] == 1100
    assert trust_fund_info["payment_type"] == "Giro"
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "banking/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert banking_info[0]["amount"] == 150
    assert banking_info[0]["original_amount"] == 200
    assert banking_info[0]["escrow_multiplier"] == 0.75


@pytest.mark.parametrize(
    "transaction_amount,trust,output,escrow_multiplier",
    [
        (
            1500,
            {
                "balance": 1000,
                "total_in_trust": 1000,
            },
            {
                "trust_balance": 1500,
                "balance": 1500,
                "amount": 500,
                "original_amount": 500,
                "total_in_trust": 1500,
                "deposit": 500,
                "original_balance": 1500,
            },
            1,
        ),
        (
            3000,
            {
                "booking_ref": "1",
                "balance": 1000,
                "total_in_trust": 1000,
            },
            {
                "trust_balance": 1500,
                "balance": 1500,
                "amount": 500,
                "total_in_trust": 1500,
                "original_amount": 1000,
                "deposit": 500,
                "original_balance": 3000,
            },
            0.5,
        ),
        (
            1000,
            {
                "balance": 2000,
                "total_in_trust": 2000,
            },
            {
                "trust_balance": 1000,
                "balance": 1000,
                "amount": -1000,
                "original_amount": -1000,
                "total_in_trust": 1000,
                "deposit": -1000,
                "original_balance": 1000,
            },
            1,
        ),
        (
            1000,
            {
                "balance": 2000,
                "total_in_trust": 2000,
            },
            {
                "trust_balance": 500,
                "balance": 500,
                "amount": -1500,
                "original_amount": -3000,
                "total_in_trust": 500,
                "deposit": -1500,
                "original_balance": 1000,
            },
            0.5,
        ),
        (
            456,
            {
                "balance": 159.6,
                "total_in_trust": 159.6,
            },
            {
                "trust_balance": 114,
                "balance": 114,
                "amount": -45.599999999999994,
                "original_amount": -182.4,
                "total_in_trust": 114,
                "deposit": -45.599999999999994,
                "original_balance": 456,
            },
            0.25,
        ),
        (
            1000,
            {},
            {
                "trust_balance": 500,
                "balance": 500,
                "amount": 500,
                "original_amount": 1000,
                "total_in_trust": 500,
                "deposit": 500,
                "original_balance": 1000,
            },
            0.5,
        ),
    ],
)
@pytest.mark.skip("To be completed later")
def test_lambda_handler_banking_persistor_wlh(
    patch_db, s3_client, transaction_amount, trust, output, escrow_multiplier
):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {
            "client_id": "2175",
            "type_of_trust_account": lookup_trust.inserted_id,
            "_id": ObjectId("62cc1b5116133e88d93dba5c"),
        }
    )
    client_id = basic_info.inserted_id
    trust.update({"client_id": client_id, "booking_ref": "1", "total_claimed": 0, "currency_code": ["GBP"]})
    patch_db.client_escrow_multiplier.insert_one(
        {"client_id": client_id, "multiplier": escrow_multiplier, "date": "2022-08-01", "modified_by": "asd"}
    )
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "banking/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
        "fileType": "text/csv",
    }
    data_list = [
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "1",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": transaction_amount,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        }
    ]
    patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Scanning",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-08-10",
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )

    patch_db.trust_fund_v2.insert_one(trust)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = banking_module.lambda_handler(data, None)

    # Then
    banking_info = list(patch_db.banking_file_details.find({"client_id": client_id, "booking_ref": "1"}))

    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "1"})
    assert trust_fund_info["balance"] == output["trust_balance"]
    assert trust_fund_info["total_in_trust"] == output["total_in_trust"]
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "banking/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert banking_info[0]["balance"] == output["balance"]
    assert banking_info[0]["amount"] == output["amount"]
    assert banking_info[0]["original_amount"] == output["original_amount"]
    assert banking_info[0]["balance"] == output["balance"]
    assert banking_info[0]["original_balance"] == output["original_balance"]
    assert banking_info[0]["escrow_multiplier"] == escrow_multiplier


@pytest.mark.parametrize(
    "client_escrow_multiplier,basic_info_multiplier,expected_multiplier",
    [(None, 0.5, 0.5), (0.7, None, 0.7), (0.5, 0.7, 0.5), (None, None, 0.7)],
)
@pytest.mark.skip("To be completed later")
def test_lambda_handler_banking_persistor_atol_escrow_with_multiplier(
    patch_db, s3_client, client_escrow_multiplier, basic_info_multiplier, expected_multiplier
):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {
            "client_id": "2105",
            "type_of_trust_account": lookup_trust.inserted_id,
            "escrow_multiplier": basic_info_multiplier,
        }
    )
    client_id = basic_info.inserted_id
    patch_db.client_escrow_multiplier.insert_one(
        {"client_id": client_id, "multiplier": client_escrow_multiplier, "date": "2022-08-01", "modified_by": "asd"}
    )
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "banking/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
        "fileType": "text/csv",
    }
    data_list = [
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Scanning",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-08-10",
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = banking_module.lambda_handler(data, None)

    # Then
    banking_info = list(
        patch_db.banking_file_details.find(
            {"client_id": ObjectId(data["clientId"]), "booking_ref": data_list[0]["booking_ref"]}
        )
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] * expected_multiplier
    assert trust_fund_info["total_in_trust"] * expected_multiplier
    assert trust_fund_info["payment_type"] == "Giro"
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "banking/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert banking_info[0]["amount"] * expected_multiplier
    assert banking_info[0]["original_amount"] == 200
    assert banking_info[0]["escrow_multiplier"] == expected_multiplier


@freeze_time("Mar 1st, 2022")
def test_handle_booking_ref_reuse_banking_persistor(patch_db, monkeypatch):
    # Given
    client_id = "62ce5983bff9a1862ca13424"
    session = None
    transaction = [
        {
            "booking_ref": "test",
            "lead_pax": "Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 100,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "type": "abc",
            "total_booking_value": 4030.0,
            "payment_type": "Giro",
            "booking_status": "Live",
        }
    ]
    patch_db.trust_fund_v2.insert_one(
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "booking_ref": "test",
            "lead_pax": "Lady Emma Monson",
            "return_date": "2020-01-01",
            "balance": 100,
        }
    )
    patch_db.banking_file_details.insert_many(
        [
            {
                "client_id": ObjectId("62ce5983bff9a1862ca13424"),
                "booking_ref": "test",
                "amount": 50,
            },
            {
                "client_id": ObjectId("62ce5983bff9a1862ca13424"),
                "booking_ref": "test",
                "amount": 100,
            },
        ]
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "client_id": ObjectId("62ce5983bff9a1862ca13424"),
                "booking_ref": "test",
                "amount": 50,
            },
            {
                "client_id": ObjectId("62ce5983bff9a1862ca13424"),
                "booking_ref": "test",
                "amount": 100,
            },
        ]
    )

    existing_trust_fund = patch_db.trust_fund_v2.find_one()

    # When
    banking_module.handle_booking_ref_reuse_banking_persistor(existing_trust_fund, transaction[0], client_id, session)

    # Then
    trust_funds = list(patch_db.trust_fund_v2.find(projection={"_id": 0}))
    banking_file_details = list(patch_db.banking_file_details.find(projection={"_id": 0}))
    claims_file_details = list(patch_db.claims_file_details.find(projection={"_id": 0}))

    assert trust_funds == [
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "booking_ref": "test_old_1",
            "lead_pax": "Lady Emma Monson",
            "return_date": "2020-01-01",
            "balance": 100,
            "original_booking_ref": "test",
        }
    ]

    assert banking_file_details == [
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "booking_ref": "test_old_1",
            "amount": 50,
        },
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "booking_ref": "test_old_1",
            "amount": 100,
        },
    ]

    assert claims_file_details == [
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "booking_ref": "test_old_1",
            "amount": 50,
        },
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "booking_ref": "test_old_1",
            "amount": 100,
        },
    ]


@pytest.mark.skip("To be completed later")
def test_lambda_handler_banking_persistor_gtl_protected_deposit(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("12cc1b5116133e88d93dba12"),
            "client_id": "2146",
            "type_of_trust_account": lookup_trust.inserted_id,
        }
    )
    basic_info = patch_db.client_basic_info.find_one({"_id": ObjectId("12cc1b5116133e88d93dba12")})
    client_id = basic_info["_id"]
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "banking/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
        "fileType": "text/csv",
    }
    data_list = [
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Protected Deposit - Applied",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "clientId": str(client_id),
            "fileId": "12345",
            "sftp": False,
            "sftpKey": "",
            "fileType": "text/csv",
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 400,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.banking_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 2},
                    "deposit": {"GBP": 600},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 100,
        "total_in_trust": 10,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    banking_module.lambda_handler(data, None)

    # Then
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == 500
    assert trust_fund_info["total_in_trust"] == 610


def assert_banking_file_details(banking_info, data, banking_data):
    assert banking_info["client_id"] == ObjectId(data["clientId"])
    assert banking_info["file_id"] == data["fileId"]
    assert banking_info["booking_ref"] == banking_data["booking_ref"]
    assert banking_info["lead_pax"] == banking_data["lead_pax"]
    assert banking_info["pax_count"] == banking_data["pax_count"]
    assert banking_info["booking_date"] == banking_data["booking_date"]
    assert banking_info["departure_date"] == banking_data["departure_date"]
    assert banking_info["return_date"] == banking_data["return_date"]
    assert banking_info["currency_code"] == banking_data["currency_code"]
    assert banking_info["amount"] == banking_data["amount"]
    assert banking_info["customer_type"] == banking_data["customer_type"]
    assert banking_info["bonding"] == banking_data["bonding"]
    assert banking_info["payment_type"] == banking_data["payment_type"]
    assert banking_info["days_to_process"] == banking_data["days_to_process"]
    assert banking_info["total_booking_value"] == banking_data["total_booking_value"]
    assert banking_info["supplier_ref"] == banking_data["supplier_ref"]
    assert banking_info["supplier_names"] == banking_data["supplier_names"]
    assert banking_info["payment_date"] == banking_data["payment_date"]
    assert banking_info["booking_status"] == banking_data["booking_status"]
    assert banking_info["transfer_of_funds"] == banking_data["transfer_of_funds"]
