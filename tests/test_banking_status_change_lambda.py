from datetime import datetime
import importlib
import json
import os
from unittest.mock import MagicMock, patch
from freezegun import freeze_time
from mongomock import ObjectId
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    banking_module = importlib.import_module("banking-status-change-lambda.banking-status-change-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(banking_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield banking_module.db
    banking_module.client.drop_database("db")


@freeze_time("Jan 14th, 2012")
@pytest.mark.parametrize(
    "previous_status, status, is_deleted, banking_input, trust_fund_output, is_banking_transaction_deleted, deleted_flag, transaction_status, expected_amount_changes",
    [
        (
            "Submitted",
            "Cancelled",
            False,
            [
                {
                    "_id": ObjectId("628783cf556173a598ea4354"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": False,
                    "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                    "check": "full-check",
                },
                {
                    "_id": ObjectId("628783cf556173a598ea4355"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": False,
                    "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                },
            ],
            {
                "client_id": "2",
                "booking_ref": "123",
                "balance": 4000,
                "total_in_trust": 0,
                "total_claimed": 4300,
                "currency_code": ["GBP"],
                "_id": ObjectId("62459c773fa78dea6fab97a7"),
                "created_at": datetime(2012, 1, 14),
                "updated_at": datetime(2012, 1, 14),
            },
            True,
            True,
            "Cancelled",
            {
                "itemCountChanges": {"GBP": -2},
                "depositChanges": {"GBP": -1000},
            },
        ),
        (
            "Cancelled",
            "Submitted",
            True,
            [
                {
                    "_id": ObjectId("628783cf556173a598ea4354"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": True,
                    "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                    "check": "quick-check",
                },
                {
                    "_id": ObjectId("628783cf556173a598ea4355"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": True,
                    "banking_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                },
            ],
            {
                "client_id": "2",
                "booking_ref": "123",
                "balance": 6000,
                "total_in_trust": 2000,
                "total_claimed": 4300,
                "currency_code": ["GBP"],
                "_id": ObjectId("62459c773fa78dea6fab97a7"),
                "created_at": datetime(2012, 1, 14),
                "updated_at": datetime(2012, 1, 14),
            },
            False,
            False,
            "Reinstated",
            {
                "itemCountChanges": {"GBP": 2},
                "depositChanges": {"GBP": 1000},
            },
        ),
    ],
)
@pytest.mark.skip("To be completed later")
def test_handle_banking_status_change_update(
    patch_db,
    s3_client,
    previous_status,
    status,
    is_deleted,
    banking_input,
    trust_fund_output,
    is_banking_transaction_deleted,
    deleted_flag,
    transaction_status,
    expected_amount_changes,
):
    # Given
    data = {
        "state": "update",
        "input": {"Key": "banking-status-change/1234/test-id/0"},
    }
    transactions = {
        "fileId": "1234",
        "clientId": "2",
        "bankingId": "621c4699b29069e5c611ca88",
        "isDeleted": is_deleted,
        "createdAt": datetime(2012, 1, 14).isoformat(),
        "fileDate": "2022-04-22",
        "transaction_ids": [str(transaction["_id"]) for transaction in banking_input],
        "bookings": [],
    }
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(transactions),
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        Key=data["input"]["Key"],
    )

    transaction_id = ObjectId("628783cf556173a598ea4354")
    patch_db.banking_file_details.insert_many(banking_input)
    anomaly_id = patch_db.anomaly_banking.insert_one(
        {
            "transaction_id": transaction_id,
            "file_id": banking_input[0]["file_id"],
            "deleted": banking_input[0]["deleted"],
        }
    ).inserted_id
    trust_funds = patch_db.trust_fund_v2.insert_one(
        {
            "client_id": "2",
            "booking_ref": "123",
            "balance": 5000,
            "total_in_trust": 1000,
            "total_claimed": 4300,
            "currency_code": ["GBP"],
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
        }
    )

    # When
    result = banking_module.lambda_handler(data, None)

    # Then
    trust_fund = patch_db.trust_fund_v2.find_one({"client_id": "2", "booking_ref": "123"})
    banking_file_details = list(patch_db.banking_file_details.find({"client_id": "2"}))
    trust_fund_output["_id"] = trust_funds.inserted_id
    anomaly = patch_db.anomaly_banking.find_one({"_id": anomaly_id})
    assert trust_fund == trust_fund_output
    assert anomaly["deleted"] == is_banking_transaction_deleted
    for trancaction in banking_file_details:
        assert trancaction["deleted"] == deleted_flag
        assert trancaction["status"] == transaction_status
    assert result == expected_amount_changes


@pytest.mark.parametrize(
    "data",
    [
        {
            "state": "final",
            "data": {
                "clientId": "628783cf556343a598ea4354",
                "bankingId": "628783cf556173a598ea4354",
                "fileId": "12346",
                "newStatus": "Cancelled",
                "createdAt": datetime(2012, 1, 14).isoformat(),
                "fileDate": "2022-02-24",
                "bookings": [],
                "input": [
                    {
                        "itemCountChanges": {"GBP": 2},
                        "depositChanges": {"GBP": 1000},
                    }
                ],
            },
        },
        {
            "state": "final",
            "data": {
                "clientId": "628783cf556343a598ea4354",
                "bankingId": "628783cf556173a598ea4354",
                "fileId": "12346",
                "newStatus": "Cancelled",
                "createdAt": datetime(2012, 1, 14).isoformat(),
                "fileDate": "2022-02-24",
                "bookings": [],
                "input": [],
            },
        },
    ],
)
@patch("helpers.track_opening_closing_balance_changes", MagicMock(return_value=None))
@freeze_time("Jan 14th, 2012")
@pytest.mark.skip("To be completed later")
def test_handle_banking_status_change_final(patch_db, data):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    metadata = patch_db.banking_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4354"),
            "client_id": client_id,
            "status": "Updating Status",
            "banking_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.utcnow(),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "deposit": {"GBP": 2000},
                },
                {
                    "status": "Updating Status",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.utcnow(),
                    "file_id": "12346",
                    "file_date": "2022-02-24",
                },
            ],
            "created_at": datetime.utcnow(),
        }
    )

    # When
    banking_module.lambda_handler(
        data,
        None,
    )

    # Then
    banking_metadata = patch_db.banking_metadata.find_one(
        {"_id": metadata.inserted_id},
        projection={
            "client_id": 1,
            "status": 1,
            "banking_files.status": 1,
            "banking_files.file_id": 1,
            "banking_files.file_date": 1,
            "banking_files.submitted_date": 1,
            "banking_files.file_name": 1,
            "created_at": 1,
            "updated_at": 1,
        },
    )
    assert banking_metadata == {
        "_id": metadata.inserted_id,
        "client_id": client_id,
        "status": "Cancelled",
        "banking_files": [
            {
                "status": "Cancelled by System",
                "file_name": "********-All -Banking -All anomalies.xlsx",
                "submitted_date": datetime.utcnow(),
                "file_id": "12345",
                "file_date": "2022-02-24",
            },
            {
                "status": "Cancelled",
                "file_name": "********-All -Banking -All anomalies.xlsx",
                "submitted_date": datetime.utcnow(),
                "file_id": "12346",
                "file_date": "2022-02-24",
            },
        ],
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }
