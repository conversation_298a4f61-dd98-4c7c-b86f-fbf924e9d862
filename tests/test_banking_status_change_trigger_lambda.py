from datetime import datetime
import importlib
import json
from math import ceil
import os
from unittest.mock import patch
import boto3
from mongomock import ObjectId
from moto import mock_stepfunctions, mock_sts
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

BATCH_SIZE = int(os.environ.get("BATCH_SIZE"))

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    banking_status_change_trigger_lambda = importlib.import_module(
        "banking-status-change-trigger-lambda.banking-status-change-trigger-lambda"
    )

simple_definition = (
    '{"Comment": "An example of the Amazon States Language using a choice state.",'
    '"StartAt": "DefaultState",'
    '"States": '
    '{"DefaultState": {"Type": "Fail","Error": "DefaultStateError","Cause": "No Matches!"}}}'
)
account_id = None


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(
        banking_status_change_trigger_lambda, "db", CustomMongoClient("mongodb://test/db").get_database()
    )
    yield banking_status_change_trigger_lambda.db
    banking_status_change_trigger_lambda.client.drop_database("db")


def _get_account_id():
    global account_id
    if account_id:
        return account_id
    sts = boto3.client("sts")
    identity = sts.get_caller_identity()
    account_id = identity["Account"]
    return account_id


def _get_default_role():
    return "arn:aws:iam::" + _get_account_id() + ":role/unknown_sf_role"


@mock_stepfunctions
@mock_sts
def create_sts():
    client = boto3.client("stepfunctions")
    sm = client.create_state_machine(
        name="banking-status-change-orchestration-dev", definition=str(simple_definition), roleArn=_get_default_role()
    )
    return sm["stateMachineArn"]


@patch(
    "uuid.uuid4",
    lambda: "test-id",
)
@pytest.mark.parametrize(
    "is_deleted, output_transactions",
    [
        (True, ["62188fcb7ec8edb86189c4fb", "62188fcb7ec8edb86189c4fc"]),
        (
            False,
            [
                "62188fcb7ec8edb86189c4fa",
                "62188fcb7ec8edb86189c4fb",
                "62188fcb7ec8edb86189c4fc",
            ],
        ),
    ],
)
@mock_stepfunctions
@mock_sts
def test_lambda_handler(patch_db, s3_client, is_deleted, output_transactions, monkeypatch):
    # Given
    create_sts()
    metadata = patch_db.banking_metadata.insert_one(
        {
            "client_id": "1",
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "banking_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "deposit": {"GBP": 2000},
                },
                {
                    "status": "Updating Status",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12346",
                    "file_date": "2022-02-24",
                },
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    patch_db.banking_file_details.insert_many(
        [
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4ff"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": "1",
                "banking_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": not is_deleted,
            },
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4fa"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": "1",
                "banking_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": is_deleted,
            },
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4fb"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": "1",
                "banking_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12346",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": is_deleted,
            },
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4fc"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": "1",
                "banking_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12346",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": is_deleted,
            },
        ]
    )
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    event = {"clientId": "TU123", "fileId": "12346", "isDeleted": is_deleted, "bankingId": str(metadata.inserted_id)}
    context = None

    # When
    result = banking_status_change_trigger_lambda.lambda_handler(event, context)

    # Then
    transactions = []
    for batch_no in range(ceil(len(output_transactions) / BATCH_SIZE)):
        data = s3_client.get_object(
            Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
            Key=f"banking-status-change/12346/test-id/{batch_no}.json",
        )
        transactions.extend(json.loads(data["Body"].read())["transaction_ids"])
    assert transactions == output_transactions
    assert result == {"statusCode": 200, "body": json.dumps("Step Function Triggered!")}
