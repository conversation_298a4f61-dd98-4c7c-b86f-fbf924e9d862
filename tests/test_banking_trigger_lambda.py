import importlib
import json
import boto3
from moto import mock_stepfunctions, mock_sts

banking_trigger_lambda = importlib.import_module("banking-trigger-lambda.banking-trigger-lambda")
simple_definition = (
    '{"Comment": "An example of the Amazon States Language using a choice state.",'
    '"StartAt": "DefaultState",'
    '"States": '
    '{"DefaultState": {"Type": "Fail","Error": "DefaultStateError","Cause": "No Matches!"}}}'
)
account_id = None


def _get_account_id():
    global account_id
    if account_id:
        return account_id
    sts = boto3.client("sts")
    identity = sts.get_caller_identity()
    account_id = identity["Account"]
    return account_id


def _get_default_role():
    return "arn:aws:iam::" + _get_account_id() + ":role/unknown_sf_role"


@mock_stepfunctions
@mock_sts
def create_sts():
    client = boto3.client("stepfunctions")
    sm = client.create_state_machine(
        name="banking-orchestration-dev", definition=str(simple_definition), roleArn=_get_default_role()
    )
    return sm["stateMachineArn"]


@mock_stepfunctions
@mock_sts
def test_lambda_handler():
    # Given
    create_sts()
    event = {
        "clientId": "TU123",
        "fileId": "eb438e16-3dbe-4ec5-bc1d-4fd1b1b8eef3_2022-03-02T06:35:08.794774",
        "replace": False,
    }
    context = None

    # When
    result = banking_trigger_lambda.lambda_handler(event, context)

    # Then
    assert result == {"statusCode": 200, "body": json.dumps("Step Function Triggered!")}
