from datetime import datetime
import importlib
import json
import os
from unittest.mock import patch
from freezegun import freeze_time
from mongomock import ObjectId
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_module = importlib.import_module("claim-aggregator-lambda.claim-aggregator-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_module.db
    claim_module.client.drop_database("db")


@freeze_time("Mar 1st, 2022")
def test_lambda_handler(patch_db, s3_client, monkeypatch):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    file_id = "12346"

    data = {
        "fileId": file_id,
        "clientId": str(client_id),
        "dataKey": "claim/persistor/output/12346.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
    }

    data_items = [
        {
            "clientId": str(client_id),
            "fileId": file_id,
            "itemCount": {"GBP": 1},
            "claimTotal": {"GBP": 100},
            "checks": {"GBP": 0},
            "checkedAmount": {"GBP": 0},
            "sftp": False,
            "sftpKey": "",
        },
        {
            "clientId": str(client_id),
            "fileId": file_id,
            "itemCount": {"GBP": 1, "USD": 2},
            "claimTotal": {"GBP": 100, "USD": 500},
            "checks": {"GBP": 0, "USD": 0},
            "checkedAmount": {"GBP": 0, "USD": 0},
            "sftp": False,
            "sftpKey": "",
        },
        {
            "clientId": str(client_id),
            "fileId": file_id,
            "itemCount": {"GBP": 1, "USD": 2, "NOK": 1},
            "claimTotal": {"GBP": 100, "USD": 500, "NOK": 400},
            "checks": {"GBP": 0, "USD": 0, "NOK": 0},
            "checkedAmount": {"GBP": 0, "USD": 0, "NOK": 0},
            "sftp": False,
            "sftpKey": "",
        },
    ]
    metadata = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "claim_total": {"GBP": 2000},
                },
                {
                    "status": "Scanning",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": file_id,
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 3, "USD": 4, "NOK": 1},
                    "claim_total": {"GBP": 300, "USD": 1000, "NOK": 400},
                    "checks": {"GBP": 0, "USD": 0, "NOK": 0},
                    "checked_amount": {"GBP": 0, "USD": 0, "NOK": 0},
                },
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    patch_db.claims_file_details.insert_many(
        [
            {"_id": ObjectId("621c3b23e22a9303b9b091ba"), "file_id": file_id},
            {"_id": ObjectId("621c3b23e22a9303b9b091bb"), "file_id": file_id},
            {"_id": ObjectId("621c3b23e22a9303b9b091bc"), "file_id": file_id},
            {"_id": ObjectId("621c3b23e22a9303b9b091bd"), "file_id": file_id},
        ]
    )
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )

    s3_client.put_object(
        Body=json.dumps(data_items), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )
    # When
    response = claim_module.lambda_handler(data, None)
    # Then
    claim_metadata = patch_db.claims_metadata.find_one({"_id": metadata.inserted_id}, projection={"_id": 0})

    assert claim_metadata == {
        "client_id": client_id,
        "claim_files": [
            {
                "status": "Cancelled by System",
                "file_name": "20220224-All -Claim -All anomalies.xlsx",
                "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                "file_id": "12345",
                "file_date": "2022-02-24",
                "item_count": {"GBP": 4},
                "claim_total": {"GBP": 2000},
            },
            {
                "status": "Scanning",
                "file_name": "20220224-All -Claim -All anomalies.xlsx",
                "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                "file_id": file_id,
                "file_date": "2022-02-24",
                "item_count": {"GBP": 3, "USD": 4, "NOK": 1},
                "claim_total": {"GBP": 300, "USD": 1000, "NOK": 400},
                "checks": {"GBP": 0, "USD": 0, "NOK": 0},
                "checked_amount": {"GBP": 0, "USD": 0, "NOK": 0},
            },
        ],
        "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        "updated_at": datetime(2022, 3, 1),
    }
    assert response == [
        {
            "batchNo": 0,
            "dataKey": "claim/aggregator/12346.json",
        },
        {
            "batchNo": 1,
            "dataKey": "claim/aggregator/12346.json",
        },
    ]
