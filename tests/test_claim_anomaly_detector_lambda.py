import json
import os
from mongomock import ObjectId
import pytest
from datetime import datetime
from freezegun import freeze_time
from unittest.mock import Mock, patch
import importlib
from custom_mongomock.mongo_client import CustomMongoClient

# from helpers.date_modifier import get_next_date

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_anomaly_detector_lambda = importlib.import_module(
        "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda"
    )


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_anomaly_detector_lambda, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_anomaly_detector_lambda.db
    claim_anomaly_detector_lambda.client.drop_database("db")


@freeze_time("Jan 14th, 2012")
def test_negative_funds_trust_sucess(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"balance": -100, "client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 500,
            "client_id": "1",
            "pax_count": 3,
            "departure_date": "2020-02-11",
            "file_id": "67555",
            "total_booking_value": 3000,
            "element": "performance",
            "deleted": "false",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {"description": "Negative cumulative Funds in Trust", "name": "Negative Funds in Trust"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Negative Funds in Trust"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.negative_funds_trust(
        transaction,
        trust,
        "67555",
        anomaly,
        "Negative Funds in Trust",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Negative Funds in Trust",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_negative_funds_trust_failure(patch_db):
    # Given
    patch_db.anomaly_claims.insert_one(
        {
            "client_id": "1",
            "booking_ref": "123",
            "anomaly_type": "Negative Funds in Trust",
            "deleted": False,
            "status": "Unresolved",
        }
    )
    patch_db.anomaly_banking.insert_one(
        {
            "client_id": "1",
            "booking_ref": "123",
            "anomaly_type": "Negative Funds in Trust",
            "deleted": False,
            "status": "Unresolved",
        }
    )
    patch_db.trust_fund_v2.insert_one({"balance": -0.0001, "client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 500,
            "client_id": "1",
            "pax_count": 3,
            "departure_date": "2020-02-11",
            "file_id": "67555",
            "total_booking_value": 3000,
            "element": "performance",
            "deleted": "false",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {"description": "Negative cumulative Funds in Trust", "name": "Negative Funds in Trust"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Negative Funds in Trust"})

    # When
    output = claim_anomaly_detector_lambda.negative_funds_trust(
        transaction,
        trust,
        "67555",
        anomaly,
        "Negative Funds in Trust",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )
    banking_anomaly = patch_db.anomaly_banking.find_one(
        {
            "client_id": "1",
            "booking_ref": "123",
            "anomaly_type": "Negative Funds in Trust",
            "deleted": False,
        },
        projection={"_id": 0},
    )
    claim_anomaly = patch_db.anomaly_claims.find_one(
        {"client_id": "1", "booking_ref": "123", "deleted": False, "anomaly_type": "Negative Funds in Trust"},
        projection={"_id": 0},
    )

    # Then
    assert output is None
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "client_id": "1",
            "anomaly_type": "Negative Funds in Trust",
            "status": "Resolved",
            "deleted": False,
            "updated_at": datetime(2012, 1, 14),
        }
    )
    assert banking_anomaly == (
        {
            "booking_ref": "123",
            "client_id": "1",
            "anomaly_type": "Negative Funds in Trust",
            "status": "Resolved",
            "deleted": False,
            "updated_at": datetime(2012, 1, 14),
        }
    )


@pytest.mark.parametrize("element", [("claim"), ("performance")])
@freeze_time("Jan 14th, 2012")
def test_funds_exceed_tbv_sucess(patch_db, element):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {"total_booking_value": 2000, "total_in_trust": 1000, "client_id": "1", "booking_ref": "123"}
    )
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 500,
            "client_id": "1",
            "pax_count": 3,
            "departure_date": "2020-02-11",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": "false",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds in Trust exceed/less than Total Booking Value",
            "name": "Funds in Trust Exceed/less than Total Booking Value",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Funds in Trust Exceed/less than Total Booking Value"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.funds_exceed_tbv(
        transaction,
        trust,
        "67555",
        anomaly,
        "Funds in Trust Exceed/less than Total Booking Value",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Funds in Trust Exceed/less than Total Booking Value",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )
    assert (trust["total_in_trust"] - trust["total_booking_value"]) != 0


@pytest.mark.parametrize(
    "total_in_trust,total_booking_value,claims_input",
    [
        (
            500,
            500,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-02-11",
                "file_id": "67555",
                "total_booking_value": 500,
                "element": "performance",
                "deleted": "false",
            },
        ),
        (
            500.001,
            500,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-02-11",
                "file_id": "67555",
                "total_booking_value": 500,
                "element": "performance",
                "deleted": "false",
            },
        ),
        (
            500.004,
            500,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-02-11",
                "file_id": "67555",
                "total_booking_value": 500,
                "element": "performance",
                "deleted": "false",
            },
        ),
        (
            500,
            500,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "element": None,
                "departure_date": "2020-02-11",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            500,
            None,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-02-11",
                "file_id": "67555",
                "total_booking_value": 500,
                "element": "performance",
                "deleted": "false",
            },
        ),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_funds_exceed_tbv_failure(patch_db, total_in_trust, total_booking_value, claims_input):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {
            "total_in_trust": total_in_trust,
            "total_booking_value": total_booking_value,
            "client_id": "1",
            "booking_ref": "123",
        }
    )
    patch_db.claims_file_details.insert_one(claims_input)
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds in Trust exceed/less than Total Booking Value",
            "name": "Funds in Trust Exceed/less than Total Booking Value",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Funds in Trust Exceed/less than Total Booking Value"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.funds_exceed_tbv(
        transaction,
        trust,
        "67555",
        anomaly,
        "Funds in Trust Exceed/less than Total Booking Value",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_multi_currency_booking_sucess(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"currency_code": "euro", "client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 500,
            "client_id": "1",
            "pax_count": 3,
            "departure_date": "2020-02-11",
            "file_id": "67555",
            "total_booking_value": 3000,
            "element": "performance",
            "deleted": "false",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {"description": "Multiple currencies detected for this booking", "name": "Multi-currency Booking"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Multi-currency Booking"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.multi_currency_booking(
        transaction,
        trust,
        "67555",
        anomaly,
        "Multi-currency Booking",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Multi-currency Booking",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_multi_currency_booking_failure(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"currency_code": "", "client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 500,
            "client_id": "1",
            "pax_count": 3,
            "departure_date": "2020-02-11",
            "file_id": "67555",
            "total_booking_value": 3000,
            "element": "performance",
            "deleted": "false",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {"description": "Multiple currencies detected for this booking", "name": "Multi-currency Booking"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Multi-currency Booking"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.multi_currency_booking(
        transaction,
        trust,
        "67555",
        anomaly,
        "Multi-currency Booking",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize("element", [("commission"), ("performance"), ("CoMmission"), ("PeRfoRmance")])
@freeze_time("Jan 14th, 2012")
def test_claim_before_return_sucess(patch_db, element):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    claim_metadata = patch_db.claims_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "*********-ABC-Claim.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2020-07-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": claim_metadata.inserted_id,
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 500,
            "client_id": "1",
            "pax_count": 3,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-13",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": "false",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Performance claim before client return",
            "name": "Claim Performance Before Return",
            "custom_field_name": "No.of Days",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 20})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Performance Before Return"})
    anomaly["custom_field_value"] = 20
    metadata = patch_db.claims_metadata.find_one({"client_id": "1"})
    file_date = metadata["claim_files"][-1]["file_date"]

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_before_return(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Performance Before Return",
        {"name": "Tripartite MA"},
        file_date,
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": claim_metadata.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Claim Performance Before Return",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )

    assert transaction["return_date"] > file_date


@pytest.mark.parametrize(
    "element,trust_type,claims_input",
    [
        (
            "performance",
            "Tripartite Tour Op",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-07-11",
                "return_date": "2012-01-14",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            "string",
            "Tripartite Tour Op",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            "string",
            "Tripartite Tour Op",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-07-11",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            "string",
            "Tripartite Tour Op",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-07-11",
                "return_date": None,
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            None,
            "Tripartite Tour Op",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            None,
            None,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": "1",
                "pax_count": 3,
                "departure_date": "2020-07-11",
                "return_date": None,
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_claim_before_return_failure(patch_db, element, trust_type, claims_input):
    # Given
    patch_db.trust_fund_v2.insert_one({"total_in_trust": 1000, "client_id": "1", "booking_ref": "123"})
    patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId("620a8020b32a49e8c133a2da"),
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "*********-ABC-Claim.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2020-07-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )
    patch_db.claims_file_details.insert_one(claims_input)

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Performance claim before client return",
            "name": "Claim Performance Before Return",
            "custom_field_name": "No.of Days",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 20})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Performance Before Return"})
    anomaly["custom_field_value"] = 20
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    claim_lookup_trust = patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    claim_lookup_trust["name"] = trust_type
    metadata = patch_db.claims_metadata.find_one({"client_id": "1"})
    file_date = metadata["claim_files"][-1]["file_date"]

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_before_return(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Performance Before Return",
        {"name": "Tripartite MA"},
        file_date,
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize("element", ["Deposit", "Performance"])
@freeze_time("Jan 14th, 2012")
def test_claim_exceed_per_pax_sucess(patch_db, element):
    # Given
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 5000,
            "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-13",
            "file_id": "67555",
            "element": element,
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Cost Per Pax",
            "description": "Claim value exceeds the maximum allowed for the number of passengers",
            "name": "Claim Exceeds Cost Per Pax",
        }
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": ObjectId("620a8020b32a49e8c133a2d3"), "custom_field_value": 50}
    )
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("620a8020b32a49e8c133a2d3"),
            "client_id": "1",
            "type_of_trust_account": lookup_trust.inserted_id,
        }
    )

    transaction = patch_db.claims_file_details.find_one(
        {"file_id": "67555", "client_id": ObjectId("620a8020b32a49e8c133a2d3")}
    )
    trust = patch_db.trust_fund_v2.find_one({"client_id": ObjectId("620a8020b32a49e8c133a2d3"), "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds Cost Per Pax"})
    anomaly["custom_field_value"] = 50
    anomaly["elements"] = [element]
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    claim_lookup_trust = patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_exceed_per_pax(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Exceeds Cost Per Pax",
        claim_lookup_trust,
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Claim Exceeds Cost Per Pax",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "element,claims_input",
    [
        (
            "string",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": ObjectId("620a8020b32a49e8c133a2df"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
        (
            "Deposit",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": ObjectId("620a8020b32a49e8c133a2df"),
                "pax_count": None,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
        (
            "Deposit",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": ObjectId("620a8020b32a49e8c133a2df"),
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
        (
            "Deposit",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "element": "Deposit",
                "client_id": ObjectId("620a8020b32a49e8c133a2df"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
        (
            None,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": ObjectId("620a8020b32a49e8c133a2df"),
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_claim_exceed_per_pax_failure(patch_db, element, claims_input):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": ObjectId("620a8020b32a49e8c133a2df"), "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(claims_input)

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Cost Per Pax",
            "description": "Claim value exceeds the maximum allowed for the number of passengers",
            "name": "Claim Exceeds Cost Per Pax",
        }
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": ObjectId("620a8020b32a49e8c133a2df"), "custom_field_value": 150}
    )
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    basic_info = patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("620a8020b32a49e8c133a2df"),
            "client_id": "1",
            "type_of_trust_account": lookup_trust.inserted_id,
        }
    )

    transaction = patch_db.claims_file_details.find_one(
        {"file_id": "67555", "client_id": ObjectId("620a8020b32a49e8c133a2df")}
    )
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": ObjectId("620a8020b32a49e8c133a2df"), "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds Cost Per Pax"})
    anomaly["custom_field_value"] = 150
    anomaly["elements"] = [element]
    client_basic_info = patch_db.client_basic_info.find_one({"_id": basic_info.inserted_id, "client_id": "1"})
    claim_lookup_trust = patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_exceed_per_pax(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Exceeds Cost Per Pax",
        claim_lookup_trust,
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize("element,trust_type", [("deposit", "Tripartite Tour Op"), ("deposit", "Tripartite MA")])
@freeze_time("Jan 14th, 2012")
def test_claim_exceed_max_sucess(patch_db, element, trust_type):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {"balance": 40, "client_id": "1", "booking_ref": "123", "total_in_trust": 1000, "total_claimed": 251}
    )  # total_in_trust is the amount after claiming
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "element": element,
            "amount": 101,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": "false",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Max % Of Elements",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "name": "Max Claim % Exceeded",
            "dependencies": ["elements", "trust_types"],
        }
    )
    look_up_trust = patch_db.trust_type.insert_one({"name": trust_type})
    patch_db.client_anomaly.insert_one(
        {
            "elements": [element],
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "custom_field_value": 25,
            "trust_types": [look_up_trust.inserted_id],
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Max Claim % Exceeded"})
    anomaly["custom_field_value"] = 25
    anomaly["elements"] = ["deposit"]
    anomaly["trust_types"] = [look_up_trust.inserted_id]

    # When
    claim_anomaly = claim_anomaly_detector_lambda.max_claim_exceed(
        transaction,
        trust,
        "67555",
        anomaly,
        "Max Claim % Exceeded",
        {"_id": look_up_trust.inserted_id},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Max Claim % Exceeded",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "element, trust_type, amount",
    [
        ("claim", "ATOL Standard", 600),
        ("claim", "ATOL Standard", 400),
        (None, "Tripartite MA", 400),
        ("deposit", "Tripartite MA", 50),
        (None, None, 400),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_claim_exceed_max_failure(patch_db, element, trust_type, amount):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {"balance": 60, "client_id": "1", "booking_ref": "123", "total_in_trust": 1000, "total_claimed": 150}
    )  # total_in_trust is the amount after claiming
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": amount,
            "client_id": "1",
            "pax_count": 5,
            "element": element,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Max % Of Elements",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "name": "Max Claim % Exceeded",
            "dependencies": ["elements"],
        }
    )
    look_up_trust = patch_db.trust_type.insert_one({"name": trust_type})
    patch_db.client_anomaly.insert_one(
        {
            "elements": [element],
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "custom_field_value": 25,
            "trust_types": [look_up_trust.inserted_id],
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Max Claim % Exceeded"})
    anomaly["custom_field_value"] = 25
    anomaly["elements"] = [element]
    anomaly["trust_types"] = [look_up_trust.inserted_id]

    # When
    claim_anomaly = claim_anomaly_detector_lambda.max_claim_exceed(
        transaction,
        trust,
        "67555",
        anomaly,
        "Max Claim % Exceeded",
        {"_id": look_up_trust.inserted_id},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize("element,trust_type", [("Deposit", "Tripartite Tour Op"), ("Deposit", "Tripartite MA")])
@freeze_time("Jan 14th, 2012")
def test_claim_exceeds_apb_sucess(patch_db, element, trust_type):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 400,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-105",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": "false",
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Percentage (%)",
            "description": "Claim amount is more than the agreed amount/%age amount per booking",
            "name": "Claim Exceeds agreed % per booking",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 50})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds agreed % per booking"})
    anomaly["custom_field_value"] = 50
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    claim_lookup_trust = patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    claim_lookup_trust["name"] = trust_type

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_exceeds_apb(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Exceeds agreed % per booking",
        {"name": trust_type},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Claim Exceeds agreed % per booking",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )

    assert (
        transaction["amount"]
        > (transaction["total_booking_value"] / transaction["pax_count"]) * anomaly["custom_field_value"] / 100
    )


@pytest.mark.parametrize(
    "element,trust_type,claims_input",
    [
        (
            "claim",
            "Tripartite MA",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-105",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            "deposit",
            "ATOL Standard",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-105",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": "false",
            },
        ),
        (
            "deposit",
            "Tripartite MA",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-105",
                "file_id": "67555",
                "deleted": "false",
            },
        ),
        (
            "deposit",
            "Tripartite MA",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "total_booking_value": None,
                "return_date": "2020-08-105",
                "file_id": "67555",
                "deleted": "false",
            },
        ),
        (
            "deposit",
            None,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "total_booking_value": None,
                "return_date": "2020-08-105",
                "file_id": "67555",
                "deleted": "false",
            },
        ),
        (
            "deposit",
            None,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": None,
                "departure_date": "2020-07-11",
                "total_booking_value": None,
                "return_date": "2020-08-105",
                "file_id": "67555",
                "deleted": "false",
            },
        ),
        (
            "deposit",
            None,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "departure_date": "2020-07-11",
                "return_date": "2020-08-105",
                "file_id": "67555",
                "deleted": "false",
            },
        ),
        (
            None,
            None,
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "departure_date": "2020-07-11",
                "return_date": "2020-08-105",
                "file_id": "67555",
                "deleted": "false",
            },
        ),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_claim_exceeds_apb_failure(patch_db, element, trust_type, claims_input):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(claims_input)
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Percentage (%)",
            "description": "Claim amount is more than the agreed amount/%age amount per booking",
            "name": "Claim Exceeds agreed % per booking",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 50})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds agreed % per booking"})
    anomaly["custom_field_value"] = 50
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    claim_lookup_trust = patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    claim_lookup_trust["name"] = trust_type

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_exceeds_apb(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Exceeds agreed % per booking",
        {"name": trust_type},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize("element,trust_type", [("deposit", "Tripartite Tour Op"), ("balance", "Tripartite MA")])
@freeze_time("Jan 14th, 2012")
def test_max_cap_success(patch_db, element, trust_type, monkeypatch):
    # Given
    file_id = "67555"
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})
    metadata = patch_db.claims_metadata.insert_one({"claim_files": [{"file_id": "1234"}, {"file_id": file_id}]})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "claims_id": metadata.inserted_id,
            "element": element,
            "amount": 600,
            "client_id": "1",
            "return_date": "2024-08-10",
            "booking_date": "2024-06-01",
            "file_id": file_id,
            "deleted": False,
        }
    )
    transaction = patch_db.claims_file_details.find_one({"client_id": "1"})
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Max Cap Amount Per Claim",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "name": "Max Cap Claim Anomaly (Rollover)",
            "dependencies": ["elements", "trust_types"],
        }
    )
    look_up_trust = patch_db.trust_type.insert_one({"name": trust_type})
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "custom_field_value": 500,
            "cap_amount": 500,
            "from_date": "2024-05-01",
            "to_date": "2024-06-24",
            "elements": ["deposit", "balance"],
            "trust_types": [look_up_trust.inserted_id],
        }
    )
    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": None, "total_claimed": 600}]),
    )

    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Max Cap Claim Anomaly (Rollover)"})
    anomaly["custom_field_value"] = 500
    anomaly["elements"] = ["deposit", "balance"]
    anomaly["trust_types"] = [look_up_trust.inserted_id]
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    patch_db.client_anomaly.find_one({"client_id": "1"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.max_cap_anomaly(
        transaction,
        trust,
        file_id,
        anomaly,
        "Max Cap Claim Anomaly (Rollover)",
        {"_id": look_up_trust.inserted_id},
        "2022-06-27",
    )

    # Then
    claims_metadata = patch_db.claims_metadata.find_one({"_id": metadata.inserted_id})
    assert claims_metadata["claim_files"][-1]["max_cap_claim_anomaly_calculated"] is True
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": metadata.inserted_id,
            "file_id": file_id,
            "anomaly_type": "Max Cap Claim Anomaly (Rollover)",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "claims_input",
    [
        {
            "booking_ref": "123",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "element": "AsPp",
            "amount": 600,
            "client_id": "1",
            "return_date": "2020-08-15",
            "file_id": "67555",
            "deleted": "false",
        },
        {
            "booking_ref": "123",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "element": "ASPP",
            "amount": 600,
            "client_id": "1",
            "file_id": "67555",
            "deleted": "false",
        },
        {
            "booking_ref": "123",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "element": "ASPP",
            "amount": 600,
            "client_id": "1",
            "return_date": "2020-08-15",
            "file_id": "67555",
            "deleted": "false",
        },
    ],
)
@freeze_time("Jan 14th, 2012")
def test_max_cap_failure(patch_db, monkeypatch, claims_input):
    # Given
    file_id = claims_input["file_id"]
    patch_db.anomaly_claims.insert_one(
        {
            "client_id": "1",
            "booking_ref": "123",
            "anomaly_type": "Max Cap Claim Anomaly (Rollover)",
            "deleted": False,
            "status": "Unresolved",
        }
    )
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})
    metadata = patch_db.claims_metadata.insert_one(
        {"_id": claims_input["claims_id"], "claim_files": [{"file_id": "1234"}, {"file_id": file_id}]}
    )
    patch_db.claims_file_details.insert_one(claims_input)
    transaction = patch_db.claims_file_details.find_one({"client_id": "1"})
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Max Cap Amount Per Claim",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "name": "Max Cap Claim Anomaly (Rollover)",
            "dependencies": ["elements", "trust_types"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "elements": ["deposit"],
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "custom_field_value": 700,
            "trust_types": [lookup_trust.inserted_id],
        }
    )

    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Max Cap Claim Anomaly (Rollover)"})
    anomaly["custom_field_value"] = 700
    anomaly["elements"] = ["deposit"]
    patch_db.client_anomaly.find_one({"client_id": "1"})

    # When
    output = claim_anomaly_detector_lambda.max_cap_anomaly(
        transaction,
        trust,
        file_id,
        anomaly,
        "Max Cap Claim Anomaly (Rollover)",
        {"_id": lookup_trust.inserted_id},
        "2022-06-27",
    )
    claim_anomaly = patch_db.anomaly_claims.find_one(
        {
            "client_id": "1",
            "anomaly_type": "Max Cap Claim Anomaly (Rollover)",
            "deleted": False,
        },
        projection={"_id": 0},
    )

    # Then
    claims_metadata = patch_db.claims_metadata.find_one({"_id": metadata.inserted_id})
    assert "max_cap_claim_anomaly_calculated" not in claims_metadata["claim_files"][-1]
    assert output is None
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "client_id": "1",
            "anomaly_type": "Max Cap Claim Anomaly (Rollover)",
            "status": "Unresolved",
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_duplicate_amount_sucess(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_many(
        [
            {
                "booking_ref": "123",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "amount": 500,
                "client_id": "1",
                "element": "claim",
                "file_id": "67555",
                "deleted": False,
            },
            {
                "booking_ref": "123",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "amount": 500,
                "client_id": "1",
                "element": "claim",
                "file_id": "67555",
                "deleted": False,
            },
        ]
    )
    lookup = patch_db.lookup_anomaly.insert_one({"description": "Duplicate claim value", "name": "Duplicate"})
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Duplicate"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.duplicate_amount(
        transaction,
        trust,
        "67555",
        anomaly,
        "Duplicate",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": transaction["_id"],
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Duplicate",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_duplicate_amount_failure(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_many(
        [
            {
                "booking_ref": "123",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "amount": 500,
                "client_id": "1",
                "element": "claim",
                "file_id": "67555",
                "deleted": False,
            },
            {
                "booking_ref": "123",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "amount": 500,
                "client_id": "1",
                "element": "deposit",
                "file_id": "67555",
                "deleted": False,
            },
        ]
    )
    lookup = patch_db.lookup_anomaly.insert_one({"description": "Duplicate claim value", "name": "Duplicate"})
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Duplicate"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.duplicate_amount(
        transaction,
        trust,
        "67555",
        anomaly,
        "Duplicate",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize(
    "element,trust_type",
    [("Performance", "Tripartite Tour Op"), ("Balance", "Tripartite MA"), ("Claim", "Tripartite MA")],
)
@freeze_time("Jan 14th, 2012")
def test_claim_early_departure_date_sucess(patch_db, element, trust_type):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 400,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2012-03-14",
            "return_date": "2020-08-10",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "The claim was attempted too early from the configured limits",
            "name": "Claim Too Early For Departure Date",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 20})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Too Early For Departure Date"})
    anomaly["custom_field_value"] = 20
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    claim_lookup_trust = patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    claim_lookup_trust["name"] = trust_type

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_early_departure_date(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Too Early For Departure Date",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Claim Too Early For Departure Date",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "element,trust_type,claims_input",
    [
        (
            "string",
            "ASSP",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2012-01-14",
                "return_date": "2020-08-10",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
        (
            "string",
            "string",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2012-01-14",
                "return_date": "2020-08-10",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
        (
            "Performance",
            "Tripartite Tour Op",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": None,
                "return_date": "2020-08-10",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
        (
            "Performance",
            "Tripartite Tour Op",
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 400,
                "client_id": "1",
                "pax_count": 5,
                "departure_date": "2012-01-14",
                "return_date": "2020-08-10",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_claim_early_departure_date_failure(patch_db, element, trust_type, claims_input):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(claims_input)

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "The claim was attempted too early from the configured limits",
            "name": "Claim Too Early For Departure Date",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 20})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Too Early For Departure Date"})
    anomaly["custom_field_value"] = 20
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    claim_lookup_trust = patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    claim_lookup_trust["name"] = trust_type

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_early_departure_date(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Too Early For Departure Date",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize(
    "element,insurance",
    [
        (
            ("Cruise"),
            (
                {
                    "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
                    "policy_no": "222",
                    "expiry_date": None,
                    "client_id": "1",
                    "provider": "ABCD",
                }
            ),
        ),
        (
            ("ASPP"),
            (
                {
                    "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
                    "policy_no": "222",
                    "expiry_date": datetime(2011, 1, 14),
                    "client_id": "1",
                    "provider": "ABCD",
                }
            ),
        ),
        (("Flights"), ({})),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_insurance_cover_in_date_sucess(patch_db, element, insurance):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 400,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-105",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Insurance date already expired",
            "name": "Insurance Cover In Date",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 50})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})
    insurance = patch_db.client_insurance_info.insert_one(insurance)

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Insurance Cover In Date"})
    anomaly["elements"] = ["BSP", "Flight", "Flights", "Cruise", "ASPP"]
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.insurance_cover_in_date(
        transaction,
        trust,
        "67555",
        anomaly,
        "Insurance Cover In Date",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Insurance Cover In Date",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "element,insurance",
    [
        (
            ("Cruise"),
            (
                {
                    "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
                    "policy_no": "222",
                    "expiry_date": datetime(2013, 1, 14),
                    "client_id": "1",
                    "provider": "ABCD",
                }
            ),
        ),
        (
            ("ASPP"),
            (
                {
                    "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
                    "policy_no": "222",
                    "expiry_date": datetime(2013, 1, 14),
                    "client_id": "1",
                    "provider": "ABCD",
                }
            ),
        ),
        (
            ("Flights"),
            (
                {
                    "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
                    "policy_no": "222",
                    "expiry_date": datetime(2013, 1, 14),
                    "client_id": "1",
                    "provider": "ABCD",
                }
            ),
        ),
    ],
)
@freeze_time("Jan 14th, 2012")
def test_insurance_cover_in_date_failure(patch_db, element, insurance):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 400,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-105",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Insurance date already expired",
            "name": "Insurance Cover In Date",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 50})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})
    insurance = patch_db.client_insurance_info.insert_one(insurance)

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Insurance Cover In Date"})
    anomaly["elements"] = ["BSP", "Flight", "Flights", "Cruise", "ASPP"]
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.insurance_cover_in_date(
        transaction,
        trust,
        "67555",
        anomaly,
        "Insurance Cover In Date",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize(
    "total_claimed,supplier_names", [(1000, "Aegean Air"), (1500, "aegean air"), (100, "Aegean Air, abcd")]
)
@freeze_time("Jan 14th, 2012")
def test_claim_supplier_list(patch_db, monkeypatch, total_claimed, supplier_names):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_metadata.insert_one(
        {
            "client_id": "1",
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "67555",
                    "file_date": "2023-02-24",
                    "item_count": {"GBP": 4},
                    "claim_total": {"GBP": 2000},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "amount": 1000,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": supplier_names,
            "element": "Flights",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Claim SupplierList Anomaly",
            "from_date": "From Date",
            "to_date": "To Date",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "elements": ["Flights", "BSP"],
            "from_date": "2023-01-02",
            "to_date": "2023-08-09",
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": "1",
        }
    )

    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": "aegean air", "total_claimed": total_claimed}]),
    )
    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "Aegean Air", "cap_amount": 100},
                {"supplier_name": "RCCL (Royal Caribbean, Celebrity, Azamara)", "cap_amount": 200},
            ],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim SupplierList Anomaly"})
    anomaly["from_date"] = "2023-01-02"
    anomaly["to_date"] = "2023-12-02"
    anomaly["elements"] = ["Flights", "BSP"]
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    patch_db.client_files.find_one({"client_id": "1"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_supplier_list(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim SupplierList Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
            "anomaly_type": "Claim SupplierList Anomaly",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "total_claimed,supplier_names", [(1000, "Aegean Air"), (1500, "aegean air"), (100, "Aegean Air, abcd")]
)
@freeze_time("Jan 14th, 2012")
def test_claim_supplier_list_insurance_empty(patch_db, monkeypatch, total_claimed, supplier_names):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_metadata.insert_one(
        {
            "client_id": "1",
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "********-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "67555",
                    "file_date": "2023-02-24",
                    "item_count": {"GBP": 4},
                    "claim_total": {"GBP": 2000},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": supplier_names,
            "element": "Flights",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Claim SupplierList Anomaly",
            "from_date": "From Date",
            "to_date": "To Date",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "elements": ["Flights", "BSP"],
            "from_date": "2023-01-02",
            "to_date": "2023-08-09",
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "Aegean Air", "cap_amount": 100},
                {"supplier_name": "RCCL (Royal Caribbean, Celebrity, Azamara)", "cap_amount": 200},
            ],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )
    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": "Royal Caribbean", "total_claimed": total_claimed}]),
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.client_anomaly.find_one({"client_id": "1"})
    anomaly["from_date"] = "2023-01-02"
    anomaly["to_date"] = "2023-12-02"
    anomaly["elements"] = ["Flights", "BSP"]

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_supplier_list(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim SupplierList Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize(
    "claims_input",
    [
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Deposit",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": None,
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
    ],
)
@freeze_time("Jan 14th, 2012")
def test_claim_supplier_list_element_failure(patch_db, claims_input):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(claims_input)
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Claim SupplierList Anomaly",
            "from_date": "From Date",
            "to_date": "To Date",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "elements": ["Flights", "BSP"],
            "from_date": "2023-01-02",
            "to_date": "2023-08-09",
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": "1",
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "Aegean Air", "cap_amount": 100},
                {"supplier_name": "RCCL (Royal Caribbean, Celebrity, Azamara)", "cap_amount": 200},
            ],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim SupplierList Anomaly"})
    anomaly["from_date"] = "2023-01-02"
    anomaly["to_date"] = "2023-12-02"
    anomaly["elements"] = ["Flights", "BSP"]
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    patch_db.client_files.find_one({"client_id": "1"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_supplier_list(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim SupplierList Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_claim_supplier_list_failure(patch_db):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "Aegean Air",
            "element": "Deposit",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Claim SupplierList Anomaly",
            "from_date": "From Date",
            "to_date": "To Date",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "elements": ["Flights", "BSP"],
            "from_date": "2023-01-02",
            "to_date": "2023-08-09",
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": "1",
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "Aegean Air", "cap_amount": 100},
                {"supplier_name": "RCCL (Royal Caribbean, Celebrity, Azamara)", "cap_amount": 200},
            ],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim SupplierList Anomaly"})
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    patch_db.client_files.find_one({"client_id": "1"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_supplier_list(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim SupplierList Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_claim_supplier_list_iglu_supplier_name_mapping(patch_db, monkeypatch):
    patch_db.trust_fund_v2.insert_one({"client_id": ObjectId("32cc1b5116144e88d93dba50"), "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "royal caribbean",
            "element": "Flights",
            "type": "Direct",
            "amount": 2000,
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Claim SupplierList Anomaly",
            "from_date": "From Date",
            "to_date": "To Date",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
            "from_date": "2023-01-02",
            "to_date": "2023-08-09",
            "elements": ["Flights"],
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "RCCL (Royal Caribbean, Celebrity Cruises)", "cap_amount": 500},
            ],
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )

    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": "royal caribbean", "total_claimed": 2000}]),
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": ObjectId("32cc1b5116144e88d93dba50"), "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim SupplierList Anomaly"})
    anomaly["from_date"] = "2023-01-02"
    anomaly["to_date"] = "2023-12-02"
    anomaly["elements"] = ["Flights"]
    patch_db.client_insurance_info.find_one({"client_id": ObjectId("32cc1b5116144e88d93dba50")})
    patch_db.client_files.find_one({"client_id": ObjectId("32cc1b5116144e88d93dba50")})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_supplier_list(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim SupplierList Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == {
        "booking_ref": transaction["booking_ref"],
        "transaction_id": transaction["_id"],
        "client_id": transaction["client_id"],
        "claims_id": transaction["claims_id"],
        "file_id": "67555",
        "anomaly_type": "Claim SupplierList Anomaly",
        "anomaly_id": anomaly["_id"],
        "status": "Unresolved",
        "deleted": False,
        "created_at": datetime(2012, 1, 14),
        "updated_at": datetime(2012, 1, 14),
    }


@freeze_time("Jan 14th, 2012")
def test_claim_supplier_list_iglu_supplier_name_mapping_failure(patch_db, monkeypatch):
    patch_db.trust_fund_v2.insert_one({"client_id": ObjectId("32cc1b5116144e88d93dba50"), "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "Cunard",
            "element": "Flights",
            "type": "Direct",
            "amount": 2000,
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Claim SupplierList Anomaly",
            "from_date": "From Date",
            "to_date": "To Date",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
            "elements": ["Flights", "BSP"],
            "from_date": "2023-01-02",
            "to_date": "2023-08-09",
            "element": ["Flights"],
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "Carnival UK (P&O, Princess Cruises, Cunard)", "cap_amount": 5000},
            ],
            "client_id": ObjectId("32cc1b5116144e88d93dba50"),
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )

    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": "cunard", "total_claimed": 2000}]),
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": ObjectId("32cc1b5116144e88d93dba50"), "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim SupplierList Anomaly"})
    anomaly["from_date"] = "2023-01-02"
    anomaly["to_date"] = "2023-12-02"
    anomaly["elements"] = ["Flights", "BSP"]
    patch_db.client_insurance_info.find_one({"client_id": ObjectId("32cc1b5116144e88d93dba50")})
    patch_db.client_files.find_one({"client_id": ObjectId("32cc1b5116144e88d93dba50")})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_supplier_list(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim SupplierList Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize(
    "total_claimed,supplier_names", [(1000, "Aegean Air"), (1000, "aegean air"), (1000, "Aegean Air, abcd")]
)
@freeze_time("Jan 14th, 2012")
def test_safi_insurance_tracker(patch_db, monkeypatch, total_claimed, supplier_names):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2022-10-26",
            "supplier_ref": "string",
            "supplier_names": supplier_names,
            "amount": 1000,
            "element": "Flights",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "To check if the amount claimed for a supplier exceeds the configured cap amount",
            "name": "SAFI Insurance Tracker",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": "1",
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "Aegean Air", "cap_amount": 100},
                {"supplier_name": "RCCL (Royal Caribbean, Celebrity, Azamara)", "cap_amount": 200},
            ],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )
    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": "aegean air", "total_claimed": total_claimed}]),
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "SAFI Insurance Tracker"})
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    patch_db.client_files.find_one({"client_id": "1"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.safi_insurance_tracker(
        transaction,
        trust,
        "67555",
        anomaly,
        "SAFI Insurance Tracker",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )
    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
            "anomaly_type": "SAFI Insurance Tracker",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
            "supplier_name": "aegean air",
        }
    )


@pytest.mark.parametrize(
    "total_claimed,supplier_names,cap_amount", [(50, "Aegean Air", 100), (150, "abcd", 100), (150, "abcd", None)]
)
@freeze_time("Jan 14th, 2012")
def test_safi_insurance_tracker_faliure(patch_db, monkeypatch, total_claimed, supplier_names, cap_amount):
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": supplier_names,
            "amount": 50,
            "element": "Flights",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "To check if the amount claimed for that supplier exceeds the configured cap amount.",
            "name": "SAFI Insurance Tracker",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )
    patch_db.client_insurance_info.insert_one(
        {
            "expiry_date": datetime(2011, 1, 14),
            "policy_no": "222",
            "files": [{"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dfh"}],
            "supplier_list_file": {"file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj"},
            "provider": "ABCD",
            "client_id": "1",
        }
    )

    patch_db.client_files.insert_one(
        {
            "supplier_list": [
                {"supplier_name": "Aegean Air", "cap_amount": cap_amount},
                {"supplier_name": "RCCL (Royal Caribbean, Celebrity, Azamara)", "cap_amount": 200},
            ],
            "client_id": "1",
            "file_name": "Listofapprovedsuppliers.xlsx",
            "file_id": "be761366-9579-4e0d-890b-8ab1e6b82dhj",
        }
    )
    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": "Aegean Air", "total_claimed": total_claimed}]),
    )
    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "SAFI Insurance Tracker"})
    patch_db.client_insurance_info.find_one({"client_id": "1"})
    patch_db.client_files.find_one({"client_id": "1"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.safi_insurance_tracker(
        transaction, trust, "67555", anomaly, "SAFI Insurance Tracker", {"name": "Tripartite MA"}, "2022-06-27"
    )

    # Then
    assert claim_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_commission_claim_anomaly(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2013-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Commission",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
            "amount": 1000,
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds with Commission element type shall be processed only if the return date is in the past.",
            "name": "Commission Claim Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "custom_field_value": 1,
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly_types = patch_db.lookup_anomaly.find_one({"name": "Commission Claim Anomaly"})
    anomaly_types["custom_field_value"] = 1
    patch_db.client_anomaly.find_one({"client_id": "1"})
    # when
    claim_anomaly = claim_anomaly_detector_lambda.commision_claim_anomaly(
        transaction,
        trust,
        "67555",
        anomaly_types,
        "Commission Claim Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )
    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
            "anomaly_type": "Commission Claim Anomaly",
            "anomaly_id": anomaly_types["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "claims_input",
    [
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2013-02-10",
            "departure_date": "2011-04-20",
            "return_date": "2011-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Commission",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": None,
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2013-02-10",
            "departure_date": None,
            "return_date": "2011-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Commission",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "return_date": "2013-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Commission",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2013-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Commission",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
            "amount": -1000,
        },
    ],
)
@freeze_time("Jan 14th, 2012")
def test_commission_claim_anomaly_failure(patch_db, claims_input):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(claims_input)
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "Funds with Commission element type shall be processed only if the return date is in the past.",
            "name": "Commission Claim Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "custom_field_value": 1,
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Commission Claim Anomaly"})
    client_anomalies = patch_db.client_anomaly.find_one({"client_id": "1"})
    # when
    claim_anomaly = claim_anomaly_detector_lambda.commision_claim_anomaly(
        transaction,
        trust,
        "67555",
        client_anomalies,
        anomaly,
        "Commission Claim Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )
    # Then
    assert claim_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_cruise_claim_anomaly_success(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "element": "Cruise",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 400,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2012-02-05",
            "return_date": "2020-08-10",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "The claim was attempted too early from the configured limits",
            "name": "Cruise Claim Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 20})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Cruise Claim Anomaly"})
    anomaly["custom_field_value"] = 20
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.cruise_claim_anomaly(
        transaction,
        trust,
        "67555",
        anomaly,
        "Cruise Claim Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Cruise Claim Anomaly",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@pytest.mark.parametrize(
    "claims_input",
    [
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "element": "Cruise",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 400,
            "client_id": "1",
            "pax_count": 5,
            "departure_date": "2012-01-14",
            "return_date": "2020-08-10",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "departure_date": "2022-04-20",
            "return_date": "2021-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": None,
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2013-02-10",
            "departure_date": None,
            "return_date": "2011-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Cruise",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
        {
            "booking_ref": "123",
            "lead_pax": "Mr Charles Diamond",
            "pax_count": 2,
            "booking_date": "2020-02-10",
            "return_date": "2013-10-26",
            "supplier_ref": "string",
            "supplier_names": "abcd",
            "element": "Commission",
            "type": "Direct",
            "client_id": "1",
            "claims_id": ObjectId("621c38cae22a9303b9b091b2"),
            "file_id": "67555",
        },
    ],
)
@freeze_time("Jan 14th, 2012")
def test_cruise_claim_anomaly_failure(patch_db, claims_input):
    # Given
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    patch_db.claims_file_details.insert_one(claims_input)

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "The claim was attempted too early from the configured limits",
            "name": "Cruise Claim Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 20})
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Cruise Claim Anomaly"})
    anomaly["custom_field_value"] = 20
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.cruise_claim_anomaly(
        transaction,
        trust,
        "67555",
        anomaly,
        "Cruise Claim Anomaly",
        {"name": "Tripartite MA"},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize("element,trust_type", [("deposit", "Tripartite Tour Op"), ("balance", "Tripartite MA")])
@freeze_time("Jan 14th, 2012")
def test_claim_exceed_cost_sucess(patch_db, element, trust_type):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {
            "balance": 40,
            "client_id": ObjectId("620a8020b32a49e8c133a2db"),
            "booking_ref": "123",
            "total_in_trust": 1000,
            "total_claimed": 251,
            "total_booking_value": 1000,
        }
    )  # total_in_trust is the amount after claiming
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "element": element,
            "amount": 101,
            "client_id": ObjectId("620a8020b32a49e8c133a2db"),
            "pax_count": 5,
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim Exceeds Cost %",
            "description": "Amount exceeds configured percentage of Total Booking Value.",
            "custom_field_name": "Max % Of Total booking value",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "elements": ["deposit", "balance"],
            "anomaly_id": lookup.inserted_id,
            "client_id": ObjectId("620a8020b32a49e8c133a2db"),
            "custom_field_value": 10,
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    transaction["element"] = element
    trust = patch_db.trust_fund_v2.find_one({"client_id": ObjectId("620a8020b32a49e8c133a2db"), "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds Cost %"})
    anomaly["custom_field_value"] = 10
    anomaly["elements"] = ["deposit", "balance"]

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_exceed_cost(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Exceeds Cost %",
        {"name": trust_type},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": ObjectId("620a8020b32a49e8c133a2db"),
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Claim Exceeds Cost %",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_claim_exceed_cost_failure(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {
            "balance": 60,
            "client_id": ObjectId("620a8020b32a49e8c133a2db"),
            "booking_ref": "123",
            "total_in_trust": 1000,
            "total_claimed": 90,
            "total_booking_value": 1000,
        }
    )  # total_in_trust is the amount after claiming
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 40,
            "client_id": ObjectId("620a8020b32a49e8c133a2db"),
            "pax_count": 5,
            "element": "deposit",
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "total_booking_value": 1000,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim Exceeds Cost %",
            "description": "Amount exceeds configured percentage of Total Booking Value.",
            "custom_field_name": "Max % Of Total booking value",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "elements": ["deposit"],
            "anomaly_id": lookup.inserted_id,
            "client_id": ObjectId("620a8020b32a49e8c133a2db"),
            "custom_field_value": 10,
        }
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": ObjectId("620a8020b32a49e8c133a2db"), "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Claim Exceeds Cost %"})
    anomaly["custom_field_value"] = 10
    anomaly["elements"] = ["deposit"]

    # When
    claim_anomaly = claim_anomaly_detector_lambda.claim_exceed_cost(
        transaction,
        trust,
        "67555",
        anomaly,
        "Claim Exceeds Cost %",
        {"name": None},
        "2022-06-27",
    )

    # Then
    assert claim_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_claim_before_t_plus_x_days(patch_db):
    # Given
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "977957",
            "lead_pax": "Parker",
            "pax_count": 2,
            "booking_date": "2022-03-27",
            "departure_date": "2022-08-07",
            "return_date": "2022-08-22",
            "currency_code": "GBP",
            "amount": -2500.001,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Card",
            "days_to_process": 0,
            "total_booking_value": 3718,
            "supplier_ref": "None",
            "supplier_names": "None",
            "payment_date": "2023-05-23",
            "booking_status": "Active",
            "client_id": ObjectId("623c1a14c1886d1dd0e6f186"),
            "banking_id": ObjectId("6243f855f71a9b05567ed31f"),
            "file_id": "123",
            "deleted": False,
            "nights": 15,
            "status": "Cancelled",
        }
    ),
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "977957",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2,
            "booking_date": "2020-11-02",
            "departure_date": "2022-06-28",
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "element": "ASPP",
            "currency_code": "GBP",
            "amount": 100000,
            "type": "Direct",
            "bonding": "ATOL Fully Bonded",
            "dept_bal_lead_time": "2",
            "total_booking_value": 10000,
            "client_id": ObjectId("623c1a14c1886d1dd0e6f186"),
            "claims_id": ObjectId("62566672600d53e857f9ae94"),
            "nights": -170,
            "file_id": "123",
            "deleted": False,
            "check": "",
            "status": "Reinstated",
            "total_due_to_supplier": 12,
            "return_date": "",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim before t+x days",
            "description": "This anomaly is to be enabled if the claim date for any booking is <=t+x to payment date for that booking on that banking report",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 2})

    transaction = patch_db.claims_file_details.find_one({"file_id": "123"})
    client_anomaly = patch_db.client_anomaly.find_one({"anomaly_id": lookup.inserted_id})
    claim_anomaly = claim_anomaly_detector_lambda.claim_before_t_plus_x_days(
        transaction,
        "",
        "123",
        client_anomaly,
        "Claim before t+x days",
        {"name": "ATOL Escrow"},
        "2023-05-24",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "977957",
            "transaction_id": claims.inserted_id,
            "client_id": ObjectId("623c1a14c1886d1dd0e6f186"),
            "claims_id": ObjectId("62566672600d53e857f9ae94"),
            "file_id": "123",
            "anomaly_type": "Claim before t+x days",
            "anomaly_id": client_anomaly["_id"],
            "status": "Unresolved",
            "deleted": False,
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
        }
    )


@freeze_time("Jan 14th, 2012")
def test_claim_before_t_plus_x_days_failure(patch_db, monkeypatch):
    # Given
    patch_db.banking_file_details.insert_one(
        {
            "booking_ref": "977957",
            "lead_pax": "Parker",
            "pax_count": 2,
            "booking_date": "2022-03-27",
            "departure_date": "2022-08-07",
            "return_date": "2022-08-22",
            "currency_code": "GBP",
            "amount": -2500.001,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Card",
            "days_to_process": 0,
            "total_booking_value": 3718,
            "supplier_ref": "None",
            "supplier_names": "None",
            "payment_date": "2023-05-24",
            "booking_status": "Active",
            "client_id": ObjectId("623c1a14c1886d1dd0e6f186"),
            "banking_id": ObjectId("6243f855f71a9b05567ed31f"),
            "file_id": "123",
            "deleted": False,
            "nights": 15,
            "status": "Cancelled",
        }
    ),
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "977957",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2,
            "booking_date": "2020-11-02",
            "departure_date": "2022-06-28",
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "element": "ASPP",
            "currency_code": "GBP",
            "amount": 100000,
            "type": "Direct",
            "bonding": "ATOL Fully Bonded",
            "dept_bal_lead_time": "2",
            "total_booking_value": 10000,
            "client_id": ObjectId("623c1a14c1886d1dd0e6f186"),
            "claims_id": ObjectId("62566672600d53e857f9ae94"),
            "nights": -170,
            "file_id": "123",
            "deleted": False,
            "check": "",
            "status": "Reinstated",
            "total_due_to_supplier": 12,
            "return_date": "",
        }
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim before t+x days",
            "description": "This anomaly is to be enabled if the claim date for any booking is <=t+x to payment date for that booking on that banking report",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one({"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 2})

    transaction = patch_db.claims_file_details.find_one({"file_id": "123"})
    client_anomaly = patch_db.client_anomaly.find_one({"anomaly_id": lookup.inserted_id})
    claim_anomaly = claim_anomaly_detector_lambda.claim_before_t_plus_x_days(
        transaction,
        "",
        "123",
        client_anomaly,
        "Claim before t+x days",
        {"name": "ATOL Escrow"},
        "2023-05-27",
    )

    # Then
    assert claim_anomaly is None


@freeze_time("Jan 14th, 2012")
def test_claim_before_x_days_from_booking(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {
            "balance": 0,
            "client_id": "1",
            "booking_ref": "123",
            "booking_date": "2022-10-01",
            "total_booking_value": 1000,
        }
    )
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 1000,
            "client_id": "1",
            "pax_count": 5,
            "element": "deposit",
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim before x days from booking",
            "description": "This anomaly is to be enabled if the claim date is before x days from booking",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 10, "elements": ["deposit"]}
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    client_anomaly = patch_db.client_anomaly.find_one({"anomaly_id": lookup.inserted_id})
    claim_anomaly = claim_anomaly_detector_lambda.claim_before_x_days_from_booking(
        transaction,
        trust,
        "67555",
        client_anomaly,
        "Claim before x days from booking",
        {"name": "ATOL Escrow"},
        "2022-10-09",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Claim before x days from booking",
            "anomaly_id": client_anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_claim_before_x_days_from_booking_failure(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {
            "balance": 0,
            "client_id": "1",
            "booking_ref": "123",
            "booking_date": "2022-10-01",
            "total_booking_value": 1000,
        }
    )
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "duplicates": 2,
            "status": "string",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "full": "string",
            "quick": "string",
            "claim_count": 3,
            "currency_code": "euro",
            "amount": 1000,
            "client_id": "1",
            "pax_count": 5,
            "element": "deposit",
            "departure_date": "2020-07-11",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "total_booking_value": 500,
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim before x days from booking",
            "description": "This anomaly is to be enabled if the claim date is before x days from booking",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 10, "elements": ["deposit"]}
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    client_anomaly = patch_db.client_anomaly.find_one({"anomaly_id": lookup.inserted_id})
    claim_anomaly = claim_anomaly_detector_lambda.claim_before_x_days_from_booking(
        transaction,
        trust,
        "67555",
        client_anomaly,
        "Claim before x days from booking",
        {"name": "ATOL Escrow"},
        "2022-10-11",
    )

    # Then
    assert claim_anomaly is None


@pytest.mark.parametrize("element", ["deposit", "balance"])
@freeze_time("Jan 14th, 2012")
def test_max_cap_limit_success(patch_db, element, monkeypatch):
    # Given
    file_id = "67555"
    look_up_trust = patch_db.trust_type.insert_one({"name": "Tripartite Tour Op"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": look_up_trust.inserted_id})
    metadata = patch_db.claims_metadata.insert_one(
        {"client_id": "1", "claim_files": [{"file_id": file_id, "file_date": "2023-05-01"}]}
    )
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "claims_id": metadata.inserted_id,
            "element": element,
            "amount": 600,
            "client_id": "1",
            "return_date": "2020-08-10",
            "file_id": file_id,
            "deleted": False,
        }
    )
    transaction = patch_db.claims_file_details.find_one({"client_id": "1"})
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Max Cap Limit Anomaly",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "custom_field_name": "Max Cap Amount Per Claim",
            "dependencies": ["elements"],
            "from_date_name": "From Date",
            "to_date_name": "To Date",
        }
    )

    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "custom_field_value": 500,
            "elements": ["deposit", "balance"],
            "trust_types": [look_up_trust.inserted_id],
            "from_date": "2023-01-02",
            "to_date": "2023-09-02",
        }
    )

    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Max Cap Limit Anomaly"})
    anomaly["custom_field_value"] = 500
    anomaly["from_date"] = "2023-01-02"
    anomaly["to_date"] = "2023-09-02"
    anomaly["elements"] = ["deposit", "balance"]
    anomaly["insurance_name"] = ""
    anomaly["trust_types"] = [look_up_trust.inserted_id]
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    patch_db.lookup_trust_type.find_one({"_id": client_basic_info["type_of_trust_account"]})
    patch_db.client_anomaly.find_one({"client_id": "1"})

    # When
    claim_anomaly = claim_anomaly_detector_lambda.max_cap_limit_anomaly(
        transaction,
        trust,
        "67555",
        anomaly,
        "Max Cap Limit Anomaly",
        {"_id": look_up_trust.inserted_id},
        "2023-05-01",
    )

    # Then
    claims_metadata = patch_db.claims_metadata.find_one({"_id": metadata.inserted_id})
    assert claims_metadata["claim_files"][-1]["max_cap_limit_anomaly_calculated"] is True
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": metadata.inserted_id,
            "file_id": "67555",
            "anomaly_type": "Max Cap Limit Anomaly",
            "anomaly_id": anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_max_cap_limt_failure(patch_db, monkeypatch):
    # Given
    file_id = "67555"
    patch_db.anomaly_claims.insert_one(
        {
            "client_id": "1",
            "booking_ref": "123",
            "anomaly_type": "Max Cap Limit Anomaly",
            "deleted": False,
            "status": "Unresolved",
            "updated_at": datetime(2012, 1, 14),
        }
    )
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "Tripartite MA"})
    patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id})
    metadata = patch_db.claims_metadata.insert_one({"claim_files": [{"file_id": "1234"}, {"file_id": file_id}]})
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "claims_id": metadata.inserted_id,
            "element": "aspp",
            "amount": 600,
            "client_id": "1",
            "return_date": "2020-08-15",
            "file_id": file_id,
            "deleted": False,
        }
    )
    transaction = patch_db.claims_file_details.find_one({"client_id": "1"})
    patch_db.trust_fund_v2.insert_one({"client_id": "1", "booking_ref": "123"})
    monkeypatch.setattr(
        patch_db.claims_file_details,
        "aggregate",
        lambda *args, **kwargs: iter([{"_id": "", "total_claimed": 600}]),
    )
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Max Cap Amount Per Claim",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "name": "Max Cap Limit Anomaly",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "elements": ["deposit"],
            "anomaly_id": lookup.inserted_id,
            "client_id": "1",
            "custom_field_value": 700,
            "trust_types": [lookup_trust.inserted_id],
        }
    )

    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    anomaly = patch_db.lookup_anomaly.find_one({"name": "Max Cap Limit Anomaly"})
    anomaly["custom_field_value"] = 700
    anomaly["elements"] = ["deposit"]
    patch_db.client_anomaly.find_one({"client_id": "1"})

    # When
    output = claim_anomaly_detector_lambda.max_cap_limit_anomaly(
        transaction,
        trust,
        "67555",
        anomaly,
        "Max Cap Limit Anomaly",
        {"_id": lookup_trust.inserted_id},
        "2022-06-27",
    )
    claim_anomaly = patch_db.anomaly_claims.find_one(
        {
            "client_id": "1",
            "anomaly_type": "Max Cap Limit Anomaly",
            "deleted": False,
        },
        projection={"_id": 0},
    )

    # Then
    claims_metadata = patch_db.claims_metadata.find_one({"_id": metadata.inserted_id})
    assert "max_cap_limit_anomaly_calculated" not in claims_metadata["claim_files"][-1]
    assert output is None
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "client_id": "1",
            "anomaly_type": "Max Cap Limit Anomaly",
            "status": "Unresolved",
            "deleted": False,
            "updated_at": datetime(2012, 1, 14),
        }
    )


mock = Mock(return_value=None)


@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.negative_funds_trust",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.funds_exceed_tbv",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_before_return",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.duplicate_amount",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_exceed_per_pax",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_early_departure_date",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.multi_currency_booking",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.insurance_cover_in_date",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_exceeds_apb",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.max_claim_exceed",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.max_cap_anomaly",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_supplier_list",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.commision_claim_anomaly",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.cruise_claim_anomaly",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_exceed_cost",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.safi_insurance_tracker",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_before_t_plus_x_days",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.claim_before_x_days_from_booking",
    mock,
)
@patch(
    "claim-anomaly-detector-lambda.claim-anomaly-detector-lambda.max_cap_limit_anomaly",
    mock,
)
def test_lambda_handler(patch_db, s3_client):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1", "type_of_trust_account": "test"})
    client_id = basic_info.inserted_id

    data = {
        "dataKey": "claim/aggregator/628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748.json",
        "batchNo": 0,
    }

    patch_db.trust_fund_v2.insert_one({"client_id": client_id, "booking_ref": "123"})
    claim_metadata = patch_db.claims_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": client_id,
            "updated_at": datetime(2012, 1, 14),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "*********-ABC-Claim.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                    "file_date": "2020-07-21",
                    "item_count": 14,
                    "deposit": 45920,
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "_id": ObjectId("621c3b23e22a9303b9b091ba"),
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": claim_metadata.inserted_id,
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": client_id,
                "pax_count": 3,
                "departure_date": "2020-02-11",
                "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                "total_booking_value": 3000,
                "element": "performance",
                "deleted": False,
            },
            {
                "_id": ObjectId("621c3b23e22a9303b9b091bb"),
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": claim_metadata.inserted_id,
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "euro",
                "amount": 500,
                "client_id": client_id,
                "pax_count": 3,
                "departure_date": "2020-02-11",
                "file_id": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
                "total_booking_value": 3000,
                "element": "performance",
                "deleted": False,
            },
        ]
    )

    claim_lookup1 = patch_db.lookup_anomaly.insert_one(
        {"description": "Negative cumulative Funds in Trust", "name": "Negative Funds in Trust"}
    )

    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup1.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup2 = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds in Trust exceed/less than Total Booking Value",
            "name": "Funds in Trust Exceed/less than Total Booking Value",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup2.inserted_id,
            "client_id": client_id,
        }
    )

    claim_lookup3 = patch_db.lookup_anomaly.insert_one(
        {"description": "Performance claim before client return", "name": "Claim Performance Before Return"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup3.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup4 = patch_db.lookup_anomaly.insert_one({"description": "Duplicate claim value", "name": "Duplicate"})

    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup4.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup5 = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Cost Per Pax",
            "description": "Claim value exceeds the maximum allowed for the number of passengers",
            "name": "Claim Exceeds Cost Per Pax",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup5.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup6 = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "The claim was attempted too early from the configured limits",
            "name": "Claim Too Early For Departure Date",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup6.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup7 = patch_db.lookup_anomaly.insert_one(
        {"description": "Multiple currencies detected for this booking", "name": "Multi-currency Booking"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup7.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup8 = patch_db.lookup_anomaly.insert_one(
        {"description": "Insurance date already expired", "name": "Insurance Cover In Date"}
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup8.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup9 = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Percentage (%)",
            "description": "Claim amount is more than the agreed amount/%age amount per booking",
            "name": "Claim Exceeds agreed % per booking",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup9.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup10 = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Max % Of Elements",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "name": "Max Claim % Exceeded",
            "dependencies": ["aspp", "deposit"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup10.inserted_id,
            "client_id": client_id,
            "elements": ["aspp", "deposit"],
        }
    )
    claim_lookup11 = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Max Cap Amount Per Claim",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "name": "Max Cap Claim Anomaly (Rollover)",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup11.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup12 = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Compare the supplier names with the client-supplier list",
            "name": "Claim SupplierList Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup12.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup13 = patch_db.lookup_anomaly.insert_one(
        {
            "description": "Funds with Commission element type shall be processed only if the return date is in the past.",
            "name": "Commission Claim Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup13.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup14 = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "Funds with Cruise element type shall be processed if the departure date is within x no of days of the claim date.",
            "name": "Cruise Claim Anomaly",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup14.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup15 = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim Exceeds Cost %",
            "description": "Amount exceeds configured percentage of Total Booking Value.",
            "custom_field_name": "Max % Of Total booking value",
            "dependencies": ["elements"],
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup15.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup16 = patch_db.lookup_anomaly.insert_one(
        {
            "description": "To check if the amount claimed for that supplier exceeds the configured cap amount.",
            "name": "SAFI Insurance Tracker",
        }
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup16.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup17 = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim before t+x days",
            "description": "This anomaly is to be enabled if the claim date for any booking is <=t+x to payment date for that booking on that banking report",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup17.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup18 = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Claim before x days from booking",
            "description": "This anomaly is to be enabled if the claim date is before x days from booking",
            "custom_field_name": "Number of Days",
        },
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup18.inserted_id,
            "client_id": client_id,
        }
    )
    claim_lookup19 = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Max Cap Limit Anomaly",
            "description": "Funds paid in to be released as soon as possible after hitting the trust",
            "custom_field_name": "Max Cap Amount Per Claim",
            "dependencies": ["elements"],
        },
    )
    patch_db.client_anomaly.insert_one(
        {
            "anomaly_id": claim_lookup19.inserted_id,
            "client_id": client_id,
        }
    )

    output_data = {
        "clientId": str(client_id),
        "fileId": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
        "sftp": False,
        "sftpKey": "",
        "transactionIds": ["621c3b23e22a9303b9b091ba", "621c3b23e22a9303b9b091bb"],
    }
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(output_data), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )
    # When

    result = claim_anomaly_detector_lambda.lambda_handler(data, "context")

    # Then

    assert result == {
        "fileId": "628f93be-42e2-48c9-9e7f-e49bdb67d2dc_2022-02-28T03:50:49.750748",
        "clientId": str(client_id),
        "sftp": False,
        "sftpKey": "",
    }
    assert mock.call_count == 38


@freeze_time("Jan 14th, 2012")
def test_departure_within_x_days_from_booking(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {"client_id": "1", "booking_ref": "123", "booking_date": "2023-10-01", "departure_date": "2023-10-12"}
    )
    claims = patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "euro",
            "amount": 1000,
            "client_id": "1",
            "element": "Flight",
            "booking_date": "2023-10-01",
            "departure_date": "2023-10-12",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Departure Within x Days From Booking Date",
            "description": "This anomaly is to be enabled if the departure date is not within x days from booking date",
            "custom_field_name": "Number of Days",
            "dependencies": ["elements"],
        },
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 10, "elements": ["Flight"]}
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    client_anomaly = patch_db.client_anomaly.find_one({"anomaly_id": lookup.inserted_id})
    claim_anomaly = claim_anomaly_detector_lambda.departure_within_x_days_from_booking(
        transaction,
        trust,
        "67555",
        client_anomaly,
        "Departure Within x Days From Booking Date",
        {"name": "ATOL Escrow"},
        "2022-10-09",
    )

    # Then
    assert claim_anomaly == (
        {
            "booking_ref": "123",
            "transaction_id": claims.inserted_id,
            "client_id": "1",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "file_id": "67555",
            "anomaly_type": "Departure Within x Days From Booking Date",
            "anomaly_id": client_anomaly["_id"],
            "status": "Unresolved",
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
            "deleted": False,
        }
    )


@freeze_time("Jan 14th, 2012")
def test_departure_within_x_days_from_booking_failure(patch_db):
    # Given
    patch_db.trust_fund_v2.insert_one(
        {"client_id": "1", "booking_ref": "123", "booking_date": "2023-10-01", "departure_date": "2023-10-07"}
    )
    patch_db.claims_file_details.insert_one(
        {
            "booking_ref": "123",
            "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
            "currency_code": "euro",
            "amount": 1000,
            "client_id": "1",
            "element": "Flight",
            "booking_date": "2023-10-01",
            "departure_date": "2023-10-07",
            "return_date": "2020-08-145",
            "file_id": "67555",
            "deleted": False,
        }
    )

    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "name": "Departure Within x Days From Booking Date",
            "description": "This anomaly is to be enabled if the departure date is not within x days from booking date",
            "custom_field_name": "Number of Days",
            "dependencies": ["elements"],
        },
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": "1", "custom_field_value": 10, "elements": ["Flight"]}
    )

    transaction = patch_db.claims_file_details.find_one({"file_id": "67555"})
    trust = patch_db.trust_fund_v2.find_one({"client_id": "1", "booking_ref": "123"})
    client_anomaly = patch_db.client_anomaly.find_one({"anomaly_id": lookup.inserted_id})
    claim_anomaly = claim_anomaly_detector_lambda.departure_within_x_days_from_booking(
        transaction,
        trust,
        "67555",
        client_anomaly,
        "Departure Within x Days From Booking Date",
        {"name": "ATOL Escrow"},
        "2022-10-09",
    )

    # Then
    assert claim_anomaly is None
