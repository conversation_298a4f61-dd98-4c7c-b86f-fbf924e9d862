from datetime import datetime
import importlib
import json
import os
from unittest.mock import patch
from mongomock import ObjectId
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_module = importlib.import_module("claim-file-fetch-lambda.claim-file-fetch-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_module.db
    claim_module.client.drop_database("db")


def test_lambda_handler(patch_db, s3_client, monkeypatch):
    # Given
    BUCKET = os.environ["STATE_MACHINE_PAYLOAD_BUCKET"]
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = {
        "clientId": client_id,
        "fileId": "12345",
        "replace": True,
    }
    monkeypatch.setattr(json, "dumps", lambda x: '{"test": "testValue"}')
    metadata = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "20220224-All -claims -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "deposit": {"GBP": 2000},
                },
                {
                    "status": "Scanning",
                    "file_name": "20220224-All -claims -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12346",
                    "file_date": "2022-02-24",
                },
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4ff"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": client_id,
                "claims_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": False,
            },
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4fa"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": client_id,
                "claims_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": False,
            },
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4fb"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": client_id,
                "claims_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": False,
            },
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4fc"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": client_id,
                "claims_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": False,
            },
        ]
    )
    s3_client.create_bucket(
        Bucket=BUCKET,
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )

    # When
    result = claim_module.lambda_handler(data, "context")

    # Then
    assert result == {
        "bucket": BUCKET,
        "prefix": "claim/file-fetch/12345/",
        "clientId": client_id,
        "fileId": "12345",
        "replace": True,
    }
