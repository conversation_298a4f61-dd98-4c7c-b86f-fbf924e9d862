import os
import tempfile
import xlsxwriter
from mongomock import ObjectId
import pytest
from moto import mock_s3
from freezegun import freeze_time
from unittest.mock import patch
from sheet2dict import Worksheet
import importlib
from custom_mongomock.mongo_client import CustomMongoClient

TEMP_DIR = tempfile.gettempdir()

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_file_generator_lambda = importlib.import_module("claim-file-generator-lambda.claim-file-generator-lambda")


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_file_generator_lambda, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_file_generator_lambda.db
    claim_file_generator_lambda.client.drop_database("db")


@freeze_time("2022-10-10")
@mock_s3
@pytest.mark.parametrize(
    "aggregation_result,result",
    [
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000, 5000, -2000],
                    "claim_amount": [4000, -4000, 2000, -2000],
                    "supplierNames": ["abc", None, None, "xyz"],
                    "type": ["purchase", "refund", None, None],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                }
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-12-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000],
                    "claim_amount": [],
                    "supplierNames": ["abc", "xyz"],
                    "type": ["purchase", "refund"],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "abc",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "purchase",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "xyz",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "refund",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000],
                    "claim_amount": [],
                    "supplierNames": [None, None],
                    "type": [None, None],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "5000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-12-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000, 5000, -2000],
                    "claim_amount": [],
                    "supplierNames": ["abc", None, None, "xyz"],
                    "type": ["purchase", None, None, "refund"],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "5000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "abc",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "purchase",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "1000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-2000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "xyz",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "refund",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-12-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000, 5000, -2000],
                    "claim_amount": [4000, -4000],
                    "supplierNames": ["abc", "efg", None, "xyz"],
                    "type": ["purchase", None, None, "refund"],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "1000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "abc",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "purchase",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "1000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-12-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-2000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "xyz",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "refund",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [5000, 4000],
                    "claim_amount": [],
                    "supplierNames": [None, "xyz"],
                    "type": ["purchase", "purchase"],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "5000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "purchase",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "xyz",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "purchase",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000, 5000, -2000, -1000],
                    "claim_amount": [4000, -4000, 1000, 1000, -2000],
                    "supplierNames": ["abc", None, None, "xyz", "test"],
                    "type": ["purchase", None, None, "refund", "refund"],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-1000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "test",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "refund",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [6000, -10000],
                    "claim_amount": [],
                    "supplierNames": [None, "xyz"],
                    "type": [None, "refund"],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "6000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-10000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "xyz",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "refund",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [10000, -3000, 2000],
                    "claim_amount": [3000, -3000, 7000],
                    "supplierNames": [None, None, "xyz"],
                    "type": [
                        None,
                        None,
                        "purchase",
                    ],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "2000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "xyz",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "purchase",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [10000, -4000, 10000, -4000],
                    "claim_amount": [4000, -4000],
                    "supplierNames": ["ml", None, None, "xyz"],
                    "type": [None, None, None, None],
                    "currency": "GBP",
                    "element": "Performance",
                },
                {
                    "booking_ref": "14001",
                    "departure_date": "2022-12-10",
                    "booking_status": "active",
                    "banking_amount": [10000, -4000],
                    "claim_amount": [4000, -4000],
                    "supplierNames": ["ki", None],
                    "type": [None, None],
                    "currency": "GBP",
                    "element": "Performance",
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "6000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "ml",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "10000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "-4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "xyz",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
            ],
        ),
    ],
)
def test_claim_file_genetrator_lambda_new_workflow_true(s3_client, patch_db, monkeypatch, aggregation_result, result):
    # Given
    data = {
        "clientId": "6256647a600d53e857f9ae74",
        "fileId": "12345",
        "fileType": "xlsx",
        "replace": False,
        "dataKey": "claim/12345.json",
        "bucket": os.environ["CLAIM_FILE_BUCKET"],
    }
    patch_db.client_basic_info.insert_one({"_id": ObjectId("6256647a600d53e857f9ae74"), "new_workflow": True})
    patch_db.trust_fund_v2.insert_many(
        [
            {
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "booking_ref": "14000",
                "currency_code": ["EUR", "GBP"],
            },
        ]
    )
    monkeypatch.setattr(patch_db.trust_fund_v2, "aggregate", lambda *args, **kwargs: aggregation_result)
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "The claim was attempted too early from the configured limits",
            "name": "Claim Too Early For Departure Date",
        }
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": ObjectId("6256647a600d53e857f9ae74"), "custom_field_value": 15}
    )
    s3_client.create_bucket(
        Bucket=os.environ["CLAIM_FILE_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    file_id = data["fileId"]
    workbook = xlsxwriter.Workbook(f"{TEMP_DIR}/{file_id}")
    workbook.close()

    s3_client.upload_file(f"{TEMP_DIR}/{file_id}", Bucket=os.environ["CLAIM_FILE_BUCKET"], Key=data["fileId"])

    # When

    claim_file_generator_lambda.lambda_handler(data, "context")

    # Then
    ws = Worksheet()
    ws.xlsx_to_dict(path=f"{TEMP_DIR}/{file_id}.xlsx")
    data_items = ws.sanitize_sheet_items
    assert data_items == result


@freeze_time("2022-10-10")
@mock_s3
@pytest.mark.parametrize(
    "aggregation_result,result",
    [
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000, 5000, -2000],
                    "claim_amount": [4000, -4000, 2000, -2000],
                    "currency": "GBP",
                    "element": "Performance",
                    "balance": 0,
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "DepartureDate": "2022-10-10",
                    "ReturnDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "0",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                }
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [5000, -4000, 5000, -2000, -1000],
                    "claim_amount": [4000, -4000, 1000, 1000, -2000],
                    "currency": "GBP",
                    "element": "Performance",
                    "balance": 4000,
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "4000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "ReturnDate": "None",
                    "PaymentType": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DepartureDate": "2022-10-10",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [6000, -10000],
                    "claim_amount": [],
                    "currency": "GBP",
                    "element": "Performance",
                    "balance": 6000,
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "6000",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "ReturnDate": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DepartureDate": "2022-10-10",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [10000, -3000, 2000],
                    "claim_amount": [3000, -3000, 7000],
                    "currency": "GBP",
                    "element": "Performance",
                    "balance": 30,
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "30",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "ReturnDate": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DepartureDate": "2022-10-10",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
            ],
        ),
        (
            [
                {
                    "booking_ref": "14000",
                    "departure_date": "2022-10-10",
                    "booking_status": "active",
                    "banking_amount": [10000, -4000, 10000, -4000],
                    "claim_amount": [4000, -4000],
                    "currency": "GBP",
                    "element": "Performance",
                    "balance": 80,
                },
                {
                    "booking_ref": "14001",
                    "departure_date": "2022-12-10",
                    "booking_status": "active",
                    "banking_amount": [10000, -4000],
                    "claim_amount": [4000, -4000],
                    "currency": "GBP",
                    "element": "Performance",
                    "balance": 30,
                },
            ],
            [
                {
                    "BookingRef": "14000",
                    "LeadPax": "None",
                    "BookingDate": "None",
                    "Element": "Performance",
                    "Currency": "GBP",
                    "BookingStatus": "active",
                    "Amount": "80",
                    "Pax": "None",
                    "SupplierRef": "None",
                    "SupplierNames": "None",
                    "BookingType": "None",
                    "Bonding": "None",
                    "PaymentType": "None",
                    "ReturnDate": "None",
                    "TotalBookingValue": "None",
                    "DaysToProcess": "None",
                    "DepartureDate": "2022-10-10",
                    "DeptBalLeadTime": "None",
                    "PaymentDate": "None",
                    "Type": "None",
                },
            ],
        ),
    ],
)
def test_claim_file_genetrator_lambda_new_workflow_false(s3_client, patch_db, monkeypatch, aggregation_result, result):
    # Given
    data = {
        "clientId": "6256647a600d53e857f9ae74",
        "fileId": "12345",
        "fileType": "xlsx",
        "replace": False,
        "dataKey": "claim/12345.json",
        "bucket": os.environ["CLAIM_FILE_BUCKET"],
    }
    patch_db.client_basic_info.insert_one({"_id": ObjectId("6256647a600d53e857f9ae74"), "new_workflow": False})
    patch_db.trust_fund_v2.insert_many(
        [
            {
                "client_id": ObjectId("6256647a600d53e857f9ae74"),
                "booking_ref": "14000",
                "currency_code": ["GBP"],
            },
        ]
    )
    monkeypatch.setattr(patch_db.trust_fund_v2, "aggregate", lambda *args, **kwargs: aggregation_result)
    lookup = patch_db.lookup_anomaly.insert_one(
        {
            "custom_field_name": "Number of Days",
            "description": "The claim was attempted too early from the configured limits",
            "name": "Claim Too Early For Departure Date",
        }
    )
    patch_db.client_anomaly.insert_one(
        {"anomaly_id": lookup.inserted_id, "client_id": ObjectId("6256647a600d53e857f9ae74"), "custom_field_value": 15}
    )
    s3_client.create_bucket(
        Bucket=os.environ["CLAIM_FILE_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    file_id = data["fileId"]
    workbook = xlsxwriter.Workbook(f"{TEMP_DIR}/{file_id}")
    workbook.close()

    s3_client.upload_file(f"{TEMP_DIR}/{file_id}", Bucket=os.environ["CLAIM_FILE_BUCKET"], Key=data["fileId"])

    # When

    claim_file_generator_lambda.lambda_handler(data, "context")

    # Then
    ws = Worksheet()
    ws.xlsx_to_dict(path=f"{TEMP_DIR}/{file_id}.xlsx")
    data_items = ws.sanitize_sheet_items
    assert data_items == result
