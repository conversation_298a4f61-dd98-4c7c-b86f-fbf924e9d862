import importlib
from unittest.mock import patch, MagicMock
from hamcrest import assert_that, calling, raises
import pytest
from datetime import datetime
from freezegun import freeze_time
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_module = importlib.import_module("claim-file-validator-lambda.claim-file-validator-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_module.db
    claim_module.client.drop_database("db")


@freeze_time("Jan 14th, 2012")
def test_cancel_claims_file(patch_db):
    # Given
    claims_metadata = patch_db.claims_metadata.insert_one(
        {
            "status": "Submitted",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "220211201-ABC-Claim.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "67555",
                    "file_date": "2022-01-21",
                    "item_count": {"GBP": 45920},
                    "deposit": {"GBP": 45920},
                }
            ],
            "created_at": datetime(2012, 1, 14),
        },
    )
    # When
    claim_module.cancel_claims_file("67555", "Validation Error for -1043388 of type alphanumeric in bookingref column")
    metadata = patch_db.claims_metadata.find_one({"client_id": "1"})
    # Then
    assert metadata == (
        {
            "_id": claims_metadata.inserted_id,
            "status": "Cancelled by System",
            "client_id": "1",
            "updated_at": datetime(2012, 1, 14),
            "claim_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "220211201-ABC-Claim.xlsx",
                    "submitted_date": datetime.now(),
                    "file_id": "67555",
                    "file_date": "2022-01-21",
                    "item_count": {"GBP": 45920},
                    "deposit": {"GBP": 45920},
                    "notes": "Validation Error for -1043388 of type alphanumeric in bookingref column",
                }
            ],
            "created_at": datetime(2012, 1, 14),
        }
    )


@pytest.mark.parametrize(
    "value,datatype,file_id,output,key,row_count,client_id",
    [
        ("string", "string", "67555", "string", "bookingref", 1, "123"),
        ("1", "number", "67555", 1.0, "amount", 2, "123"),
        ("-1", "number", "67555", -1.0, "amount", 3, "123"),
        ("-3.552713678800501e+14", "number", "67555", -355271367880050.1, "amount", 4, "123"),
        ("-3.552713678800501e-14", "number", "67555", -3.552713678800501e-14, "amount", 2, "123"),
        ("1a2b", "alphanumeric", "67555", "1a2b", "reference", 2, "123"),
        ("2021-02-12", "date", "67555", "2021-02-12", "returndate", 3, "123"),
        ("January", "month", "67555", "January", "month", 2, "123"),
        ("january", "month", "67555", "january", "month", 3, "123"),
        ("True", "boolean", "6755", True, "intrust", 3, "123"),
        ("Yes", "boolean", "6755", True, "intrust", 4, "123"),
        ("False", "boolean", "6755", False, "intrust", 1, "123"),
        ("No", "boolean", "6755", False, "intrust", 2, "123"),
        ("true", "boolean", "6755", True, "intrust", 2, "123"),
        ("yes", "boolean", "6755", True, "intrust", 1, "123"),
        ("false", "boolean", "6755", False, "intrust", 3, "123"),
        ("no", "boolean", "6755", False, "intrust", 2, "123"),
        ("y", "boolean", "6755", True, "intrust", 1, "123"),
        ("Y", "boolean", "6755", True, "intrust", 3, "123"),
        ("n", "boolean", "6755", False, "intrust", 2, "123"),
        ("N", "boolean", "6755", False, "intrust", 1, "123"),
    ],
)
def test_validate_type_success(patch_db, value, datatype, file_id, output, key, row_count, client_id):
    # When
    result = claim_module.validate_type(value, datatype, file_id, key, row_count, client_id)
    # Then
    assert result == output


@pytest.mark.parametrize(
    "value,datatype,file_id,key,row_count,client_id",
    [
        (1, "string", "67555", "bookingref", 1, "123"),
        ("a", "number", "67555", "amount", 2, "123"),
        (1, "alphanumeric", "67555", "reference", 2, "123"),
        (2021 - 2 - 12, "date", "67555", "bookingdate", 3, "123"),
        ("01", "month", "67555", "month", 1, "123"),
        (None, "string", "67555", "bookingref", 4, "123"),
        (None, "number", "67555", "amount", 1, "123"),
        (None, "alphanumeric", "67555", "reference", 4, "123"),
        (None, "date", "67555", "bookingdate", 2, "123"),
        (None, "month", "67555", "month", 3, "123"),
    ],
)
def test_validate_type_failure(patch_db, value, datatype, file_id, key, row_count, client_id):
    # When

    # Then
    assert_that(
        calling(claim_module.validate_type).with_args(value, datatype, file_id, key, row_count, client_id),
        raises(Exception),
    )


@pytest.mark.parametrize(
    "input,output",
    [
        (["2020-05-04", "date", "file_id", "date"], "2020-05-04"),
        (["2020-05-04 10:18:32", "date", "file_id", "date"], "2020-05-04"),
        (["2020-05-04 10:18:32.123", "date", "file_id", "date"], "2020-05-04"),
        (["2020/05/04", "date", "file_id", "date"], "2020-05-04"),
        (["04-05-2020", "date", "file_id", "date"], "2020-05-04"),
        (["04-05-2020 10:18:32", "date", "file_id", "date"], "2020-05-04"),
        (["04-05-2020 10:18:32.234567", "date", "file_id", "date"], "2020-05-04"),
        (["04/05/2020", "date", "file_id", "date"], "2020-05-04"),
        (["2020/05/04 12:00:00 PM", "date", "file_id", "date"], "2020-05-04"),
        (["6/15/2021 12:00:00 AM", "date", "file_id", "date"], "2021-06-15"),
    ],
)
def test_claim_try_parsing_date(input, output):
    # Given
    data = input
    # When
    result = claim_module.try_parsing_date(*data, 1, "123")
    # Then
    assert result == output


@patch(
    "claim-file-validator-lambda.claim-file-validator-lambda.cancel_claims_file",
    MagicMock(),
)
def test_claim_try_parsing_date_failure(patch_db):
    # Given
    data = ["invalid_date", "date", "file_id", "date", "1", "123"]
    # When

    # Then
    assert_that(
        calling(claim_module.try_parsing_date).with_args(*data),
        raises(ValueError),
    )


@pytest.mark.parametrize(
    "input,output", [("A BC", "abc"), ("A  B C", "abc"), ("A B C", "abc"), ("A / b", "a/b"), (" A / b ", "a/b")]
)
def test_custom_filter(input, output):
    # Given
    data = input
    # When
    result = claim_module.custom_filter(data)
    # Then
    assert result == output


def test_currency_validation_sucess(patch_db):
    # Given
    currency_list = ["GBP", "USD"]
    # When
    result = claim_module.currency_validation(currency_list, "USD", "123")
    # Then
    assert result == "USD"


@pytest.mark.parametrize("input", ["usdw", "GBP"])
def test_currency_validation_failure(patch_db, input):
    # Given
    currency_list = ["NOK", "USD"]
    value = input
    # Then
    assert_that(
        calling(claim_module.currency_validation).with_args(currency_list, value, "123"),
        raises(Exception),
    )


def test_custom_data_modifier_broadway(patch_db):
    # Given
    patch_db.lookup_claim_elements.insert_many(
        [
            {"name": "Charter Flight (Price)"},
            {"name": "Scheduled Flight (Price)"},
            {"name": "Accom (Cost)"},
            {"name": "Transfer (Cost)"},
            {"name": "Parking (Cost)"},
            {"name": "Car Hire (Cost)"},
            {"name": "Tour (Cost)"},
            {"name": "Add On Tour (Cost)"},
            {"name": "Generic (Cost)"},
            {"name": "Cruise (Cost)"},
            {"name": "TRVL INS (Cost)"},
            {"name": "Output VAT"},
            {"name": "Fees (with cost only)"},
            {"name": "TD Profit"},
        ]
    )

    data = [
        {
            "Booking reference": "BTW232058",
            "Lead Pax": "Mrs P Smith",
            "Booking Date": "2021-11-12 00:00:00",
            "Departure Date": "2022-05-13 00:00:00",
            "Return Date": "2022-05-27 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "Charter Flight (Price)": "0",
            "Scheduled Flight (Price)": "0",
            "Accom (Cost)": "822.8",
            "Transfer (Cost)": "0",
            "Parking (Cost)": "0",
            "Car Hire (Cost)": "0",
            "Tour (Cost)": "0",
            "Add On Tour (Cost)": "0",
            "Generic (Cost)": "0",
            "Cruise (Cost)": "0",
            "TRVL INS (Cost)": "0",
            "Output VAT": "0",
            "Fees (with cost only)": "0",
            "TD Profit": "0",
            "TBV": "1416",
            "Total Paid": "1191.73",
            "Claimed VALUE": "368.93",
            "Amount": "822.8",
            "CLAIMED IN MTH": "822.8",
            "CUM'L CLAIMED VALUE": "1191.73",
            "Cumulative FIT": "0",
            "Validity Date": "2022-05-31 03:00:00",
        },
        {
            "Booking reference": "BSH360284",
            "Lead Pax": "Mr I Gammon",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "Charter Flight (Price)": "0",
            "Scheduled Flight (Price)": "0",
            "Accom (Cost)": "1362.58",
            "Transfer (Cost)": "22.63",
            "Parking (Cost)": "0",
            "Car Hire (Cost)": "0",
            "Tour (Cost)": "0",
            "Add On Tour (Cost)": "0",
            "Generic (Cost)": "0",
            "Cruise (Cost)": "0",
            "TRVL INS (Cost)": "0",
            "Output VAT": "0",
            "Fees (with cost only)": "0",
            "TD Profit": "0",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "1385.21",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
        },
        {
            "Booking reference": "BTW232297",
            "Lead Pax": "Mr D Blackburn",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "Charter Flight (Price)": "0",
            "Scheduled Flight (Price)": "100",
            "Accom (Cost)": "254.73",
            "Transfer (Cost)": "36.14",
            "Parking (Cost)": "0",
            "Car Hire (Cost)": "0",
            "Tour (Cost)": "0",
            "Add On Tour (Cost)": "223.92",
            "Generic (Cost)": "0",
            "Cruise (Cost)": "0",
            "TRVL INS (Cost)": "0",
            "Output VAT": "0",
            "Fees (with cost only)": "0",
            "TD Profit": "0",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "614.79",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
        },
        {
            "Booking reference": "BTW232298",
            "Lead Pax": "Mr D abc",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "Charter Flight (Price)": "0",
            "Scheduled Flight (Price)": "0",
            "Accom (Cost)": "0",
            "Transfer (Cost)": "0",
            "Parking (Cost)": "10",
            "Car Hire (Cost)": "0",
            "Tour (Cost)": "0",
            "Add On Tour (Cost)": "0",
            "Generic (Cost)": "0",
            "Cruise (Cost)": "0",
            "TRVL INS (Cost)": "0",
            "Output VAT": "0",
            "Fees (with cost only)": "0",
            "TD Profit": "0",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "130",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
        },
    ]

    row_count = 5
    # When
    result = claim_module.custom_data_modifier_broadway(data, row_count)

    # Then
    assert result == [
        {
            "Booking reference": "BTW232058",
            "Lead Pax": "Mrs P Smith",
            "Booking Date": "2021-11-12 00:00:00",
            "Departure Date": "2022-05-13 00:00:00",
            "Return Date": "2022-05-27 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "1416",
            "Total Paid": "1191.73",
            "Claimed VALUE": "368.93",
            "Amount": "822.8",
            "CLAIMED IN MTH": "822.8",
            "CUM'L CLAIMED VALUE": "1191.73",
            "Cumulative FIT": "0",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Accom (Cost)",
            "initial_row_count": 6,
        },
        {
            "Booking reference": "BSH360284",
            "Lead Pax": "Mr I Gammon",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "1362.58",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Accom (Cost)",
            "initial_row_count": 7,
        },
        {
            "Booking reference": "BSH360284",
            "Lead Pax": "Mr I Gammon",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "22.63",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Transfer (Cost)",
            "initial_row_count": 7,
        },
        {
            "Booking reference": "BTW232297",
            "Lead Pax": "Mr D Blackburn",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "100",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Scheduled Flight (Price)",
            "initial_row_count": 8,
        },
        {
            "Booking reference": "BTW232297",
            "Lead Pax": "Mr D Blackburn",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "254.73",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Accom (Cost)",
            "initial_row_count": 8,
        },
        {
            "Booking reference": "BTW232297",
            "Lead Pax": "Mr D Blackburn",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "36.14",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Transfer (Cost)",
            "initial_row_count": 8,
        },
        {
            "Booking reference": "BTW232297",
            "Lead Pax": "Mr D Blackburn",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "223.92",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Add On Tour (Cost)",
            "initial_row_count": 8,
        },
        {
            "Booking reference": "BTW232298",
            "Lead Pax": "Mr D abc",
            "Booking Date": "2022-03-19 00:00:00",
            "Departure Date": "2022-08-25 00:00:00",
            "Return Date": "2022-09-06 00:00:00",
            "Booking Type": "Package (Multi-contract)",
            "TBV": "3640",
            "Total Paid": "200.14",
            "Claimed VALUE": "0",
            "Amount": "10",
            "CLAIMED IN MTH": "10",
            "CUM'L CLAIMED VALUE": "10",
            "Cumulative FIT": "190.14",
            "Validity Date": "2022-05-31 03:00:00",
            "element": "Parking (Cost)",
            "initial_row_count": 9,
        },
    ]
