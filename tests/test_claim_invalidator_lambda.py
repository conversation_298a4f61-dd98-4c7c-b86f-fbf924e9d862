from datetime import datetime
import importlib
import json
import os
from unittest.mock import patch
from mongomock import ObjectId
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_module = importlib.import_module("claim-invalidator-lambda.claim-invalidator-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_module.db
    claim_module.client.drop_database("db")


@pytest.mark.skip("To be completed later")
def test_lambda_handler(patch_db, s3_client):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": True,
        "transactions": [
            {"_id": "62188fcb7ec8edb86189c4ff", "booking_ref": "1", "amount": 500},
            {"_id": "62188fcb7ec8edb86189c4fa", "booking_ref": "1", "amount": 500},
        ],
    }

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    key = "claim/file-fetch/12345.json"
    s3_client.put_object(Body=json.dumps(data), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=key)
    metadata = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "20220224-All -claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "deposit": {"GBP": 2000},
                },
                {
                    "status": "Scanning",
                    "file_name": "20220224-All -claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12346",
                    "file_date": "2022-02-24",
                },
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4ff"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": client_id,
                "claims_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": False,
            },
            {
                "_id": ObjectId("62188fcb7ec8edb86189c4fa"),
                "booking_ref": "1",
                "booking_date": "2022-02-01",
                "departure_date": "2022-01-01",
                "return_date": "2022-01-01",
                "amount": 500,
                "client_id": client_id,
                "claims_id": metadata.inserted_id,
                "currency_code": "EUR",
                "customer_type": "Agent",
                "file_id": "12345",
                "element": "ddd",
                "total_booking_value": 100,
                "transfer_of_funds": "2",
                "deleted": False,
            },
        ]
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "1",
        "balance": 1000,
        "total_in_trust": 1000,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    # When
    claim_module.lambda_handler({"Key": key}, "context")

    # Then
    claim_file_details = list(
        patch_db.claims_file_details.find({"file_id": "12345"}, projection={"_id": 0, "deleted": 1})
    )
    trust_fund = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "1"})
    assert claim_file_details == [{"deleted": True}, {"deleted": True}]
    assert trust_fund["balance"] == 2000
    assert trust_fund["total_claimed"] == -1000
