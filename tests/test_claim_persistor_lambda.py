from datetime import datetime
import os
from unittest import mock
from mongomock import ObjectId
import pytest
import importlib
import json
from custom_mongomock.mongo_client import CustomMongoClient


with mock.patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_module = importlib.import_module("claim-persistor-lambda.claim-persistor-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_module.db
    claim_module.client.drop_database("db")


@pytest.mark.skip("To be completed later")
def test_lambda_handler_claim_persistor(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow1"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id}
    )
    client_id = basic_info.inserted_id
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "claim/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
    }
    data_list = [
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2022-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2022-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2022-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2022-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 14},
                    "claim_total": {"GBP": 45920},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = claim_module.lambda_handler(data, None)

    # Then
    claim_info = list(
        patch_db.claims_file_details.find({"client_id": client_id, "booking_ref": data_list[0]["booking_ref"]})
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == -300
    assert trust_fund_info["total_claimed"] == 800
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "claim/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert_claim_file_details(claim_info[0], data, data_list[2])
    assert_claim_file_details(claim_info[1], data, data_list[3])


@pytest.mark.skip("To be completed later")
def test_lambda_handler_escrow_claim_persistor(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    patch_db.client_basic_info.insert_one(
        {
            "_id": ObjectId("621c4699b29069e5c622ca88"),
            "client_id": "2193",
            "type_of_trust_account": lookup_trust.inserted_id,
        }
    )
    basic_info = patch_db.client_basic_info.find_one({"_id": ObjectId("621c4699b29069e5c622ca88")})
    client_id = basic_info["_id"]
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "claim/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
    }
    data_list = [
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 14},
                    "claim_total": {"GBP": 45920},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = claim_module.lambda_handler(data, None)

    # Then
    claim_info = list(
        patch_db.claims_file_details.find({"client_id": client_id, "booking_ref": data_list[0]["booking_ref"]})
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == 100
    assert trust_fund_info["total_claimed"] == 400
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "claim/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert claim_info[0]["amount"] == 100
    assert claim_info[0]["original_amount"] == 200
    assert claim_info[0]["escrow_multiplier"] == 0.5


def test_lambda_handler_without_currency_code(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow1"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id}
    )
    client_id = basic_info.inserted_id
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "claim/persistor/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "batchNo": 0,
        "batchSize": 2,
        "sftp": False,
        "sftpKey": "",
    }
    data_list = [
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2022-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 14},
                    "claim_total": {"GBP": 45920},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = claim_module.lambda_handler(data, None)

    # Then
    claim_info = patch_db.claims_file_details.find_one(
        {"client_id": client_id, "booking_ref": data_list[0]["booking_ref"]}
    )
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "claim/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert claim_info["currency_code"] == "GBP"


@pytest.mark.skip("To be completed later")
def test_lambda_handler_claim_persistor_we_love_holidays(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "2175", "type_of_trust_account": lookup_trust.inserted_id}
    )
    client_id = basic_info.inserted_id
    patch_db.client_escrow_multiplier.insert_one(
        {"client_id": client_id, "multiplier": 0.75, "date": "2022-08-01", "modified_by": "asd"}
    )
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "claim/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
    }
    data_list = [
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Scanning",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-08-11",
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = claim_module.lambda_handler(data, None)

    # Then
    claim_info = list(
        patch_db.claims_file_details.find({"client_id": client_id, "booking_ref": data_list[0]["booking_ref"]})
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == -100
    assert trust_fund_info["total_claimed"] == 600
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "claim/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert claim_info[0]["amount"] == 150
    assert claim_info[0]["original_amount"] == 200
    assert claim_info[0]["escrow_multiplier"] == 0.75


@pytest.mark.parametrize(
    "client_escrow_multiplier,basic_info_multiplier,expected_multiplier",
    [(None, 0.5, 0.5), (0.7, None, 0.7), (0.5, 0.7, 0.5), (None, None, 0.7)],
)
@pytest.mark.skip("To be completed later")
def test_lambda_handler_claim_persistor_atol_escrow(
    patch_db, s3_client, client_escrow_multiplier, basic_info_multiplier, expected_multiplier
):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Escrow"})
    basic_info = patch_db.client_basic_info.insert_one(
        {
            "client_id": "2105",
            "type_of_trust_account": lookup_trust.inserted_id,
            "escrow_multiplier": basic_info_multiplier,
        }
    )
    client_id = basic_info.inserted_id
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "claim/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
    }
    patch_db.client_escrow_multiplier.insert_one(
        {"client_id": client_id, "multiplier": client_escrow_multiplier, "date": "2022-08-01", "modified_by": "asd"}
    )
    data_list = [
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2021-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 200,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Scanning",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-08-11",
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    result = claim_module.lambda_handler(data, None)

    # Then
    claim_info = list(
        patch_db.claims_file_details.find({"client_id": client_id, "booking_ref": data_list[0]["booking_ref"]})
    )
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] * expected_multiplier
    assert trust_fund_info["total_claimed"] * expected_multiplier
    assert result == {
        "fileId": "12345",
        "clientId": str(client_id),
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "dataKey": "claim/persistor/output/12345.json",
        "sftp": False,
        "sftpKey": "",
    }
    assert claim_info[0]["amount"] * expected_multiplier
    assert claim_info[0]["original_amount"] == 200
    assert claim_info[0]["escrow_multiplier"] == expected_multiplier


@pytest.mark.skip("To be completed later")
def test_lambda_handler_claim_persistor_gtl_protected_deposit(patch_db, s3_client):
    # Given
    lookup_trust = patch_db.lookup_trust_type.insert_one({"name": "ATOL Standard"})
    basic_info = patch_db.client_basic_info.insert_one(
        {"client_id": "1", "type_of_trust_account": lookup_trust.inserted_id}
    )
    client_id = basic_info.inserted_id
    data = {
        "clientId": str(client_id),
        "fileId": "12345",
        "replace": False,
        "dataKey": "claim/12345.json",
        "bucket": os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        "sftp": False,
        "sftpKey": "",
    }
    data_list = [
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2022-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 300,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Giro",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
        {
            "booking_ref": "992638",
            "lead_pax": "Lady Emma Monson",
            "pax_count": 2.0,
            "booking_date": "2022-04-01",
            "departure_date": "2021-10-11",
            "return_date": "2021-10-16",
            "currency_code": "GBP",
            "amount": 300,
            "customer_type": "Direct",
            "bonding": "ATOL",
            "payment_type": "Protected Deposit - Applied",
            "days_to_process": 2.0,
            "total_booking_value": 4030.0,
            "supplier_ref": "A45EX",
            "supplier_names": "Hoseason",
            "payment_date": "2020-12-01",
            "booking_status": "Live",
            "transfer_of_funds": "977957",
        },
    ]
    patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Submitted",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 14},
                    "claim_total": {"GBP": 45920},
                }
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )
    trust_fund_data = {
        "client_id": client_id,
        "booking_ref": "992638",
        "balance": 500,
        "total_in_trust": 500,
        "total_claimed": 0,
        "currency_code": ["GBP"],
        "created_at": "2022-03-03T07:24:46.359Z",
        "updated_at": "2022-03-03T12:57:42.628Z",
    }
    patch_db.trust_fund_v2.insert_one(trust_fund_data)

    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(data_list), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["dataKey"]
    )

    # When
    claim_module.lambda_handler(data, None)

    # Then
    trust_fund_info = patch_db.trust_fund_v2.find_one({"client_id": client_id, "booking_ref": "992638"})
    assert trust_fund_info["balance"] == 200
    assert trust_fund_info["total_claimed"] == 600


def assert_claim_file_details(claim_info, data, claim_data):
    assert claim_info["client_id"] == ObjectId(data["clientId"])
    assert claim_info["file_id"] == data["fileId"]
    assert claim_info["booking_ref"] == claim_data["booking_ref"]
    assert claim_info["lead_pax"] == claim_data["lead_pax"]
    assert claim_info["pax_count"] == claim_data["pax_count"]
    assert claim_info["booking_date"] == claim_data["booking_date"]
    assert claim_info["departure_date"] == claim_data["departure_date"]
    assert claim_info["return_date"] == claim_data["return_date"]
    assert claim_info["currency_code"] == claim_data["currency_code"]
    assert claim_info["amount"] == claim_data["amount"]
    assert claim_info["customer_type"] == claim_data["customer_type"]
    assert claim_info["bonding"] == claim_data["bonding"]
    assert claim_info["payment_type"] == claim_data["payment_type"]
    assert claim_info["days_to_process"] == claim_data["days_to_process"]
    assert claim_info["total_booking_value"] == claim_data["total_booking_value"]
    assert claim_info["supplier_ref"] == claim_data["supplier_ref"]
    assert claim_info["supplier_names"] == claim_data["supplier_names"]
    assert claim_info["payment_date"] == claim_data["payment_date"]
    assert claim_info["booking_status"] == claim_data["booking_status"]
    assert claim_info["transfer_of_funds"] == claim_data["transfer_of_funds"]
