from datetime import datetime
import importlib
import json
import os
from unittest.mock import MagicMock, patch
from freezegun import freeze_time
from mongomock import ObjectId
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_module = importlib.import_module("claim-status-change-lambda.claim-status-change-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_module.db
    claim_module.client.drop_database("db")


@freeze_time("Jan 14th, 2012")
@pytest.mark.parametrize(
    "previous_status, status, is_deleted, claim_input, trust_fund_output, is_claim_transaction_deleted, deleted_flag, transaction_status, expected_amount_changes",
    [
        (
            "Submitted",
            "Cancelled",
            False,
            [
                {
                    "_id": ObjectId("628783cf556173a598ea4354"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": False,
                    "claim_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                    "check": "full-check",
                },
                {
                    "_id": ObjectId("628783cf556173a598ea4355"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": False,
                    "claim_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                },
            ],
            {
                "client_id": "2",
                "booking_ref": "123",
                "balance": 6000,
                "total_in_trust": 1000,
                "total_claimed": 3300,
                "currency_code": ["GBP"],
                "_id": ObjectId("62459c773fa78dea6fab97a7"),
                "created_at": datetime(2012, 1, 14),
                "updated_at": datetime(2012, 1, 14),
            },
            True,
            True,
            "Cancelled",
            {
                "amountChanges": {"GBP": 1000},
                "itemCountChanges": {"GBP": -2},
                "claimTotalChanges": {"GBP": -1000},
                "checksChanges": {"GBP": -1},
                "checkedAmountChanges": {"GBP": -500},
            },
        ),
        (
            "Cancelled",
            "Submitted",
            True,
            [
                {
                    "_id": ObjectId("628783cf556173a598ea4354"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": True,
                    "claim_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                    "check": "quick-check",
                },
                {
                    "_id": ObjectId("628783cf556173a598ea4355"),
                    "client_id": "2",
                    "booking_ref": "123",
                    "deleted": True,
                    "claim_id": ObjectId("621c4699b29069e5c611ca88"),
                    "file_id": "1234",
                    "currency_code": "GBP",
                    "amount": 500,
                },
            ],
            {
                "client_id": "2",
                "booking_ref": "123",
                "balance": 4000,
                "total_in_trust": 1000,
                "total_claimed": 5300,
                "currency_code": ["GBP"],
                "_id": ObjectId("62459c773fa78dea6fab97a7"),
                "created_at": datetime(2012, 1, 14),
                "updated_at": datetime(2012, 1, 14),
            },
            False,
            False,
            "Reinstated",
            {
                "amountChanges": {"GBP": -1000},
                "itemCountChanges": {"GBP": 2},
                "claimTotalChanges": {"GBP": 1000},
                "checksChanges": {"GBP": 1},
                "checkedAmountChanges": {"GBP": 500},
            },
        ),
    ],
)
@pytest.mark.skip("To be completed later")
def test_handle_claim_status_change_update(
    patch_db,
    s3_client,
    previous_status,
    status,
    is_deleted,
    claim_input,
    trust_fund_output,
    is_claim_transaction_deleted,
    deleted_flag,
    transaction_status,
    expected_amount_changes,
):
    # Given
    data = {
        "state": "update",
        "input": {
            "Key": "claim-status-change/1234/test-id/0",
        },
    }
    transactions = {
        "fileId": "1234",
        "clientId": "2",
        "claimId": "621c4699b29069e5c611ca88",
        "isDeleted": is_deleted,
        "createdAt": datetime(2012, 1, 14).isoformat(),
        "fileDate": "2022-04-22",
        "transaction_ids": [str(transaction["_id"]) for transaction in claim_input],
        "bookings": [],
    }
    s3_client.create_bucket(
        Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    s3_client.put_object(
        Body=json.dumps(transactions), Bucket=os.environ["STATE_MACHINE_PAYLOAD_BUCKET"], Key=data["input"]["Key"]
    )

    transaction_id = ObjectId("628783cf556173a598ea4354")
    patch_db.claims_file_details.insert_many(claim_input)
    anomaly_id = patch_db.anomaly_claims.insert_one(
        {
            "transaction_id": transaction_id,
            "file_id": claim_input[0]["file_id"],
            "deleted": claim_input[0]["deleted"],
        }
    ).inserted_id
    trust_funds = patch_db.trust_fund_v2.insert_one(
        {
            "client_id": "2",
            "booking_ref": "123",
            "balance": 5000,
            "total_in_trust": 1000,
            "total_claimed": 4300,
            "currency_code": ["GBP"],
            "created_at": datetime(2012, 1, 14),
            "updated_at": datetime(2012, 1, 14),
        }
    )

    # When
    result = claim_module.lambda_handler(data, None)

    # Then
    trust_fund = patch_db.trust_fund_v2.find_one({"client_id": "2", "booking_ref": "123"})
    claim_file_details = list(patch_db.claims_file_details.find({"client_id": "2"}))
    trust_fund_output["_id"] = trust_funds.inserted_id
    anomaly = patch_db.anomaly_claims.find_one({"_id": anomaly_id})
    assert trust_fund == trust_fund_output
    assert anomaly["deleted"] == is_claim_transaction_deleted
    for trancaction in claim_file_details:
        assert trancaction["deleted"] == deleted_flag
        assert trancaction["status"] == transaction_status
    assert result == expected_amount_changes


@pytest.mark.parametrize(
    "data",
    [
        {
            "state": "final",
            "data": {
                "clientId": ObjectId("621c4699b29069e5c622ca88"),
                "claimId": "628783cf556173a598ea4354",
                "fileId": "12346",
                "newStatus": "Cancelled",
                "createdAt": datetime(2012, 1, 14).isoformat(),
                "fileDate": "2022-02-24",
                "bookings": [],
                "input": [
                    {
                        "amountChanges": {"GBP": -1000},
                        "itemCountChanges": {"GBP": 2},
                        "claimTotalChanges": {"GBP": 1000},
                        "checksChanges": {"GBP": 1},
                        "checkedAmountChanges": {"GBP": 500},
                    }
                ],
            },
        },
        {
            "state": "final",
            "data": {
                "clientId": ObjectId("621c4699b29069e5c622ca88"),
                "claimId": "628783cf556173a598ea4354",
                "fileId": "12346",
                "newStatus": "Cancelled",
                "createdAt": datetime(2012, 1, 14).isoformat(),
                "fileDate": "2022-02-24",
                "bookings": [],
                "input": [],
            },
        },
    ],
)
@patch("helpers.track_opening_closing_balance_changes", MagicMock(return_value=None))
@freeze_time("Jan 14th, 2012")
def test_handle_claim_status_change_final(patch_db, data):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    metadata = patch_db.claims_metadata.insert_one(
        {
            "_id": ObjectId("628783cf556173a598ea4354"),
            "client_id": client_id,
            "status": "Updating Status",
            "claim_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.utcnow(),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "claim_total": {"GBP": 2000},
                },
                {
                    "status": "Updating Status",
                    "file_name": "20220224-All -Claim -All anomalies.xlsx",
                    "submitted_date": datetime.utcnow(),
                    "file_id": "12346",
                    "file_date": "2022-02-24",
                },
            ],
            "created_at": datetime.utcnow(),
        }
    )

    # When
    claim_module.lambda_handler(
        data,
        None,
    )

    # Then
    claim_metadata = patch_db.claims_metadata.find_one(
        {"_id": metadata.inserted_id},
        projection={
            "client_id": 1,
            "status": 1,
            "claim_files.status": 1,
            "claim_files.file_id": 1,
            "claim_files.file_date": 1,
            "claim_files.submitted_date": 1,
            "claim_files.file_name": 1,
            "created_at": 1,
            "updated_at": 1,
        },
    )
    assert claim_metadata == {
        "_id": metadata.inserted_id,
        "client_id": client_id,
        "status": "Cancelled",
        "claim_files": [
            {
                "status": "Cancelled by System",
                "file_name": "20220224-All -Claim -All anomalies.xlsx",
                "submitted_date": datetime.utcnow(),
                "file_id": "12345",
                "file_date": "2022-02-24",
            },
            {
                "status": "Cancelled",
                "file_name": "20220224-All -Claim -All anomalies.xlsx",
                "submitted_date": datetime.utcnow(),
                "file_id": "12346",
                "file_date": "2022-02-24",
            },
        ],
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }
