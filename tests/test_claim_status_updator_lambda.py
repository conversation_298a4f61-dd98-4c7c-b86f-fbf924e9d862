from datetime import datetime
import importlib
from unittest.mock import patch
from freezegun import freeze_time
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    claim_module = importlib.import_module("claim-status-updator-lambda.claim-status-updator-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(claim_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield claim_module.db
    claim_module.client.drop_database("db")


@freeze_time("Mar 1st, 2022")
def test_lambda_handler(patch_db):
    # Given
    basic_info = patch_db.client_basic_info.insert_one({"client_id": "1"})
    client_id = basic_info.inserted_id
    file_id = "12346"
    sftp = False
    sftp_key = ""

    patch_db.client_basic_info.insert_one({"client_id": client_id, "is_editable": True})
    metadata = patch_db.claims_metadata.insert_one(
        {
            "client_id": client_id,
            "status": "Scanning",
            "updated_at": datetime.strptime("2022-02-28T03:01:58.996", "%Y-%m-%dT%H:%M:%S.%f"),
            "claim_files": [
                {
                    "status": "Cancelled by System",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": "12345",
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 4},
                    "claim_total": {"GBP": 2000},
                },
                {
                    "status": "Scanning",
                    "file_name": "********-All -Banking -All anomalies.xlsx",
                    "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                    "file_id": file_id,
                    "file_date": "2022-02-24",
                    "item_count": {"GBP": 3, "USD": 4, "NOK": 1},
                    "claim_total": {"GBP": 300, "USD": 1000, "NOK": 400},
                },
            ],
            "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        }
    )

    # When
    response = claim_module.lambda_handler(
        {"clientId": client_id, "fileId": file_id, "sftp": sftp, "sftpKey": sftp_key}, None
    )

    # Then
    client_basic_info = patch_db.client_basic_info.find_one({"client_id": "1"})
    claim_metadata = patch_db.claims_metadata.find_one({"_id": metadata.inserted_id}, projection={"_id": 0})
    assert client_basic_info["is_editable"] is False
    assert claim_metadata == {
        "client_id": client_id,
        "status": "Submitted",
        "claim_files": [
            {
                "status": "Cancelled by System",
                "file_name": "********-All -Banking -All anomalies.xlsx",
                "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                "file_id": "12345",
                "file_date": "2022-02-24",
                "item_count": {"GBP": 4},
                "claim_total": {"GBP": 2000},
            },
            {
                "status": "Submitted",
                "file_name": "********-All -Banking -All anomalies.xlsx",
                "submitted_date": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
                "file_id": file_id,
                "file_date": "2022-02-24",
                "item_count": {"GBP": 3, "USD": 4, "NOK": 1},
                "claim_total": {"GBP": 300, "USD": 1000, "NOK": 400},
                "notes": "Successfully processed file.",
            },
        ],
        "created_at": datetime.strptime("2022-02-28T03:01:55.690", "%Y-%m-%dT%H:%M:%S.%f"),
        "updated_at": datetime(2022, 3, 1),
    }
    assert response == {
        "clientId": client_id,
        "fileId": "12346",
        "sftp": False,
        "sftpKey": "",
        "msg": "Successfully processed claim file.",
    }
