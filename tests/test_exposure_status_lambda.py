from datetime import datetime
import importlib
from unittest import mock
from bson import ObjectId
from freezegun import freeze_time
import pytest
from custom_mongomock.mongo_client import CustomMongoClient

with mock.patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    exposure_status_lambda = importlib.import_module("dashboard.exposure-status-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(exposure_status_lambda, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield exposure_status_lambda.db
    exposure_status_lambda.client.drop_database("db")


@freeze_time("Dec 22 2022")
def test_lambda_handler(patch_db, monkeypatch):
    # Given
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62ce5983bff9a1862ca13424"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    monkeypatch.setattr(
        patch_db.trust_fund_v2,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": {"client_id": ObjectId("62ce5983bff9a1862ca13424"), "currency_code": "EUR"},
                "Open": 0,
                "OpenAmount": 0,
                "Flown": 1,
                "FlownAmount": 100.0,
                "Vouchered": 0,
                "VoucheredAmount": 0,
                "Rebooked": 0,
                "RebookedAmount": 0,
                "Refunded": 0,
                "RefundedAmount": 0,
                "ExpiredVisa": 0,
                "ExpiredVisaAmount": 0,
                "ExpiredMasterCard": 0,
                "ExpiredMasterCardAmount": 0,
                "ChargeBack": 0,
                "ChargeBackAmount": 0,
            },
            {
                "_id": {"client_id": ObjectId("63a3ecb2c8ab4283540f6923"), "currency_code": "GBP"},
                "Open": 0,
                "OpenAmount": 0,
                "Flown": 60472,
                "FlownAmount": 25963.06,
                "Vouchered": 0,
                "VoucheredAmount": 0,
                "Rebooked": 0,
                "RebookedAmount": 0,
                "Refunded": 0,
                "RefundedAmount": 0,
                "ExpiredVisa": 0,
                "ExpiredVisaAmount": 0,
                "ExpiredMasterCard": 0,
                "ExpiredMasterCardAmount": 0,
                "ChargeBack": 0,
                "ChargeBackAmount": 0,
            },
        ],
    )
    request = {"userId": user_id}

    # When
    exposure_status_lambda.lambda_handler(request, None)

    # Then
    exposure_status = list(patch_db.dashboard_exposure_status.find({}, projection={"_id": 0}))
    assert exposure_status == [
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "currency_code": "EUR",
            "exposure_status": {
                "open": {"count": 0, "amount": 0},
                "flown": {"count": 1, "amount": 100.0},
                "vouchered": {"count": 0, "amount": 0},
                "rebooked": {"count": 0, "amount": 0},
                "refunded": {"count": 0, "amount": 0},
                "expired": {"visa": {"count": 0, "amount": 0}, "master_card": {"count": 0, "amount": 0}},
                "charge_back": {"count": 0, "amount": 0},
            },
            "updated_at": datetime(2022, 12, 22, 0, 0),
        },
        {
            "client_id": ObjectId("63a3ecb2c8ab4283540f6923"),
            "currency_code": "GBP",
            "exposure_status": {
                "open": {"count": 0, "amount": 0},
                "flown": {"count": 60472, "amount": 25963.06},
                "vouchered": {"count": 0, "amount": 0},
                "rebooked": {"count": 0, "amount": 0},
                "refunded": {"count": 0, "amount": 0},
                "expired": {"visa": {"count": 0, "amount": 0}, "master_card": {"count": 0, "amount": 0}},
                "charge_back": {"count": 0, "amount": 0},
            },
            "updated_at": datetime(2022, 12, 22, 0, 0),
        },
    ]


@freeze_time("Dec 22 2022")
def test_lambda_handler_specific_client(patch_db, monkeypatch):
    # Given
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("62ce5983bff9a1862ca13424"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    monkeypatch.setattr(
        patch_db.trust_fund_v2,
        "aggregate",
        lambda *args, **kwargs: [
            {
                "_id": {"client_id": ObjectId("62ce5983bff9a1862ca13424"), "currency_code": "EUR"},
                "Open": 0,
                "OpenAmount": 0,
                "Flown": 1,
                "FlownAmount": 100.0,
                "Vouchered": 0,
                "VoucheredAmount": 0,
                "Rebooked": 0,
                "RebookedAmount": 0,
                "Refunded": 0,
                "RefundedAmount": 0,
                "ExpiredVisa": 0,
                "ExpiredVisaAmount": 0,
                "ExpiredMasterCard": 0,
                "ExpiredMasterCardAmount": 0,
                "ChargeBack": 0,
                "ChargeBackAmount": 0,
            }
        ],
    )
    request = {"clientId": "62ce5983bff9a1862ca13424", "userId": user_id}

    # When
    exposure_status_lambda.lambda_handler(request, None)

    # Then
    exposure_status = list(patch_db.dashboard_exposure_status.find({}, projection={"_id": 0}))
    assert exposure_status == [
        {
            "client_id": ObjectId("62ce5983bff9a1862ca13424"),
            "currency_code": "EUR",
            "exposure_status": {
                "open": {"count": 0, "amount": 0},
                "flown": {"count": 1, "amount": 100.0},
                "vouchered": {"count": 0, "amount": 0},
                "rebooked": {"count": 0, "amount": 0},
                "refunded": {"count": 0, "amount": 0},
                "expired": {"visa": {"count": 0, "amount": 0}, "master_card": {"count": 0, "amount": 0}},
                "charge_back": {"count": 0, "amount": 0},
            },
            "updated_at": datetime(2022, 12, 22, 0, 0),
        },
    ]
