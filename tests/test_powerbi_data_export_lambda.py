import pytest
import json
import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from moto import mock_s3
import boto3

# Import the lambda function and helpers
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from powerbi_data_export_lambda.powerbi_data_export_lambda import (
    get_banking_data, get_claims_data, get_summary_data,
    create_excel_file, upload_to_s3, lambda_handler
)
from helpers.powerbi_transforms import (
    clean_banking_data, clean_claims_data, create_summary_tables,
    add_powerbi_metadata, validate_data_quality
)


@pytest.fixture
def sample_banking_data():
    """Sample banking data for testing."""
    return [
        {
            '_id': '507f1f77bcf86cd799439011',
            'client_id': '507f1f77bcf86cd799439012',
            'client_name': 'Test Client 1',
            'booking_ref': 'BK001',
            'amount': 1000.50,
            'currency_code': 'GBP',
            'entry_type': 'deposit',
            'payment_type': 'card',
            'lead_pax': '<PERSON>',
            'pax_count': 2,
            'booking_date': datetime(2023, 12, 1),
            'departure_date': datetime(2024, 1, 15),
            'return_date': datetime(2024, 1, 22),
            'total_booking_value': 2000.00,
            'bonding': 'ATOL',
            'booking_status': 'confirmed',
            'file_date': '2023-12-01',
            'created_at': datetime(2023, 12, 1, 10, 0, 0),
            'updated_at': datetime(2023, 12, 1, 10, 0, 0),
            'balance': 1000.50,
            'total_in_trust': 1000.50,
            'total_claimed': 0.0
        },
        {
            '_id': '507f1f77bcf86cd799439013',
            'client_id': '507f1f77bcf86cd799439014',
            'client_name': 'Test Client 2',
            'booking_ref': 'BK002',
            'amount': 500.00,
            'currency_code': 'EUR',
            'entry_type': 'deposit',
            'payment_type': 'bank_transfer',
            'lead_pax': 'Jane Smith',
            'pax_count': 1,
            'booking_date': datetime(2023, 12, 2),
            'departure_date': datetime(2024, 2, 10),
            'return_date': datetime(2024, 2, 17),
            'total_booking_value': 800.00,
            'bonding': 'ABTA',
            'booking_status': 'confirmed',
            'file_date': '2023-12-02',
            'created_at': datetime(2023, 12, 2, 14, 30, 0),
            'updated_at': datetime(2023, 12, 2, 14, 30, 0),
            'balance': 500.00,
            'total_in_trust': 500.00,
            'total_claimed': 0.0
        }
    ]


@pytest.fixture
def sample_claims_data():
    """Sample claims data for testing."""
    return [
        {
            '_id': '507f1f77bcf86cd799439015',
            'client_id': '507f1f77bcf86cd799439012',
            'client_name': 'Test Client 1',
            'booking_ref': 'BK001',
            'amount': 200.00,
            'currency_code': 'GBP',
            'payment_type': 'refund',
            'lead_pax': 'John Doe',
            'pax_count': 2,
            'booking_date': datetime(2023, 12, 1),
            'departure_date': datetime(2024, 1, 15),
            'return_date': datetime(2024, 1, 22),
            'total_booking_value': 2000.00,
            'bonding': 'ATOL',
            'booking_status': 'cancelled',
            'file_date': '2023-12-10',
            'created_at': datetime(2023, 12, 10, 9, 0, 0),
            'updated_at': datetime(2023, 12, 10, 9, 0, 0),
            'balance': 800.50,
            'total_in_trust': 1000.50,
            'total_claimed': 200.00
        }
    ]


class TestPowerBITransforms:
    """Test the PowerBI transformation helper functions."""
    
    def test_clean_banking_data(self, sample_banking_data):
        """Test banking data cleaning."""
        df = clean_banking_data(sample_banking_data)
        
        assert not df.empty
        assert len(df) == 2
        assert 'transaction_id' in df.columns
        assert 'client_name' in df.columns
        assert 'transaction_amount' in df.columns
        assert 'transaction_type' in df.columns
        assert df['transaction_type'].iloc[0] == 'Banking'
        
        # Check data types
        assert pd.api.types.is_datetime64_any_dtype(df['booking_date'])
        assert pd.api.types.is_numeric_dtype(df['transaction_amount'])
    
    def test_clean_claims_data(self, sample_claims_data):
        """Test claims data cleaning."""
        df = clean_claims_data(sample_claims_data)
        
        assert not df.empty
        assert len(df) == 1
        assert 'transaction_id' in df.columns
        assert 'client_name' in df.columns
        assert 'claim_amount' in df.columns
        assert 'transaction_type' in df.columns
        assert df['transaction_type'].iloc[0] == 'Claims'
        
        # Check calculated columns
        assert 'claim_percentage' in df.columns
        assert df['claim_percentage'].iloc[0] == 10.0  # 200/2000 * 100
    
    def test_clean_empty_data(self):
        """Test cleaning with empty data."""
        banking_df = clean_banking_data([])
        claims_df = clean_claims_data([])
        
        assert banking_df.empty
        assert claims_df.empty
    
    def test_create_summary_tables(self, sample_banking_data, sample_claims_data):
        """Test summary table creation."""
        banking_df = clean_banking_data(sample_banking_data)
        claims_df = clean_claims_data(sample_claims_data)
        
        summaries = create_summary_tables(banking_df, claims_df)
        
        assert 'client_summary' in summaries
        assert 'currency_summary' in summaries
        assert 'monthly_summary' in summaries
        
        # Check client summary
        client_summary = summaries['client_summary']
        assert len(client_summary) == 2  # Two unique clients
        assert 'net_position' in client_summary.columns
    
    def test_add_powerbi_metadata(self, sample_banking_data):
        """Test PowerBI metadata addition."""
        df = clean_banking_data(sample_banking_data)
        enhanced_df = add_powerbi_metadata(df, 'banking')
        
        assert 'data_refresh_timestamp' in enhanced_df.columns
        assert 'data_source' in enhanced_df.columns
        assert 'data_type' in enhanced_df.columns
        assert enhanced_df['data_type'].iloc[0] == 'banking'
        assert 'created_year' in enhanced_df.columns
        assert 'is_large_transaction' in enhanced_df.columns
    
    def test_validate_data_quality(self, sample_banking_data):
        """Test data quality validation."""
        df = clean_banking_data(sample_banking_data)
        validation = validate_data_quality(df, 'banking')
        
        assert validation['record_count'] == 2
        assert validation['validation_status'] == 'passed'
        assert isinstance(validation['issues'], list)
        assert isinstance(validation['warnings'], list)
    
    def test_validate_empty_data(self):
        """Test validation with empty data."""
        validation = validate_data_quality(pd.DataFrame(), 'banking')
        
        assert validation['record_count'] == 0
        assert validation['validation_status'] == 'empty_dataset'


class TestPowerBIExportLambda:
    """Test the main PowerBI export Lambda function."""
    
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.db')
    def test_get_banking_data(self, mock_db, sample_banking_data):
        """Test banking data retrieval."""
        mock_db.banking_file_details.aggregate.return_value = sample_banking_data
        
        result = get_banking_data(30)
        
        assert len(result) == 2
        assert result[0]['client_name'] == 'Test Client 1'
        mock_db.banking_file_details.aggregate.assert_called_once()
    
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.db')
    def test_get_claims_data(self, mock_db, sample_claims_data):
        """Test claims data retrieval."""
        mock_db.claims_file_details.aggregate.return_value = sample_claims_data
        
        result = get_claims_data(30)
        
        assert len(result) == 1
        assert result[0]['client_name'] == 'Test Client 1'
        mock_db.claims_file_details.aggregate.assert_called_once()
    
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.db')
    def test_get_summary_data(self, mock_db):
        """Test summary data generation."""
        mock_db.banking_file_details.aggregate.return_value = []
        mock_db.claims_file_details.aggregate.return_value = []
        
        result = get_summary_data()
        
        assert 'banking_summary' in result
        assert 'claims_summary' in result
        assert 'generated_at' in result
    
    def test_create_excel_file(self, sample_banking_data, sample_claims_data):
        """Test Excel file creation."""
        summary_data = {
            'banking_summary': [],
            'claims_summary': [],
            'generated_at': datetime.utcnow().isoformat()
        }
        
        filepath = create_excel_file(sample_banking_data, sample_claims_data, summary_data)
        
        assert os.path.exists(filepath)
        assert filepath.endswith('.xlsx')
        
        # Verify Excel file content
        excel_data = pd.read_excel(filepath, sheet_name=None)
        assert 'Banking_Data' in excel_data
        assert 'Claims_Data' in excel_data
        assert 'Export_Metadata' in excel_data
        
        # Clean up
        os.remove(filepath)
    
    @mock_s3
    def test_upload_to_s3(self):
        """Test S3 upload functionality."""
        # Create mock S3 bucket
        s3 = boto3.client('s3', region_name='us-east-1')
        bucket_name = 'test-powerbi-bucket'
        s3.create_bucket(Bucket=bucket_name)
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False) as f:
            f.write('test content')
            temp_filepath = f.name
        
        try:
            s3_key = upload_to_s3(temp_filepath, bucket_name)
            
            assert s3_key.startswith('powerbi-exports/')
            
            # Verify file was uploaded
            response = s3.head_object(Bucket=bucket_name, Key=s3_key)
            assert response['ResponseMetadata']['HTTPStatusCode'] == 200
            
        finally:
            os.remove(temp_filepath)
    
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.get_banking_data')
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.get_claims_data')
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.get_summary_data')
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.upload_to_s3')
    def test_lambda_handler_success(self, mock_upload, mock_summary, mock_claims, mock_banking,
                                   sample_banking_data, sample_claims_data):
        """Test successful Lambda execution."""
        # Setup mocks
        mock_banking.return_value = sample_banking_data
        mock_claims.return_value = sample_claims_data
        mock_summary.return_value = {
            'banking_summary': [],
            'claims_summary': [],
            'generated_at': datetime.utcnow().isoformat()
        }
        mock_upload.return_value = 'powerbi-exports/test_file.xlsx'
        
        # Test event
        event = {
            'days_back': 7,
            'bucket': 'test-bucket'
        }
        
        response = lambda_handler(event, {})
        
        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['message'] == 'PowerBI export completed successfully'
        assert body['banking_records'] == 2
        assert body['claims_records'] == 1
    
    @patch('powerbi_data_export_lambda.powerbi_data_export_lambda.get_banking_data')
    def test_lambda_handler_error(self, mock_banking):
        """Test Lambda error handling."""
        mock_banking.side_effect = Exception('Database connection failed')
        
        response = lambda_handler({}, {})
        
        assert response['statusCode'] == 500
        body = json.loads(response['body'])
        assert 'error' in body
        assert body['message'] == 'PowerBI export failed'


if __name__ == '__main__':
    pytest.main([__file__])
