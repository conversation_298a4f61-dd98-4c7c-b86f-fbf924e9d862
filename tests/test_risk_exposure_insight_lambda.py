from datetime import datetime
from unittest import mock

from freezegun import freeze_time
from mongomock import ObjectId
import pytest
import importlib
from custom_mongomock.mongo_client import CustomMongoClient

with mock.patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")):
    risk_module = importlib.import_module("risk-exposure-insight-lambda.risk-exposure-insight-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(risk_module, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield risk_module.db
    risk_module.client.drop_database("db")


@freeze_time("Jan 14th, 2012")
def test_lambda_handler_risk_exposure1(patch_db, s3_client, monkeypatch):
    # Given
    patch_db.claims_file_details.insert_many(
        [
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
            {
                "booking_ref": "1234",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
            {
                "booking_ref": "12345",
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "EUR",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ]
    )

    # When
    risk_module.lambda_handler(
        {},
        None,
    )

    # Then
    risk_data_eur = patch_db.dashboard_risk_exposure.find_one({"currency_code": "EUR"})
    risk_data_gbp = patch_db.dashboard_risk_exposure.find_one({"currency_code": "GBP"})

    assert risk_data_eur["claim_amount"] == 5000
    assert risk_data_gbp["claim_amount"] == 10000


@freeze_time("Jan 14th, 2012")
def test_lambda_handler_risk_exposure_if_user_id(patch_db, s3_client, monkeypatch):
    # Given
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("620a8020b32a49e8c133a2da"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
                "currency_code": "EUR",
                "date": datetime(2012, 1, 14, 0, 0),
                "_id": ObjectId("4f10c580c404a1aa1c7a513a"),
                "created_at": datetime(2012, 1, 14, 0, 0),
                "claim_amount": 5000,
                "updated_at": datetime(2012, 1, 14, 0, 0),
            },
            {
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "currency_code": "GBP",
                "date": datetime(2012, 1, 14, 0, 0),
                "_id": ObjectId("4f10c580c404a1aa1c7a513b"),
                "created_at": datetime(2012, 1, 14, 0, 0),
                "claim_amount": 10000,
                "updated_at": datetime(2012, 1, 14, 0, 0),
            },
        ]
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-14",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8d133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-14",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
            {
                "booking_ref": "1234",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-14",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
            {
                "booking_ref": "12345",
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "EUR",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-14",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
            {
                "booking_ref": "12345",
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "EUR",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2d3"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-14",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ]
    )

    # When
    risk_module.lambda_handler(
        {"userId": user_id},
        None,
    )

    # Then

    risk_data_eur = patch_db.dashboard_risk_exposure.find_one({"currency_code": "EUR"})
    risk_data_gbp = patch_db.dashboard_risk_exposure.find_one({"currency_code": "GBP"})

    assert risk_data_eur["claim_amount"] == 5000
    assert risk_data_gbp["claim_amount"] == 15000


@freeze_time("Jan 14th, 2012")
def test_lambda_handler_risk_exposure_if_cancelled(patch_db, s3_client, monkeypatch):
    # Given
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("620a8020b32a49e8c133a2da"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "currency_code": "GBP",
                "date": datetime(2012, 1, 14, 0, 0),
                "_id": ObjectId("4f10c580c404a1aa1c7a513b"),
                "created_at": datetime(2012, 1, 14, 0, 0),
                "claim_amount": 10000,
                "updated_at": datetime(2012, 1, 14, 0, 0),
            },
        ]
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": True,
            },
            {
                "booking_ref": "1234",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": True,
            },
        ]
    )

    # When
    risk_module.lambda_handler(
        {"userId": user_id},
        None,
    )

    # Then
    risk_data_gbp = patch_db.dashboard_risk_exposure.find_one({"currency_code": "GBP"})

    assert risk_data_gbp is None


@freeze_time("Jan 14th, 2012")
def test_lambda_handler_risk_exposure_if_one_currency_cancelled(patch_db, s3_client, monkeypatch):
    # Given
    user_id = "ee4006bd-23ab-499a-9adc-cf7c6f195c48"
    patch_db.user.insert_one(
        {
            "user_id": "ee4006bd-23ab-499a-9adc-cf7c6f195c48",
            "clients": [ObjectId("620a8020b32a49e8c133a2da"), ObjectId("63a3ecb2c8ab4283540f6923")],
        }
    )
    patch_db.dashboard_risk_exposure.insert_many(
        [
            {
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "currency_code": "GBP",
                "date": datetime(2012, 1, 14, 0, 0),
                "_id": ObjectId("4f10c580c404a1aa1c7a513b"),
                "created_at": datetime(2012, 1, 14, 0, 0),
                "claim_amount": 10000,
                "updated_at": datetime(2012, 1, 14, 0, 0),
            },
            {
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "currency_code": "EUR",
                "date": datetime(2012, 1, 14, 0, 0),
                "_id": ObjectId("4f10c580c404a1aa1c7a513d"),
                "created_at": datetime(2012, 1, 14, 0, 0),
                "claim_amount": 10000,
                "updated_at": datetime(2012, 1, 14, 0, 0),
            },
        ]
    )
    patch_db.claims_file_details.insert_many(
        [
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": True,
            },
            {
                "booking_ref": "1234",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": True,
            },
            {
                "booking_ref": "123",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "GBP",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": True,
            },
            {
                "booking_ref": "1234",
                "duplicates": 2,
                "status": "string",
                "claims_id": ObjectId("620a8020b32a49e8c133a2da"),
                "full": "string",
                "quick": "string",
                "claim_count": 3,
                "currency_code": "EUR",
                "amount": 5000,
                "client_id": ObjectId("620a8020b32a49e8c133a2da"),
                "pax_count": 5,
                "departure_date": "2020-07-11",
                "return_date": "2020-08-13",
                "file_id": "67555",
                "total_booking_value": 500,
                "deleted": False,
            },
        ]
    )

    # When
    risk_module.lambda_handler(
        {"userId": user_id},
        None,
    )

    # Then
    risk_data_eur = patch_db.dashboard_risk_exposure.find_one({"currency_code": "EUR"})
    risk_data_gbp = patch_db.dashboard_risk_exposure.find_one({"currency_code": "GBP"})

    assert risk_data_gbp is None

    assert risk_data_eur["claim_amount"] == 5000
