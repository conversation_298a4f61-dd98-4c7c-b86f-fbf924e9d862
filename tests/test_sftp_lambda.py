import importlib
from unittest.mock import patch, MagicMock
import pytest
from custom_mongomock.mongo_client import CustomMongoClient
import os
import tempfile
import xlsxwriter
from mongomock import ObjectId

TEMP_DIR = tempfile.gettempdir()

with patch("pymongo.MongoClient", return_value=CustomMongoClient("mongodb://test/db")), patch(
    "helpers.secret_manager_handler.get_secret", MagicMock(return_value=("abc", "password", "mongodb://test/db"))
):
    sftp = importlib.import_module("sftp-lambda.sftp-lambda", None)


@pytest.fixture(scope="function")
def patch_db(monkeypatch):
    monkeypatch.setattr(sftp, "db", CustomMongoClient("mongodb://test/db").get_database())
    yield sftp.db
    sftp.client.drop_database("db")


@pytest.mark.parametrize(
    "sftp_location, file_path",
    [
        (["/sftp_folder/", "/sftp_folder4/sftp_folder3/"], "sftp_folder/********-banking.csv"),
        ("/", "********-banking.csv"),
        (["/sftp_folder1/sftp_folder2/"], "sftp_folder1/sftp_folder2/********-banking.csv"),
    ],
)
def test_sftp_lambda_handler(requests_mock, patch_db, s3_client, sftp_location, file_path):
    # Given
    patch_db.client_basic_info.insert_one({"_id": ObjectId("62188fcb7ec8edb86189c4ff"), "sftp_location": sftp_location})
    s3_client.create_bucket(
        Bucket=os.environ["SFTP_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    data = {
        "dataKey": file_path,
        "bucket": os.environ["SFTP_BUCKET"],
    }
    workbook = xlsxwriter.Workbook(f"{TEMP_DIR}/********-banking.csv")
    workbook.close()

    s3_client.upload_file(f"{TEMP_DIR}/********-banking.csv", Bucket=os.environ["SFTP_BUCKET"], Key=data["dataKey"])

    requests_mock.post(
        f"{os.environ['URL']}/login", json={"authenticationResult": {"AccessToken": "eyJraWQiOiJqdWdrYTFmWGNkbEJW"}}
    )

    event = {
        "Records": [
            {
                "s3": {
                    "s3SchemaVersion": "1.0",
                    "bucket": {"name": "test_bucket", "arn": "arn:aws:s3:::test_bucket"},
                    "object": {"key": file_path, "eTag": "0123456789abcdef0123456789abcdef"},
                }
            }
        ]
    }

    requests_mock.get(
        f"{os.environ['URL']}/banking/upload/presigned-url",
        json={"fileId": "123", "presignedUrl": "https://test-banking-files-dev.s3.amazonaws.com/123"},
    )
    test_presigned_url = os.environ["PRESIGNED_URL"].strip('"')
    requests_mock.put(test_presigned_url, status_code=200)
    requests_mock.post(f"{os.environ['URL']}/banking/upload", status_code=201)
    requests_mock.get(f"{os.environ['URL']}/logout")

    # When
    response = sftp.lambda_handler(event, None)

    # Then
    assert response is None


def test_sftp_invalid_file_format(requests_mock, patch_db, s3_client):
    # Given
    patch_db.client_basic_info.insert_one(
        {"_id": ObjectId("62188fcb7ec8edb86189c4ff"), "sftp_location": ["/sftp_folder/"]}
    )

    s3_client.create_bucket(
        Bucket=os.environ["SFTP_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    data = {
        "dataKey": "sftp_folder/********-banking.png",
        "bucket": os.environ["SFTP_BUCKET"],
    }
    workbook = xlsxwriter.Workbook(f"{TEMP_DIR}/********-banking.png")
    workbook.close()

    s3_client.upload_file(f"{TEMP_DIR}/********-banking.png", Bucket=os.environ["SFTP_BUCKET"], Key=data["dataKey"])

    requests_mock.post(
        f"{os.environ['URL']}/login", json={"authenticationResult": {"AccessToken": "eyJraWQiOiJqdWdrYTFmWGNkbEJW"}}
    )

    event = {
        "Records": [
            {
                "s3": {
                    "s3SchemaVersion": "1.0",
                    "configurationId": "testConfigRule",
                    "bucket": {"name": "test_bucket", "arn": "arn:aws:s3:::test_bucket"},
                    "object": {
                        "key": "sftp_folder/********-banking.png",
                        "eTag": "0123456789abcdef0123456789abcdef",
                    },
                }
            }
        ]
    }

    requests_mock.get(f"{os.environ['URL']}/logout")
    # When
    with pytest.raises(Exception) as excinfo:
        sftp.lambda_handler(event, None)

    # Then
    assert str(excinfo.value) == "File format not supported, use xls, xlsx or csv files"


def test_sftp_client_not_found_for_given_folder(requests_mock, patch_db, s3_client):
    # Given
    patch_db.client_basic_info.insert_one({"_id": ObjectId("62188fcb7ec8edb86189c4ff")})

    s3_client.create_bucket(
        Bucket=os.environ["SFTP_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    data = {
        "dataKey": "sftp_folder/********-banking.csv",
        "bucket": os.environ["SFTP_BUCKET"],
    }
    workbook = xlsxwriter.Workbook(f"{TEMP_DIR}/********-banking.csv")
    workbook.close()

    s3_client.upload_file(f"{TEMP_DIR}/********-banking.csv", Bucket=os.environ["SFTP_BUCKET"], Key=data["dataKey"])

    requests_mock.post(
        f"{os.environ['URL']}/login", json={"authenticationResult": {"AccessToken": "eyJraWQiOiJqdWdrYTFmWGNkbEJW"}}
    )

    event = {
        "Records": [
            {
                "s3": {
                    "s3SchemaVersion": "1.0",
                    "configurationId": "testConfigRule",
                    "bucket": {"name": "test_bucket", "arn": "arn:aws:s3:::test_bucket"},
                    "object": {
                        "key": "sftp_folder/********-banking.csv",
                        "eTag": "0123456789abcdef0123456789abcdef",
                    },
                }
            }
        ]
    }

    requests_mock.get(f"{os.environ['URL']}/logout")
    # When
    with pytest.raises(Exception) as excinfo:
        sftp.lambda_handler(event, None)

    # Then
    assert str(excinfo.value) == "client not found for the given folder location"


def test_sftp_Invalid_file_name(requests_mock, patch_db, s3_client):
    # Given
    patch_db.client_basic_info.insert_one(
        {"_id": ObjectId("62188fcb7ec8edb86189c4ff"), "sftp_location": "/sftp_folder/"}
    )

    s3_client.create_bucket(
        Bucket=os.environ["SFTP_BUCKET"],
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"},
    )
    data = {
        "dataKey": "sftp_folder/********-hy.xlsx",
        "bucket": os.environ["SFTP_BUCKET"],
    }
    workbook = xlsxwriter.Workbook(f"{TEMP_DIR}/********-hy.xlsx")
    workbook.close()

    s3_client.upload_file(f"{TEMP_DIR}/********-hy.xlsx", Bucket=os.environ["SFTP_BUCKET"], Key=data["dataKey"])

    requests_mock.post(
        f"{os.environ['URL']}/login", json={"authenticationResult": {"AccessToken": "eyJraWQiOiJqdWdrYTFmWGNkbEJW"}}
    )

    event = {
        "Records": [
            {
                "s3": {
                    "s3SchemaVersion": "1.0",
                    "configurationId": "testConfigRule",
                    "bucket": {"name": "test_bucket", "arn": "arn:aws:s3:::test_bucket"},
                    "object": {
                        "key": "sftp_folder/********-hy.xlsx",
                        "eTag": "0123456789abcdef0123456789abcdef",
                    },
                }
            }
        ]
    }

    requests_mock.get(f"{os.environ['URL']}/logout")
    # When
    with pytest.raises(Exception) as excinfo:
        sftp.lambda_handler(event, None)

    # Then
    assert str(excinfo.value) == "Invalid file name"
