import os
from datetime import datetime
import pymongo
from pymongo import MongoClient
from helpers.secret_manager_handler import get_secret
from bson import ObjectId
import logging

url = os.environ["URL"]
details = get_secret("eu-west-2", False, True)
user_name, password, mongo_db = details
client = MongoClient(os.environ.get("DATABASE_URI") or get_secret("eu-west-2"))
db = client.get_database()
if logging.getLogger().hasHandlers():
    logging.getLogger().setLevel(logging.INFO)
else:
    logging.basicConfig(level=logging.INFO)

client_id = os.environ.get("WLH_NEW")


def lambda_handler(event, context):
    latest_banking_file = db.banking_metadata.find_one(
        {
            "client_id": ObjectId(client_id),
            "status": {"$nin": ["Cancelled", "Cancelled by System", "Scanning"]},
        },
        sort=[("updated_at", pymongo.DESCENDING)],
    )
    banking_id = latest_banking_file.get("_id")
    if latest_banking_file.get("banking_files"):
        file_id = latest_banking_file["banking_files"][0]["file_id"]

    pipeline1 = [
        {
            "$match": {
                "client_id": ObjectId(client_id),
                "banking_id": banking_id,
                "deleted": False,
            }
        },
        {
            "$group": {
                "_id": {"currency_code": "$currency_code", "booking_ref": "$booking_ref"},
                "total_gross_amount": {"$first": "$funds_collected"},
                "element_amount_sum": {
                    "$sum": {
                        "$cond": {
                            "if": {"$in": ["$element", ["Flight", "Insurance", "FlightCorpCard"]]},
                            "then": "$amount",
                            "else": 0,
                        }
                    }
                },
            }
        },
        {
            "$group": {
                "_id": "$_id.currency_code",
                "total_gross_amount": {"$sum": "$total_gross_amount"},
                "total_element_amount_sum": {"$sum": "$element_amount_sum"},
            }
        },
        {
            "$project": {
                "_id": 1,
                "total_element_amount_sum": 1,
                "total_gross_amount": 1,
                "total_net_amount": {"$subtract": ["$total_gross_amount", "$total_element_amount_sum"]},
            }
        },
    ]

    result1 = list(db.banking_file_details.aggregate(pipeline1, allowDiskUse=True))
    gross_amount = 0
    initial_net_amount = 0
    final_net_amount = 0
    if result1:
        gross_amount = round(result1[0]["total_gross_amount"], 2)
        initial_net_amount = result1[0]["total_net_amount"]

    pipeline2 = [
        {
            "$match": {
                "client_id": ObjectId(client_id),
                "banking_id": banking_id,
                "deleted": False,
            }
        },
        {
            "$facet": {
                "cancellation_net_amount": [
                    {"$match": {"booking_status": "Cancellation"}},
                    {
                        "$group": {
                            "_id": {"booking_ref": "$booking_ref", "currency_code": "$currency_code"},
                            "funds_collected": {"$first": "$funds_collected"},
                            "total_amount": {
                                "$sum": {
                                    "$cond": {
                                        "if": {"$in": ["$element", ["Flight", "Insurance", "FlightCorpCard"]]},
                                        "then": "$amount",
                                        "else": 0,
                                    }
                                }
                            },
                        }
                    },
                    {
                        "$group": {
                            "_id": "$_id.currency_code",
                            "total_funds_collected": {"$sum": "$funds_collected"},
                            "total_element_amount_sum": {"$sum": "$total_amount"},
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "cancellation_net_amount": {
                                "$subtract": ["$total_funds_collected", "$total_element_amount_sum"]
                            },
                        }
                    },
                ],
                "cancellation_refund_net_amount": [
                    {
                        "$match": {
                            "booking_status": "Cancellation_Refund",
                            "element": {"$in": ["Flight", "Insurance", "FlightCorpCard"]},
                        }
                    },
                    {
                        "$group": {
                            "_id": {"booking_ref": "$booking_ref", "currency_code": "$currency_code"},
                            "funds_collected": {"$first": "$funds_collected"},
                            "total_amount": {"$sum": "$amount"},
                        }
                    },
                    {"$project": {"_id": 0, "net_amount": {"$subtract": ["$funds_collected", "$total_amount"]}}},
                    {"$match": {"net_amount": {"$lt": 0}}},
                    {
                        "$group": {"_id": None, "cancellation_refund_net_amount": {"$sum": "$net_amount"}},
                    },
                ],
                "negative_active_net_amount": [
                    {
                        "$match": {
                            "booking_status": "Active",
                            "element": {"$in": ["Flight", "Insurance", "FlightCorpCard"]},
                        }
                    },
                    {
                        "$group": {
                            "_id": {"booking_ref": "$booking_ref", "currency_code": "$currency_code"},
                            "funds_collected": {"$first": "$funds_collected"},
                            "total_amount": {"$sum": "$amount"},
                        }
                    },
                    {"$project": {"_id": 0, "net_amount": {"$subtract": ["$funds_collected", "$total_amount"]}}},
                    {"$match": {"net_amount": {"$lt": 0}}},
                    {
                        "$group": {"_id": None, "total_negative_net_amount": {"$sum": "$net_amount"}},
                    },
                ],
            }
        },
    ]

    result2 = list(db.banking_file_details.aggregate(pipeline2, allowDiskUse=True))

    if result2:
        final_net_amount = initial_net_amount
        if result2[0].get("cancellation_net_amount"):
            cancelled_net_amount = result2[0]["cancellation_net_amount"]
            final_net_amount -= abs(cancelled_net_amount[0]["cancellation_net_amount"])
        if result2[0].get("cancellation_refund_net_amount"):
            cancellation_refund_net_amount = result2[0]["cancellation_refund_net_amount"]
            final_net_amount += abs(cancellation_refund_net_amount[0]["cancellation_refund_net_amount"])
        if result2[0].get("negative_active_net_amount"):
            negative_active_net_amount = result2[0]["negative_active_net_amount"]
            final_net_amount += abs(negative_active_net_amount[0]["total_negative_net_amount"])
    final_net_amount = round(final_net_amount, 2)
    db.banking_metadata.update_one(
        {"banking_files": {"$elemMatch": {"file_id": file_id}}, "client_id": ObjectId(client_id)},
        {
            "$set": {
                "banking_files.$.gross_amount": {"GBP": gross_amount},
                "banking_files.$.net_amount": {"GBP": final_net_amount},
                "updated_at": datetime.utcnow(),
            }
        },
    )
